INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS84B1TXQK07M1SNPQYYXA4C', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 01:57:36 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 01:57:36 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS87NW5FF54VGQ486XN8S8W0', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 02:55:56 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 02:55:56 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS8A1Z9C8NTP7WT4YQHYG3X0', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 03:37:30 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 03:37:30 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS8FKZ9CEAC0JER0JZ9ZFDZ7', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 05:14:42 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 05:14:42 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS8FM5W8B7BK5TBM6S850DQR', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 05:14:49 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 05:14:49 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS8PJMQA81CGK9MFJ2QCWA9A', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 07:16:19 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 07:16:19 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS8VE3YKMW1VDSGH0270M2QY', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 08:41:13 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 08:41:13 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS8W7CK2JYDKXFN3Y3V1VXNR', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 08:55:01 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 08:55:01 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS9342PRYKR9AZ9NSBTAYB8R', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 10:55:33 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 10:55:33 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS934680M5HGBC3HGK3M22GX', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 10:55:37 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 10:55:37 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS934A5WGP3T2MP94WWQWMQA', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 10:55:41 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 10:55:41 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS94PPT8E1QWHDFVAZ5P01PZ', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 11:23:12 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 11:23:12 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS958XJZ8D5DMAS0F8T4DRC9', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 11:33:09 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 11:33:09 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS9593HCQKWFE3CHGZ5791X8', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 11:33:15 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 11:33:15 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS95G4T0DSJN7J6VKWGGN6FQ', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 11:37:05 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 11:37:05 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS95QKYJFCHYWGEAMKNSVCVN', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 11:41:10 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 11:41:10 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS96QQCG19VW4ZQ28KXGA90N', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 11:58:42 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 11:58:42 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS96QWCDHP930ZRX19BD1VHQ', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 11:58:48 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 11:58:48 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS96YY1JH46BAXQXZHR2DY3P', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 12:02:39 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 12:02:39 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS983QRR5SFQJQRVAPCHY3BY', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 12:22:45 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 12:22:45 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS98PPKJ8M1V0CQYJGFRFKFT', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 12:33:06 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 12:33:06 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS98SAFHSV7WBFZQ9DDF3S86', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 12:34:32 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 12:34:32 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS99CQ360DDAF13D4JNAZZ6M', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 12:45:07 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 12:45:07 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS9HSRBE25QFWQN15WYGFQE0', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 15:12:03 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 15:12:03 GMT+0300 (Eastern European Summer Time)');
INSERT OR REPLACE INTO carts (id, customer_id, email, currency_code, region_id, billing_address, shipping_address, metadata, created_at, updated_at) 
                   VALUES ('cart_01JS9YXZ1NH5Y7BAN830CK84ND', null, null, 'ron', 'reg_01JQY1KMRCG8ABC5CSBMC4C9GH', null, '{"first_name":null,"last_name":null,"company":null,"address_1":null,"address_2":null,"city":null,"province":null,"postal_code":null,"country_code":"ro","phone":null}', '{}', 'Sun Apr 20 2025 19:01:33 GMT+0300 (Eastern European Summer Time)', 'Sun Apr 20 2025 19:01:33 GMT+0300 (Eastern European Summer Time)');