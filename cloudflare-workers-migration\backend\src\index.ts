import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';

// Import route handlers
import { storeRoutes } from './routes/store';
import simplifiedAdminRoutes from './routes/admin/simplified';
import { authRoutes } from './routes/auth';
import { webhookRoutes } from './routes/webhooks';

// Define environment bindings
type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
  ENVIRONMENT: string;
  CACHE: KVNamespace;
  SESSIONS: KVNamespace;
  ASSETS: R2Bucket;
  UPLOADS: R2Bucket;
};

// Create the main Hono app
const app = new Hono<{ Bindings: Bindings }>();

// Global middleware
app.use('*', logger());
app.use('*', prettyJSON());

// CORS configuration
app.use('*', cors({
  origin: (origin) => {
    // Allow requests from your frontend domains
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'https://handmadein.ro',
      'https://www.handmadein.ro',
      'https://admin.handmadein.ro',
      'https://staging.handmadein.ro',
      'https://admin-staging.handmadein.ro'
    ];
    console.log('CORS check - Origin:', origin, 'Allowed:', allowedOrigins.includes(origin) || origin?.endsWith('.handmadein.ro'));
    return allowedOrigins.includes(origin) || origin?.endsWith('.handmadein.ro') ? origin : null;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Accept-Language',
    'X-Locale'
  ],
  credentials: true,
}));

// Health check endpoint
app.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: c.env.ENVIRONMENT,
  });
});

// Debug endpoint to check environment variables (remove in production)
app.get('/debug/env', (c) => {
  return c.json({
    environment: c.env.ENVIRONMENT,
    hasJwtSecret: !!c.env.JWT_SECRET,
    jwtSecretLength: c.env.JWT_SECRET?.length || 0,
    frontendUrl: c.env.FRONTEND_URL,
    adminUrl: c.env.ADMIN_URL,
  });
});

// Debug endpoint to test JWT token parsing (remove in production)
app.get('/debug/token', async (c) => {
  try {
    const authHeader = c.req.header('Authorization');
    
    if (!authHeader) {
      return c.json({
        error: 'No Authorization header',
        headers: Object.fromEntries(c.req.raw.headers),
      });
    }

    if (!authHeader.startsWith('Bearer ')) {
      return c.json({
        error: 'Authorization header does not start with Bearer',
        authHeader: authHeader,
      });
    }

    const token = authHeader.substring(7);
    const tokenParts = token.split('.');
    
    return c.json({
      authHeader: authHeader,
      tokenLength: token.length,
      tokenParts: tokenParts.length,
      tokenPreview: token.substring(0, 50) + '...',
      hasJwtSecret: !!c.env.JWT_SECRET,
    });
  } catch (error) {
    return c.json({
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
  }
});

// API routes
app.route('/store', storeRoutes);
app.route('/admin', simplifiedAdminRoutes);
app.route('/auth', authRoutes);
app.route('/webhooks', webhookRoutes);

// 404 handler
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Not Found',
    message: 'The requested endpoint does not exist',
  }, 404);
});

// Error handler
app.onError((err, c) => {
  console.error('Unhandled error:', err);
  
  return c.json({
    success: false,
    error: 'Internal Server Error',
    message: c.env.ENVIRONMENT === 'development' ? err.message : 'Something went wrong',
  }, 500);
});

export default app; 