import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import { 
  ChevronLeftIcon,
  TruckIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ArchiveBoxIcon,
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';

interface ShippingProfile {
  id: string;
  name: string;
  type: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

const SettingsShipping: React.FC = () => {
  const queryClient = useQueryClient();
  const [isAddingProfile, setIsAddingProfile] = useState(false);
  const [editingProfile, setEditingProfile] = useState<ShippingProfile | null>(null);
  const [newProfile, setNewProfile] = useState({
    name: '',
    type: 'default',
    description: '',
  });

  // Fetch shipping data
  const { data: shippingData, isLoading } = useQuery({
    queryKey: ['admin-shipping'],
    queryFn: () => api.get('/admin/api/shipping-zones').then(res => res.data),
  });

  // Create shipping profile mutation
  const createProfileMutation = useMutation({
    mutationFn: (data: any) => api.post('/admin/api/shipping-zones', data),
    onSuccess: () => {
      toast.success('Shipping profile created successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-shipping'] });
      setIsAddingProfile(false);
      setNewProfile({ name: '', type: 'default', description: '' });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create shipping profile');
    },
  });

  // Update shipping profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      api.put(`/admin/api/shipping-zones/${id}`, data),
    onSuccess: () => {
      toast.success('Shipping profile updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-shipping'] });
      setEditingProfile(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update shipping profile');
    },
  });

  // Delete shipping profile mutation
  const deleteProfileMutation = useMutation({
    mutationFn: (profileId: string) => api.delete(`/admin/api/shipping-zones/${profileId}`),
    onSuccess: () => {
      toast.success('Shipping profile deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-shipping'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete shipping profile');
    },
  });

  const handleCreateProfile = () => {
    if (!newProfile.name.trim()) {
      toast.error('Profile name is required');
      return;
    }

    createProfileMutation.mutate({
      name: newProfile.name,
      type: newProfile.type,
      metadata: { description: newProfile.description },
    });
  };

  const handleUpdateProfile = (profile: ShippingProfile) => {
    updateProfileMutation.mutate({
      id: profile.id,
      data: {
        name: profile.name,
        type: profile.type,
        metadata: profile.metadata,
      },
    });
  };

  const handleDeleteProfile = (profileId: string) => {
    if (window.confirm('Are you sure you want to delete this shipping profile?')) {
      deleteProfileMutation.mutate(profileId);
    }
  };

  const getProfileTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      default: 'Default',
      express: 'Express',
      overnight: 'Overnight',
      economy: 'Economy',
      pickup: 'In-Store Pickup',
    };
    return types[type] || type;
  };

  const getProfileTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      default: 'bg-blue-100 text-blue-800',
      express: 'bg-red-100 text-red-800',
      overnight: 'bg-purple-100 text-purple-800',
      economy: 'bg-green-100 text-green-800',
      pickup: 'bg-yellow-100 text-yellow-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const shippingProfiles = shippingData?.data?.shippingProfiles || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/admin/settings"
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Settings
          </Link>
          <span className="text-gray-400">/</span>
          <div className="flex items-center space-x-2">
            <TruckIcon className="h-5 w-5 text-orange-600" />
            <h1 className="text-2xl font-semibold text-gray-900">Shipping</h1>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Link
            to="/settings/shipping-pricing"
            className="bg-green-600 text-white px-4 py-2 rounded-md text-sm hover:bg-green-700 transition-colors duration-200 flex items-center space-x-2"
          >
            <CurrencyDollarIcon className="h-4 w-4" />
            <span>Manage Pricing</span>
          </Link>
          <button
            onClick={() => setIsAddingProfile(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Add Shipping Profile</span>
          </button>
        </div>
      </div>

      {/* Description */}
      <div className="bg-orange-50 border border-orange-200 rounded-md p-4">
        <p className="text-orange-800 text-sm">
          Configure shipping profiles to define different shipping methods, costs, and delivery options for your customers.
        </p>
      </div>

      {/* Add Profile Form */}
      {isAddingProfile && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Shipping Profile</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Profile Name *
              </label>
              <input
                type="text"
                value={newProfile.name}
                onChange={(e) => setNewProfile({ ...newProfile, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Standard Shipping, Express Delivery"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Shipping Type
              </label>
              <select
                value={newProfile.type}
                onChange={(e) => setNewProfile({ ...newProfile, type: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="default">Default</option>
                <option value="express">Express</option>
                <option value="overnight">Overnight</option>
                <option value="economy">Economy</option>
                <option value="pickup">In-Store Pickup</option>
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={newProfile.description}
                onChange={(e) => setNewProfile({ ...newProfile, description: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Optional description for this shipping profile"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setIsAddingProfile(false)}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateProfile}
              disabled={createProfileMutation.isPending}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200"
            >
              {createProfileMutation.isPending ? 'Creating...' : 'Create Profile'}
            </button>
          </div>
        </div>
      )}

      {/* Shipping Profiles List */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Shipping Profiles</h3>
          <p className="text-sm text-gray-600 mt-1">
            {shippingProfiles.length} profile{shippingProfiles.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {shippingProfiles.length === 0 ? (
          <div className="p-6 text-center">
            <TruckIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No shipping profiles configured</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first shipping profile.
            </p>
            <button
              onClick={() => setIsAddingProfile(true)}
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200"
            >
              Add Shipping Profile
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {shippingProfiles.map((profile: ShippingProfile) => {
              const isEditing = editingProfile?.id === profile.id;

              return (
                <div key={profile.id} className="p-6">
                  {isEditing ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Profile Name
                          </label>
                          <input
                            type="text"
                            value={editingProfile.name}
                            onChange={(e) => setEditingProfile({ ...editingProfile, name: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Shipping Type
                          </label>
                          <select
                            value={editingProfile.type}
                            onChange={(e) => setEditingProfile({ ...editingProfile, type: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="default">Default</option>
                            <option value="express">Express</option>
                            <option value="overnight">Overnight</option>
                            <option value="economy">Economy</option>
                            <option value="pickup">In-Store Pickup</option>
                          </select>
                        </div>
                      </div>
                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={() => setEditingProfile(null)}
                          className="px-3 py-1 text-gray-600 hover:text-gray-800 transition-colors duration-200 flex items-center space-x-1"
                        >
                          <XMarkIcon className="h-4 w-4" />
                          <span>Cancel</span>
                        </button>
                        <button
                          onClick={() => handleUpdateProfile(editingProfile)}
                          disabled={updateProfileMutation.isPending}
                          className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 transition-colors duration-200 flex items-center space-x-1"
                        >
                          <CheckIcon className="h-4 w-4" />
                          <span>{updateProfileMutation.isPending ? 'Saving...' : 'Save'}</span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="text-lg font-medium text-gray-900">{profile.name}</h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProfileTypeColor(profile.type)}`}>
                            {getProfileTypeLabel(profile.type)}
                          </span>
                        </div>
                        
                        <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <ArchiveBoxIcon className="h-4 w-4" />
                            <span>Profile Type: {getProfileTypeLabel(profile.type)}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <ClockIcon className="h-4 w-4" />
                            <span>Created {new Date(profile.created_at).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <MapPinIcon className="h-4 w-4" />
                            <span>Active Profile</span>
                          </div>
                        </div>

                        {profile.metadata?.description && (
                          <div className="mt-3">
                            <p className="text-sm text-gray-600">{profile.metadata.description}</p>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => setEditingProfile(profile)}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
                          title="Edit profile"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteProfile(profile.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors duration-200"
                          title="Delete profile"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Shipping Configuration */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Shipping Options */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Shipping Options</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Free Shipping Threshold</h4>
                <p className="text-sm text-gray-600">Minimum order value for free shipping</p>
              </div>
              <input
                type="number"
                placeholder="100.00"
                className="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Default Shipping Rate</h4>
                <p className="text-sm text-gray-600">Standard shipping cost</p>
              </div>
              <input
                type="number"
                placeholder="10.00"
                className="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Express Shipping Rate</h4>
                <p className="text-sm text-gray-600">Express delivery cost</p>
              </div>
              <input
                type="number"
                placeholder="25.00"
                className="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Shipping Statistics */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Shipping Statistics</h3>
          <div className="space-y-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArchiveBoxIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">{shippingProfiles.length}</div>
                <div className="text-sm text-gray-600">Shipping Profiles</div>
              </div>
            </div>

            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TruckIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">0</div>
                <div className="text-sm text-gray-600">Active Shipments</div>
              </div>
            </div>

            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">€0.00</div>
                <div className="text-sm text-gray-600">Total Shipping Revenue</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Shipping Zones */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Shipping Zones</h3>
        <div className="bg-gray-50 rounded-lg p-4 text-center">
          <MapPinIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-600">
            Shipping zones allow you to set different shipping rates based on geographical locations.
          </p>
          <button className="mt-3 text-blue-600 hover:text-blue-700 text-sm font-medium">
            Configure Shipping Zones
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsShipping; 