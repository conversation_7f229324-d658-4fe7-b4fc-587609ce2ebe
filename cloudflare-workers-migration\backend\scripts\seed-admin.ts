import bcrypt from 'bcryptjs';

// This script creates a default admin user
// Usage: Run this in the Cloudflare Workers D1 console or using wrangler d1 execute

async function createAdminUser() {
  const email = '<EMAIL>';
  const password = 'admin123'; // Change this to a secure password
  const passwordHash = await bcrypt.hash(password, 12);
  
  const adminId = 'admin-' + crypto.randomUUID();
  const now = new Date().toISOString();
  
  console.log('Creating admin user with the following SQL:');
  console.log(`
INSERT INTO admin_users (id, email, password_hash, first_name, last_name, role, created_at, updated_at)
VALUES (
  '${adminId}',
  '${email}',
  '${passwordHash}',
  'Admin',
  'User',
  'super_admin',
  '${now}',
  '${now}'
);
  `);
  
  console.log('\nCredentials:');
  console.log(`Email: ${email}`);
  console.log(`Password: ${password}`);
  console.log('\nIMPORTANT: Change the password after first login!');
}

createAdminUser().catch(console.error); 