import { DatabaseService } from '../src/services/database';

// Configuration
const TARGET_PRODUCT_TYPE = 'ptyp_01JTXB37EFVB4S3MJETQ2HVY0A';
const NEW_SHIPPING_PROFILE = 'sp_01JTXB95KCCZT30EWN692SN9RD';

interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

/**
 * Script to update shipping profiles for products with a specific product type
 * 
 * This script:
 * 1. Finds all products with product_type = ptyp_01JTXB37EFVB4S3MJETQ2HVY0A
 * 2. Updates their shipping_profile relationships to sp_01JTXB95KCCZT30EWN692SN9RD
 * 3. Logs the changes made
 */
export async function updateProductShippingProfiles(env: WorkerEnv) {
  const db = new DatabaseService(env);
  
  try {
    console.log('🚀 Starting product shipping profile update script...');
    console.log(`Target product type: ${TARGET_PRODUCT_TYPE}`);
    console.log(`New shipping profile: ${NEW_SHIPPING_PROFILE}`);
    
    // Find all products with the target product type
    const productsToUpdate = await db.findMany('product', {
      where: `type_id = '${TARGET_PRODUCT_TYPE}' AND (deleted_at IS NULL OR deleted_at = '')`,
    });
    
    console.log(`📦 Found ${productsToUpdate.length} products with target product type`);
    
    if (productsToUpdate.length === 0) {
      console.log('✅ No products found to update. Script completed.');
      return {
        success: true,
        message: 'No products found to update',
        updateCount: 0,
      };
    }
    
    // Verify the target shipping profile exists
    const shippingProfile = await db.findById('shipping_profile', NEW_SHIPPING_PROFILE);
    if (!shippingProfile) {
      throw new Error(`Target shipping profile ${NEW_SHIPPING_PROFILE} not found or deleted`);
    }
    
    console.log(`✅ Verified shipping profile exists: ${shippingProfile.name || 'Unknown'}`);
    
    let updateCount = 0;
    const errors: string[] = [];
    
    // Update each product
    for (const product of productsToUpdate) {
      try {
        // Check if product already has a relationship with the target shipping profile
        const existingRelation = await env.DB.prepare(`
          SELECT * FROM product_shipping_profile 
          WHERE product_id = ? AND shipping_profile_id = ? AND (deleted_at IS NULL OR deleted_at = '')
        `).bind(product.id, NEW_SHIPPING_PROFILE).first();
        
        if (existingRelation) {
          console.log(`⏭️  Product ${product.id} (${product.title}) already has target shipping profile, skipping`);
          continue;
        }
        
        // Get current shipping profile relationships
        const currentRelations = await env.DB.prepare(`
          SELECT shipping_profile_id FROM product_shipping_profile 
          WHERE product_id = ? AND (deleted_at IS NULL OR deleted_at = '')
        `).bind(product.id).all();
        
        const currentProfileIds = currentRelations.results?.map((r: any) => r.shipping_profile_id) || [];
        
        // Soft delete existing relationships
        if (currentProfileIds.length > 0) {
          await env.DB.prepare(`
            UPDATE product_shipping_profile 
            SET deleted_at = ?, updated_at = ?
            WHERE product_id = ? AND (deleted_at IS NULL OR deleted_at = '')
          `).bind(
            new Date().toISOString(),
            new Date().toISOString(),
            product.id
          ).run();
        }
        
        // Create new relationship with target shipping profile
        const relationshipId = crypto.randomUUID();
        await env.DB.prepare(`
          INSERT INTO product_shipping_profile (
            id, product_id, shipping_profile_id, created_at, updated_at, deleted_at
          ) VALUES (?, ?, ?, ?, ?, NULL)
        `).bind(
          relationshipId,
          product.id,
          NEW_SHIPPING_PROFILE,
          new Date().toISOString(),
          new Date().toISOString()
        ).run();
        
        updateCount++;
        console.log(`✅ Updated product ${product.id} (${product.title})`);
        console.log(`   Old shipping profiles: ${currentProfileIds.join(', ') || 'None'}`);
        console.log(`   New shipping profile: ${NEW_SHIPPING_PROFILE}`);
        
      } catch (error) {
        const errorMessage = `Failed to update product ${product.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(`❌ ${errorMessage}`);
        errors.push(errorMessage);
      }
    }
    
    console.log('\n📊 Script Summary:');
    console.log(`   Products found: ${productsToUpdate.length}`);
    console.log(`   Products updated: ${updateCount}`);
    console.log(`   Errors: ${errors.length}`);
    
    if (errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      errors.forEach(error => console.log(`   - ${error}`));
    }
    
    console.log('\n🎉 Script completed successfully!');
    
    return {
      success: true,
      message: 'Script completed',
      totalFound: productsToUpdate.length,
      updateCount,
      errors,
    };
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    throw error;
  }
}

/**
 * Dry run version - shows what would be updated without making changes
 */
export async function dryRunProductShippingProfiles(env: WorkerEnv) {
  const db = new DatabaseService(env);
  
  try {
    console.log('🔍 Running DRY RUN - no changes will be made...');
    console.log(`Target product type: ${TARGET_PRODUCT_TYPE}`);
    console.log(`New shipping profile: ${NEW_SHIPPING_PROFILE}`);
    
    // Find all products with the target product type
    const productsToUpdate = await db.findMany('product', {
      where: `type_id = '${TARGET_PRODUCT_TYPE}' AND (deleted_at IS NULL OR deleted_at = '')`,
    });
    
    console.log(`📦 Found ${productsToUpdate.length} products with target product type`);
    
    if (productsToUpdate.length === 0) {
      console.log('✅ No products found to update.');
      return {
        success: true,
        message: 'No products found to update',
        wouldUpdateCount: 0,
      };
    }
    
    // Verify the target shipping profile exists
    const shippingProfile = await db.findById('shipping_profile', NEW_SHIPPING_PROFILE);
    if (!shippingProfile) {
      throw new Error(`Target shipping profile ${NEW_SHIPPING_PROFILE} not found or deleted`);
    }
    
    console.log(`✅ Verified shipping profile exists: ${shippingProfile.name || 'Unknown'}`);
    
    let wouldUpdateCount = 0;
    
    console.log('\n📋 Products that would be updated:');
    
    for (const product of productsToUpdate) {
      // Check if product already has a relationship with the target shipping profile
      const existingRelation = await env.DB.prepare(`
        SELECT * FROM product_shipping_profile 
        WHERE product_id = ? AND shipping_profile_id = ? AND (deleted_at IS NULL OR deleted_at = '')
      `).bind(product.id, NEW_SHIPPING_PROFILE).first();
      
      if (existingRelation) {
        console.log(`⏭️  Product ${product.id} (${product.title}) already has target shipping profile`);
        continue;
      }
      
      // Get current shipping profile relationships
      const currentRelations = await env.DB.prepare(`
        SELECT sp.id, sp.name FROM product_shipping_profile psp
        JOIN shipping_profile sp ON psp.shipping_profile_id = sp.id
        WHERE psp.product_id = ? AND (psp.deleted_at IS NULL OR psp.deleted_at = '')
      `).bind(product.id).all();
      
      const currentProfiles = currentRelations.results?.map((r: any) => `${r.id} (${r.name})`) || [];
      
      wouldUpdateCount++;
      console.log(`📝 Would update: ${product.id} (${product.title})`);
      console.log(`   Current shipping profiles: ${currentProfiles.join(', ') || 'None'}`);
      console.log(`   Would change to: ${NEW_SHIPPING_PROFILE}`);
    }
    
    console.log('\n📊 Dry Run Summary:');
    console.log(`   Products found: ${productsToUpdate.length}`);
    console.log(`   Products that would be updated: ${wouldUpdateCount}`);
    console.log(`   Products already correct: ${productsToUpdate.length - wouldUpdateCount}`);
    
    return {
      success: true,
      message: 'Dry run completed',
      totalFound: productsToUpdate.length,
      wouldUpdateCount,
      alreadyCorrect: productsToUpdate.length - wouldUpdateCount,
    };
    
  } catch (error) {
    console.error('💥 Dry run failed:', error);
    throw error;
  }
}

// Export for use in other scripts or API endpoints
export default {
  updateProductShippingProfiles,
  dryRunProductShippingProfiles,
  TARGET_PRODUCT_TYPE,
  NEW_SHIPPING_PROFILE,
}; 