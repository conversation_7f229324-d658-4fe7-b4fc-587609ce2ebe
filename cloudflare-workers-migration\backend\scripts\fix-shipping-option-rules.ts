import { DatabaseService } from '../src/services/database';

interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

const TARGET_SHIPPING_OPTION = 'so_01JTXBA1J91ZM38P55CM6MQM2F';

/**
 * <PERSON>ript to add missing shipping option rules and restore deleted shipping option
 */
export async function fixShippingOptionRules(env: WorkerEnv) {
  try {
    console.log('🔧 Fixing shipping option and rules...');
    console.log(`Target shipping option: ${TARGET_SHIPPING_OPTION}`);
    
    // Check if shipping option exists (including deleted ones)
    const shippingOption = await env.DB.prepare(`
      SELECT * FROM shipping_option WHERE id = ?
    `).bind(TARGET_SHIPPING_OPTION).first();
    
    if (!shippingOption) {
      throw new Error(`Shipping option ${TARGET_SHIPPING_OPTION} not found`);
    }
    
    console.log(`📋 Shipping option found: ${shippingOption.name}`);
    console.log(`📋 Deleted status: ${shippingOption.deleted_at ? 'DELETED' : 'ACTIVE'}`);
    
    let restoredOption = false;
    
    // Restore shipping option if it's deleted
    if (shippingOption.deleted_at) {
      await env.DB.prepare(`
        UPDATE shipping_option 
        SET deleted_at = NULL, updated_at = ?
        WHERE id = ?
      `).bind(
        new Date().toISOString(),
        TARGET_SHIPPING_OPTION
      ).run();
      
      console.log(`🔄 Restored shipping option: ${shippingOption.name}`);
      restoredOption = true;
    }
    
    // Check existing rules (only non-deleted ones)
    const existingRules = await env.DB.prepare(`
      SELECT * FROM shipping_option_rule 
      WHERE shipping_option_id = ? 
      AND (deleted_at IS NULL OR deleted_at = '')
    `).bind(TARGET_SHIPPING_OPTION).all();
    
    console.log(`📝 Found ${existingRules.results?.length || 0} existing non-deleted rules`);
    
    // Also check for deleted rules that we might need to restore
    const deletedRules = await env.DB.prepare(`
      SELECT * FROM shipping_option_rule 
      WHERE shipping_option_id = ? 
      AND deleted_at IS NOT NULL AND deleted_at != ''
    `).bind(TARGET_SHIPPING_OPTION).all();
    
    console.log(`🗑️ Found ${deletedRules.results?.length || 0} deleted rules that might be restored`);
    
    const rulesMap = new Map();
    if (existingRules.results) {
      for (const rule of existingRules.results) {
        rulesMap.set(rule.attribute, rule);
      }
    }
    
    const deletedRulesMap = new Map();
    if (deletedRules.results) {
      for (const rule of deletedRules.results) {
        deletedRulesMap.set(rule.attribute, rule);
      }
    }
    
    // Required rules
    const requiredRules = [
      { attribute: 'is_return', value: '"false"' },
      { attribute: 'enabled_in_store', value: '"true"' }
    ];
    
    let addedCount = 0;
    let updatedCount = 0;
    let restoredCount = 0;
    
    for (const requiredRule of requiredRules) {
      const existingRule = rulesMap.get(requiredRule.attribute);
      const deletedRule = deletedRulesMap.get(requiredRule.attribute);
      
      if (existingRule) {
        // Update existing rule if value is different
        if (existingRule.value !== requiredRule.value) {
          await env.DB.prepare(`
            UPDATE shipping_option_rule 
            SET value = ?, updated_at = ?
            WHERE id = ?
          `).bind(
            requiredRule.value,
            new Date().toISOString(),
            existingRule.id
          ).run();
          
          console.log(`✅ Updated rule: ${requiredRule.attribute} = ${requiredRule.value}`);
          updatedCount++;
        } else {
          console.log(`✓ Rule already correct: ${requiredRule.attribute} = ${requiredRule.value}`);
        }
      } else if (deletedRule) {
        // Restore deleted rule with correct value
        await env.DB.prepare(`
          UPDATE shipping_option_rule 
          SET value = ?, updated_at = ?, deleted_at = NULL
          WHERE id = ?
        `).bind(
          requiredRule.value,
          new Date().toISOString(),
          deletedRule.id
        ).run();
        
        console.log(`🔄 Restored and updated rule: ${requiredRule.attribute} = ${requiredRule.value}`);
        restoredCount++;
      } else {
        // Add missing rule
        const ruleId = crypto.randomUUID();
        await env.DB.prepare(`
          INSERT INTO shipping_option_rule (
            id, shipping_option_id, attribute, operator, value, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `).bind(
          ruleId,
          TARGET_SHIPPING_OPTION,
          requiredRule.attribute,
          'eq', // Equal operator
          requiredRule.value,
          new Date().toISOString(),
          new Date().toISOString()
        ).run();
        
        console.log(`✅ Added rule: ${requiredRule.attribute} = ${requiredRule.value}`);
        addedCount++;
      }
    }
    
    console.log('\n📊 Summary:');
    console.log(`   Shipping option restored: ${restoredOption ? 1 : 0}`);
    console.log(`   Rules added: ${addedCount}`);
    console.log(`   Rules updated: ${updatedCount}`);
    console.log(`   Rules restored: ${restoredCount}`);
    console.log(`   Total active rules now: ${(existingRules.results?.length || 0) + addedCount + restoredCount}`);
    
    return {
      success: true,
      message: 'Shipping option and rules fixed successfully',
      shippingOptionId: TARGET_SHIPPING_OPTION,
      restoredOption,
      addedCount,
      updatedCount,
      restoredCount,
    };
    
  } catch (error) {
    console.error('💥 Failed to fix shipping option rules:', error);
    throw error;
  }
}

/**
 * Dry run version - shows what would be added/updated without making changes
 */
export async function dryRunFixShippingOptionRules(env: WorkerEnv) {
  try {
    console.log('🔍 DRY RUN - checking shipping option rules...');
    console.log(`Target shipping option: ${TARGET_SHIPPING_OPTION}`);
    
    // Check if shipping option exists
    const shippingOption = await env.DB.prepare(`
      SELECT * FROM shipping_option WHERE id = ?
    `).bind(TARGET_SHIPPING_OPTION).first();
    
    if (!shippingOption) {
      throw new Error(`Shipping option ${TARGET_SHIPPING_OPTION} not found`);
    }
    
    console.log(`📋 Shipping option found: ${shippingOption.name}`);
    
    // Check existing rules (only non-deleted ones)
    const existingRules = await env.DB.prepare(`
      SELECT * FROM shipping_option_rule 
      WHERE shipping_option_id = ? 
      AND (deleted_at IS NULL OR deleted_at = '')
    `).bind(TARGET_SHIPPING_OPTION).all();
    
    console.log(`📝 Found ${existingRules.results?.length || 0} existing non-deleted rules`);
    
    // Also check for deleted rules that we might need to restore
    const deletedRules = await env.DB.prepare(`
      SELECT * FROM shipping_option_rule 
      WHERE shipping_option_id = ? 
      AND deleted_at IS NOT NULL AND deleted_at != ''
    `).bind(TARGET_SHIPPING_OPTION).all();
    
    console.log(`🗑️ Found ${deletedRules.results?.length || 0} deleted rules that might be restored`);
    
    const rulesMap = new Map();
    if (existingRules.results) {
      for (const rule of existingRules.results) {
        rulesMap.set(rule.attribute, rule);
        console.log(`   - ${rule.attribute} = ${rule.value}`);
      }
    }
    
    const deletedRulesMap = new Map();
    if (deletedRules.results) {
      for (const rule of deletedRules.results) {
        deletedRulesMap.set(rule.attribute, rule);
        console.log(`   - [DELETED] ${rule.attribute} = ${rule.value}`);
      }
    }
    
    // Required rules
    const requiredRules = [
      { attribute: 'is_return', value: '"false"' },
      { attribute: 'enabled_in_store', value: '"true"' }
    ];
    
    let wouldAddCount = 0;
    let wouldUpdateCount = 0;
    let wouldRestoreCount = 0;
    
    console.log('\n📋 Required rules analysis:');
    
    for (const requiredRule of requiredRules) {
      const existingRule = rulesMap.get(requiredRule.attribute);
      const deletedRule = deletedRulesMap.get(requiredRule.attribute);
      
      if (existingRule) {
        if (existingRule.value !== requiredRule.value) {
          console.log(`📝 Would UPDATE: ${requiredRule.attribute} from "${existingRule.value}" to "${requiredRule.value}"`);
          wouldUpdateCount++;
        } else {
          console.log(`✓ Already correct: ${requiredRule.attribute} = ${requiredRule.value}`);
        }
      } else if (deletedRule) {
        console.log(`🔄 Would RESTORE and UPDATE: ${requiredRule.attribute} from "${deletedRule.value}" to "${requiredRule.value}"`);
        wouldRestoreCount++;
      } else {
        console.log(`📝 Would ADD: ${requiredRule.attribute} = ${requiredRule.value}`);
        wouldAddCount++;
      }
    }
    
    console.log('\n📊 Dry Run Summary:');
    console.log(`   Rules that would be added: ${wouldAddCount}`);
    console.log(`   Rules that would be updated: ${wouldUpdateCount}`);
    console.log(`   Rules that would be restored: ${wouldRestoreCount}`);
    console.log(`   Rules already correct: ${requiredRules.length - wouldAddCount - wouldUpdateCount - wouldRestoreCount}`);
    
    return {
      success: true,
      message: 'Dry run completed',
      shippingOptionId: TARGET_SHIPPING_OPTION,
      wouldAddCount,
      wouldUpdateCount,
      wouldRestoreCount,
    };
    
  } catch (error) {
    console.error('💥 Dry run failed:', error);
    throw error;
  }
}

export default {
  fixShippingOptionRules,
  dryRunFixShippingOptionRules,
  TARGET_SHIPPING_OPTION,
}; 