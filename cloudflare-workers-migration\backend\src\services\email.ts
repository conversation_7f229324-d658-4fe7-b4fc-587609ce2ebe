import { WorkerEnv } from 'handmadein-shared';

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface EmailData {
  to: string;
  from?: string;
  subject: string;
  html: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: string;
    contentType: string;
  }>;
}

export class EmailService {
  private env: WorkerEnv;
  private defaultFrom: string;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.defaultFrom = '<EMAIL>';
  }

  async sendEmail(data: EmailData): Promise<boolean> {
    try {
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: data.from || this.defaultFrom,
          to: [data.to],
          subject: data.subject,
          html: data.html,
          text: data.text,
          attachments: data.attachments,
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        console.error('Email sending failed:', error);
        return false;
      }

      const result = await response.json() as { id?: string };
      console.log('Email sent successfully:', result.id || 'unknown');
      return true;
    } catch (error) {
      console.error('Email service error:', error);
      return false;
    }
  }

  async sendOrderConfirmation(order: any, customer: any): Promise<boolean> {
    const template = this.getOrderConfirmationTemplate(order, customer);
    
    return this.sendEmail({
      to: customer.email,
      subject: `Order Confirmation #${order.display_id} - HandmadeIn.ro`,
      html: template.html,
      text: template.text,
    });
  }

  async sendPasswordReset(email: string, resetToken: string): Promise<boolean> {
    const resetUrl = `${this.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    const template = this.getPasswordResetTemplate(resetUrl);
    
    return this.sendEmail({
      to: email,
      subject: 'Password Reset - HandmadeIn.ro',
      html: template.html,
      text: template.text,
    });
  }

  async sendWelcomeEmail(customer: any): Promise<boolean> {
    const template = this.getWelcomeTemplate(customer);
    
    return this.sendEmail({
      to: customer.email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendOrderStatusUpdate(order: any, customer: any, status: string): Promise<boolean> {
    const template = this.getOrderStatusTemplate(order, customer, status);
    
    return this.sendEmail({
      to: customer.email,
      subject: `Order #${order.display_id} Status Update - HandmadeIn.ro`,
      html: template.html,
      text: template.text,
    });
  }

  private getOrderConfirmationTemplate(order: any, customer: any): EmailTemplate {
    const itemsHtml = order.items.map((item: any) => `
      <tr>
        <td style="padding: 10px; border-bottom: 1px solid #eee;">
          ${item.title}
        </td>
        <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: center;">
          ${item.quantity}
        </td>
        <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">
          ${item.unit_price * item.quantity} RON
        </td>
      </tr>
    `).join('');

    const metadata = JSON.parse(order.metadata || '{}');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Order Confirmation</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2c5aa0;">Thank you for your order!</h1>
          
          <p>Dear ${customer.first_name || 'Customer'},</p>
          
          <p>We've received your order and are preparing it for shipment. Here are the details:</p>
          
          <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h2>Order #${order.display_id}</h2>
            <p><strong>Order Date:</strong> ${new Date(order.created_at).toLocaleDateString()}</p>
            <p><strong>Status:</strong> ${order.status}</p>
          </div>
          
          <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
              <tr style="background: #f0f0f0;">
                <th style="padding: 10px; text-align: left; border-bottom: 2px solid #ddd;">Item</th>
                <th style="padding: 10px; text-align: center; border-bottom: 2px solid #ddd;">Quantity</th>
                <th style="padding: 10px; text-align: right; border-bottom: 2px solid #ddd;">Price</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
            </tbody>
            <tfoot>
              <tr>
                <td colspan="2" style="padding: 10px; text-align: right; font-weight: bold;">
                  Subtotal:
                </td>
                <td style="padding: 10px; text-align: right; font-weight: bold;">
                  ${metadata.subtotal || 0} RON
                </td>
              </tr>
              <tr>
                <td colspan="2" style="padding: 10px; text-align: right;">
                  Shipping:
                </td>
                <td style="padding: 10px; text-align: right;">
                  ${metadata.shipping_total || 0} RON
                </td>
              </tr>
              <tr>
                <td colspan="2" style="padding: 10px; text-align: right;">
                  Tax:
                </td>
                <td style="padding: 10px; text-align: right;">
                  ${metadata.tax_total || 0} RON
                </td>
              </tr>
              <tr style="background: #f0f0f0;">
                <td colspan="2" style="padding: 10px; text-align: right; font-weight: bold; font-size: 18px;">
                  Total:
                </td>
                <td style="padding: 10px; text-align: right; font-weight: bold; font-size: 18px;">
                  ${metadata.total || 0} RON
                </td>
              </tr>
            </tfoot>
          </table>
          
          <div style="background: #e8f4fd; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>What's next?</h3>
            <p>We'll send you a shipping confirmation email with tracking information once your order ships.</p>
            <p>If you have any questions, please contact <NAME_EMAIL></p>
          </div>
          
          <p>Thank you for choosing HandmadeIn.ro!</p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="font-size: 12px; color: #666;">
            HandmadeIn.ro<br>
            This email was sent to ${customer.email}
          </p>
        </div>
      </body>
      </html>
    `;

    const text = `
Thank you for your order!

Dear ${customer.first_name || 'Customer'},

We've received your order #${order.display_id} and are preparing it for shipment.

Order Details:
${order.items.map((item: any) => `- ${item.title} x${item.quantity} - ${item.unit_price * item.quantity} RON`).join('\n')}

Total: ${metadata.total || 0} RON

We'll send you a shipping confirmation email with tracking information once your order ships.

Thank you for choosing HandmadeIn.ro!
    `;

    return { subject: `Order Confirmation #${order.display_id}`, html, text };
  }

  private getPasswordResetTemplate(resetUrl: string): EmailTemplate {
    // Common styles for all emails (matching the AWS Cognito design)
    const commonStyles = `
      body { 
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #334433;
        background-color: #f9f7f2;
        margin: 0;
        padding: 0;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .header {
        background-color: #2c5530;
        color: #ffffff;
        padding: 20px;
        text-align: center;
        border-radius: 8px 8px 0 0;
        margin: -20px -20px 20px -20px;
      }
      .footer {
        background-color: #f9f7f2;
        padding: 15px;
        text-align: center;
        font-size: 14px;
        color: #666;
        border-radius: 0 0 8px 8px;
        margin: 20px -20px -20px -20px;
        border-top: 1px solid #e0d8c5;
      }
      h1 {
        color: #ffffff;
        margin: 0;
        font-size: 24px;
      }
      h2 {
        color: #2c5530;
        font-size: 20px;
      }
      .button {
        background-color: #c75f34;
        color: white;
        padding: 12px 25px;
        text-decoration: none;
        border-radius: 4px;
        display: inline-block;
        font-weight: bold;
        margin: 15px 0;
      }
      a {
        color: #c75f34;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
      p {
        margin: 15px 0;
      }
    `;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Resetare Parolă</title>
        <style>${commonStyles}</style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <h1>Resetare Parolă</h1>
          </div>
          
          <h2>Salut!</h2>
          <p>Am primit o solicitare de resetare a parolei pentru contul tău Handmade in RO. Nu-ți face griji, se întâmplă și celor mai buni dintre noi!</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" class="button">Resetează Parola</a>
          </div>
          
          <p>Poți de asemenea să faci clic aici pentru a reseta parola: <a href="${resetUrl}">${resetUrl}</a></p>
          
          <p>Acest link va expira în 1 oră din motive de securitate.</p>
          
          <p>Dacă nu ai solicitat resetarea parolei, te rugăm să ignori acest mesaj sau să contactezi echipa noastră de asistență dacă ai nelămuriri.</p>
          
          <p>Abia așteptăm să te revedem în magazinul nostru!</p>
          
          <div class="footer">
            <p>Produse autentice românești realizate manual</p>
            <p>© ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Resetare Parolă

Salut!

Am primit o solicitare de resetare a parolei pentru contul tău Handmade in RO. Nu-ți face griji, se întâmplă și celor mai buni dintre noi!

Fă clic pe acest link pentru a reseta parola: ${resetUrl}

Acest link va expira în 1 oră din motive de securitate.

Dacă nu ai solicitat resetarea parolei, te rugăm să ignori acest mesaj sau să contactezi echipa noastră de asistență dacă ai nelămuriri.

Abia așteptăm să te revedem în magazinul nostru!

Produse autentice românești realizate manual
© ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
    `;

    return { subject: 'Resetează Parola pentru Handmade in RO', html, text };
  }

  private getWelcomeTemplate(customer: any): EmailTemplate {
    // Common styles for all emails (matching the AWS Cognito design)
    const commonStyles = `
      body { 
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #334433;
        background-color: #f9f7f2;
        margin: 0;
        padding: 0;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .header {
        background-color: #2c5530;
        color: #ffffff;
        padding: 20px;
        text-align: center;
        border-radius: 8px 8px 0 0;
        margin: -20px -20px 20px -20px;
      }
      .footer {
        background-color: #f9f7f2;
        padding: 15px;
        text-align: center;
        font-size: 14px;
        color: #666;
        border-radius: 0 0 8px 8px;
        margin: 20px -20px -20px -20px;
        border-top: 1px solid #e0d8c5;
      }
      h1 {
        color: #ffffff;
        margin: 0;
        font-size: 24px;
      }
      h2 {
        color: #2c5530;
        font-size: 20px;
      }
      .button {
        background-color: #c75f34;
        color: white;
        padding: 12px 25px;
        text-decoration: none;
        border-radius: 4px;
        display: inline-block;
        font-weight: bold;
        margin: 15px 0;
      }
      a {
        color: #c75f34;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
      p {
        margin: 15px 0;
      }
    `;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Bine ai venit la Handmade in RO</title>
        <style>${commonStyles}</style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <h1>Bine ai venit la Handmade in RO!</h1>
          </div>
          
          <h2>Îți mulțumim că te-ai alăturat nouă!</h2>
          <p>Dragă ${customer.first_name || 'Client'},</p>
          
          <p>Suntem încântați să te avem ca parte a comunității noastre. Contul tău a fost creat cu succes și acum ești gata să explorezi colecția noastră unică de comori românești lucrate manual.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${this.env.FRONTEND_URL || 'https://handmadein.ro'}" class="button">Începe Cumpărăturile</a>
          </div>
          
          <p>Abia așteptăm să împărtășim cu tine pasiunea noastră pentru artizanatul autentic! Explorează colecția noastră de:</p>
          
          <ul>
            <li>Îmbrăcăminte și accesorii tradiționale românești</li>
            <li>Textile și materiale țesute manual</li>
            <li>Articole din lemn și decorațiuni pentru casă</li>
            <li>Ceramică și olărit autentic</li>
            <li>Și multe altele!</li>
          </ul>
          
          <p>Dacă ai întrebări despre contul tău sau produsele noastre, echipa noastră de servicii pentru clienți este întotdeauna gata să te <NAME_EMAIL></p>
          
          <p>Cumpărături plăcute!</p>
          
          <div class="footer">
            <p>Produse autentice românești realizate manual</p>
            <p>© ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Bine ai venit la Handmade in RO!

Îți mulțumim că te-ai alăturat nouă!

Dragă ${customer.first_name || 'Client'},

Suntem încântați să te avem ca parte a comunității noastre. Contul tău a fost creat cu succes și acum ești gata să explorezi colecția noastră unică de comori românești lucrate manual.

Vizitează magazinul nostru: ${this.env.FRONTEND_URL || 'https://handmadein.ro'}

Abia așteptăm să împărtășim cu tine pasiunea noastră pentru artizanatul autentic! Explorează colecția noastră de:

• Îmbrăcăminte și accesorii tradiționale românești
• Textile și materiale țesute manual  
• Articole din lemn și decorațiuni pentru casă
• Ceramică și olărit autentic
• Și multe altele!

Dacă ai întrebări despre contul tău sau produsele noastre, echipa noastră de servicii pentru clienți este întotdeauna gata să te <NAME_EMAIL>

Cumpărături plăcute!

Produse autentice românești realizate manual
© ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
    `;

    return { subject: 'Bine ai venit la Handmade in RO!', html, text };
  }

  private getOrderStatusTemplate(order: any, customer: any, status: string): EmailTemplate {
    const statusMessages = {
      'completed': 'Your order has been completed and is ready for pickup/delivery!',
      'shipped': 'Your order has been shipped and is on its way to you!',
      'canceled': 'Your order has been canceled.',
      'refunded': 'Your order has been refunded.',
    };

    const message = statusMessages[status as keyof typeof statusMessages] || `Your order status has been updated to: ${status}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Order Status Update</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2c5aa0;">Order Status Update</h1>
          
          <p>Dear ${customer.first_name || 'Customer'},</p>
          
          <p>${message}</p>
          
          <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h2>Order #${order.display_id}</h2>
            <p><strong>Status:</strong> ${status}</p>
            <p><strong>Updated:</strong> ${new Date().toLocaleDateString()}</p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${this.env.FRONTEND_URL}/account/orders/${order.id}" style="background: #2c5aa0; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              View Order Details
            </a>
          </div>
          
          <p>If you have any questions, please contact <NAME_EMAIL></p>
          
          <p>Thank you for choosing HandmadeIn.ro!</p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="font-size: 12px; color: #666;">
            HandmadeIn.ro<br>
            This email was sent to ${customer.email}
          </p>
        </div>
      </body>
      </html>
    `;

    const text = `
Order Status Update

Dear ${customer.first_name || 'Customer'},

${message}

Order #${order.display_id}
Status: ${status}
Updated: ${new Date().toLocaleDateString()}

View your order at: ${this.env.FRONTEND_URL}/account/orders/${order.id}

Thank you for choosing HandmadeIn.ro!
    `;

    return { subject: `Order #${order.display_id} Status Update`, html, text };
  }
} 