import { sqliteTable, text, integer, real, blob } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// Base fields for all entities
const baseFields = {
  id: text('id').primaryKey(),
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  deleted_at: text('deleted_at'),
};

// Products
export const products = sqliteTable('products', {
  ...baseFields,
  subtitle: text('subtitle'),
  description: text('description'),
  handle: text('handle').unique().notNull(),
  is_giftcard: integer('is_giftcard', { mode: 'boolean' }).default(false).notNull(),
  status: text('status').default('draft').notNull(), // draft, proposed, published, rejected
  thumbnail: text('thumbnail'),
  weight: real('weight'),
  length: real('length'),
  height: real('height'),
  width: real('width'),
  hs_code: text('hs_code'),
  origin_country: text('origin_country'),
  mid_code: text('mid_code'),
  material: text('material'),
  collection_id: text('collection_id'),
  type_id: text('type_id'),
  discountable: integer('discountable', { mode: 'boolean' }).default(true).notNull(),
  external_id: text('external_id'),
  metadata: text('metadata'), // JSON string
});

// Product translations
export const productTranslations = sqliteTable('product_translations', {
  ...baseFields,
  product_id: text('product_id').notNull(),
  language_code: text('language_code').notNull(),
  title: text('title').notNull(),
  description: text('description'),
});

export const productVariants = sqliteTable('product_variants', {
  ...baseFields,
  title: text('title').notNull(),
  product_id: text('product_id').notNull(),
  sku: text('sku'),
  barcode: text('barcode'),
  ean: text('ean'),
  upc: text('upc'),
  variant_rank: integer('variant_rank'),
  inventory_quantity: integer('inventory_quantity').default(0).notNull(),
  allow_backorder: integer('allow_backorder', { mode: 'boolean' }).default(false).notNull(),
  manage_inventory: integer('manage_inventory', { mode: 'boolean' }).default(true).notNull(),
  hs_code: text('hs_code'),
  origin_country: text('origin_country'),
  mid_code: text('mid_code'),
  material: text('material'),
  weight: real('weight'),
  length: real('length'),
  height: real('height'),
  width: real('width'),
  metadata: text('metadata'), // JSON string
});

export const productCollections = sqliteTable('product_collections', {
  ...baseFields,
  title: text('title').notNull(),
  handle: text('handle').unique().notNull(),
  metadata: text('metadata'), // JSON string
});

export const productImages = sqliteTable('product_images', {
  ...baseFields,
  product_id: text('product_id').notNull(),
  url: text('url').notNull(),
  alt_text: text('alt_text'),
  sort_order: integer('sort_order').default(0),
});

export const productTags = sqliteTable('product_tags', {
  ...baseFields,
  value: text('value').notNull(),
});

export const productProductTags = sqliteTable('product_product_tags', {
  product_id: text('product_id').notNull(),
  tag_id: text('tag_id').notNull(),
});

// Customers
export const customers = sqliteTable('customers', {
  ...baseFields,
  email: text('email').unique().notNull(),
  first_name: text('first_name'),
  last_name: text('last_name'),
  billing_address_id: text('billing_address_id'),
  phone: text('phone'),
  has_account: integer('has_account', { mode: 'boolean' }).default(false).notNull(),
  password_hash: text('password_hash'),
  metadata: text('metadata'), // JSON string
});

export const customerGroups = sqliteTable('customer_groups', {
  ...baseFields,
  name: text('name').notNull(),
  metadata: text('metadata'), // JSON string
});

export const customerGroupCustomers = sqliteTable('customer_group_customers', {
  customer_group_id: text('customer_group_id').notNull(),
  customer_id: text('customer_id').notNull(),
});

// Addresses
export const addresses = sqliteTable('addresses', {
  ...baseFields,
  customer_id: text('customer_id'),
  company: text('company'),
  first_name: text('first_name'),
  last_name: text('last_name'),
  address_1: text('address_1'),
  address_2: text('address_2'),
  city: text('city'),
  country_code: text('country_code'),
  province: text('province'),
  postal_code: text('postal_code'),
  phone: text('phone'),
  metadata: text('metadata'), // JSON string
});

// Regions and Countries
export const regions = sqliteTable('regions', {
  ...baseFields,
  name: text('name').notNull(),
  currency_code: text('currency_code').notNull(),
  tax_rate: real('tax_rate').default(0),
  tax_code: text('tax_code'),
  gift_cards_taxable: integer('gift_cards_taxable', { mode: 'boolean' }).default(true),
  automatic_taxes: integer('automatic_taxes', { mode: 'boolean' }).default(true),
  metadata: text('metadata'), // JSON string
});

export const countries = sqliteTable('countries', {
  id: text('id').primaryKey(), // ISO 2 letter code
  iso_2: text('iso_2').notNull(),
  iso_3: text('iso_3').notNull(),
  num_code: integer('num_code').notNull(),
  name: text('name').notNull(),
  display_name: text('display_name').notNull(),
  region_id: text('region_id'),
});

// Carts
export const carts = sqliteTable('carts', {
  ...baseFields,
  email: text('email'),
  billing_address_id: text('billing_address_id'),
  shipping_address_id: text('shipping_address_id'),
  region_id: text('region_id').notNull(),
  customer_id: text('customer_id'),
  payment_id: text('payment_id'),
  type: text('type').default('default').notNull(), // default, swap, draft_order, payment_link, claim
  completed_at: text('completed_at'),
  payment_authorized_at: text('payment_authorized_at'),
  idempotency_key: text('idempotency_key'),
  context: text('context'), // JSON string
  sales_channel_id: text('sales_channel_id'),
  metadata: text('metadata'), // JSON string
});

// Order Items (instead of Line Items)
export const orderItems = sqliteTable('order_items', {
  ...baseFields,
  order_id: text('order_id').notNull(),
  variant_id: text('variant_id'),
  product_title: text('product_title').notNull(),
  variant_title: text('variant_title'),
  sku: text('sku'),
  quantity: integer('quantity').notNull(),
  unit_price: real('unit_price').notNull(),
  total_price: real('total_price').notNull(),
  fulfilled_quantity: integer('fulfilled_quantity').default(0),
  refunded_quantity: integer('refunded_quantity').default(0),
  metadata: text('metadata'), // JSON string
});

// Orders
export const orders = sqliteTable('orders', {
  ...baseFields,
  status: text('status').default('pending').notNull(), // pending, completed, archived, canceled, requires_action
  fulfillment_status: text('fulfillment_status').default('not_fulfilled').notNull(),
  payment_status: text('payment_status').default('not_paid').notNull(),
  display_id: integer('display_id').notNull(),
  cart_id: text('cart_id'),
  customer_id: text('customer_id').notNull(),
  email: text('email').notNull(),
  billing_address_id: text('billing_address_id'),
  shipping_address_id: text('shipping_address_id'),
  region_id: text('region_id').notNull(),
  currency_code: text('currency_code').notNull(),
  tax_rate: real('tax_rate'),
  draft_order_id: text('draft_order_id'),
  no_notification: integer('no_notification', { mode: 'boolean' }),
  idempotency_key: text('idempotency_key'),
  external_id: text('external_id'),
  sales_channel_id: text('sales_channel_id'),
  metadata: text('metadata'), // JSON string
});

// Payments
export const payments = sqliteTable('payments', {
  ...baseFields,
  swap_id: text('swap_id'),
  cart_id: text('cart_id'),
  order_id: text('order_id'),
  amount: integer('amount').notNull(), // in cents
  currency_code: text('currency_code').notNull(),
  amount_refunded: integer('amount_refunded').default(0).notNull(),
  provider_id: text('provider_id').notNull(),
  data: text('data').notNull(), // JSON string
  captured_at: text('captured_at'),
  canceled_at: text('canceled_at'),
  metadata: text('metadata'), // JSON string
});

export const paymentSessions = sqliteTable('payment_sessions', {
  ...baseFields,
  cart_id: text('cart_id'),
  provider_id: text('provider_id').notNull(),
  is_selected: integer('is_selected', { mode: 'boolean' }),
  is_initiated: integer('is_initiated', { mode: 'boolean' }).default(false).notNull(),
  status: text('status').notNull(), // authorized, pending, requires_more, error, canceled
  data: text('data').notNull(), // JSON string
  amount: integer('amount'), // in cents
  payment_authorized_at: text('payment_authorized_at'),
});

// Shipping
export const shippingOptions = sqliteTable('shipping_options', {
  ...baseFields,
  name: text('name').notNull(),
  region_id: text('region_id').notNull(),
  profile_id: text('profile_id').notNull(),
  provider_id: text('provider_id').notNull(),
  price_type: text('price_type').notNull(), // flat_rate, calculated
  amount: integer('amount'), // in cents
  is_return: integer('is_return', { mode: 'boolean' }).default(false),
  admin_only: integer('admin_only', { mode: 'boolean' }).default(false),
  data: text('data'), // JSON string
  metadata: text('metadata'), // JSON string
});

export const shippingMethods = sqliteTable('shipping_methods', {
  ...baseFields,
  shipping_option_id: text('shipping_option_id').notNull(),
  order_id: text('order_id'),
  cart_id: text('cart_id'),
  swap_id: text('swap_id'),
  return_id: text('return_id'),
  claim_order_id: text('claim_order_id'),
  price: integer('price').notNull(), // in cents
  data: text('data').notNull(), // JSON string
  includes_tax: integer('includes_tax', { mode: 'boolean' }).default(false),
  subtotal: integer('subtotal').notNull(),
  total: integer('total').notNull(),
  tax_total: integer('tax_total').notNull(),
});

// Fulfillments
export const fulfillments = sqliteTable('fulfillments', {
  ...baseFields,
  claim_order_id: text('claim_order_id'),
  swap_id: text('swap_id'),
  order_id: text('order_id'),
  tracking_numbers: text('tracking_numbers'), // JSON array
  data: text('data').notNull(), // JSON string
  shipped_at: text('shipped_at'),
  canceled_at: text('canceled_at'),
  metadata: text('metadata'), // JSON string
  provider_id: text('provider_id').notNull(),
  location_id: text('location_id'),
  no_notification: integer('no_notification', { mode: 'boolean' }),
});

// Returns
export const returns = sqliteTable('returns', {
  ...baseFields,
  status: text('status').default('requested').notNull(), // requested, received, requires_action, canceled
  order_id: text('order_id').notNull(),
  swap_id: text('swap_id'),
  claim_order_id: text('claim_order_id'),
  shipping_data: text('shipping_data'), // JSON string
  refund_amount: integer('refund_amount').notNull(),
  received_at: text('received_at'),
  metadata: text('metadata'), // JSON string
});

// Refunds
export const refunds = sqliteTable('refunds', {
  ...baseFields,
  order_id: text('order_id'),
  payment_id: text('payment_id').notNull(),
  amount: integer('amount').notNull(), // in cents
  note: text('note'),
  reason: text('reason').notNull(), // discount, return, swap, claim, other
  metadata: text('metadata'), // JSON string
  idempotency_key: text('idempotency_key'),
});

// Custom modules

// Journal entries
export const journalEntries = sqliteTable('journal_entries', {
  ...baseFields,
  title: text('title').notNull(),
  content: text('content').notNull(),
  excerpt: text('excerpt'),
  slug: text('slug').unique().notNull(),
  author: text('author'),
  image_url: text('image_url'),
  published: integer('published', { mode: 'boolean' }).default(false).notNull(),
  published_at: text('published_at'),
  tags: text('tags'), // JSON array
  metadata: text('metadata'), // JSON string
});

// Product sales tracking
export const productSales = sqliteTable('product_sales', {
  ...baseFields,
  product_id: text('product_id').notNull(),
  total_sales: integer('total_sales').default(0).notNull(),
  quantity_sold: integer('quantity_sold').default(0).notNull(),
  revenue: integer('revenue').default(0).notNull(), // in cents
  last_sale_at: text('last_sale_at'),
});

// Collection images
export const collectionImages = sqliteTable('collection_images', {
  ...baseFields,
  collection_id: text('collection_id').notNull(),
  image_url: text('image_url').notNull(),
  alt_text: text('alt_text'),
  sort_order: integer('sort_order').default(0),
});

// Trusted shop reviews
export const trustedShopReviews = sqliteTable('trusted_shop_reviews', {
  ...baseFields,
  rating: integer('rating').notNull(),
  comment: text('comment'),
  customer_name: text('customer_name'),
  product_id: text('product_id'),
  order_id: text('order_id'),
  verified: integer('verified', { mode: 'boolean' }).default(false),
  external_id: text('external_id'),
});

// Admin users
export const adminUsers = sqliteTable('admin_users', {
  ...baseFields,
  email: text('email').unique().notNull(),
  password_hash: text('password_hash').notNull(),
  first_name: text('first_name'),
  last_name: text('last_name'),
  role: text('role').default('viewer').notNull(), // super_admin, admin, manager, editor, viewer
  permissions: text('permissions'), // JSON array
  last_login: text('last_login'),
  metadata: text('metadata'), // JSON string
});

// Sessions
export const sessions = sqliteTable('sessions', {
  id: text('id').primaryKey(),
  user_id: text('user_id').notNull(),
  user_type: text('user_type').notNull(), // customer, admin
  expires_at: text('expires_at').notNull(),
  data: text('data'), // JSON string
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// File uploads
export const fileUploads = sqliteTable('file_uploads', {
  ...baseFields,
  filename: text('filename').notNull(),
  original_name: text('original_name').notNull(),
  mimetype: text('mimetype').notNull(),
  size: integer('size').notNull(),
  url: text('url').notNull(),
  bucket: text('bucket').notNull(),
  key: text('key').notNull(),
  uploaded_by: text('uploaded_by'),
  metadata: text('metadata'), // JSON string
});

// Email templates
export const emailTemplates = sqliteTable('email_templates', {
  ...baseFields,
  name: text('name').unique().notNull(),
  subject: text('subject').notNull(),
  html: text('html').notNull(),
  text: text('text'),
  variables: text('variables'), // JSON array
  active: integer('active', { mode: 'boolean' }).default(true),
});

// Notification events
export const notificationEvents = sqliteTable('notification_events', {
  ...baseFields,
  type: text('type').notNull(),
  data: text('data').notNull(), // JSON string
  recipient: text('recipient').notNull(),
  template_id: text('template_id'),
  status: text('status').default('pending').notNull(), // pending, sent, failed
  sent_at: text('sent_at'),
  error_message: text('error_message'),
});

// Settings
export const settings = sqliteTable('settings', {
  key: text('key').primaryKey(),
  value: text('value').notNull(), // JSON string
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Activity logs
export const activityLogs = sqliteTable('activity_logs', {
  ...baseFields,
  user_id: text('user_id').notNull(),
  user_type: text('user_type').notNull(), // customer, admin
  action: text('action').notNull(),
  resource_type: text('resource_type').notNull(),
  resource_id: text('resource_id'),
  details: text('details'), // JSON string
  ip_address: text('ip_address'),
  user_agent: text('user_agent'),
}); 