import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import {
  ShoppingBagIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ChartBarIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { format, subDays } from 'date-fns';

interface DashboardData {
  stats: {
    totalRevenue: number;
    totalOrders: number;
    totalCustomers: number;
    avgOrderValue: number;
    revenueGrowth: number;
    ordersGrowth: number;
    customersGrowth: number;
    conversionRate: number;
  };
  recentOrders: Array<{
    id: string;
    display_id: number;
    total: number;
    currency_code: string;
    status: string;
    customer: { first_name: string; last_name: string } | null;
    created_at: string;
  }>;
  salesChart: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
  topProducts: Array<{
    id: string;
    title: string;
    thumbnail?: string;
    sales: number;
    revenue: number;
  }>;
  ordersByStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  lowStockProducts: Array<{
    id: string;
    title: string;
    thumbnail?: string;
    inventory_quantity: number;
    low_stock_threshold: number;
  }>;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

const Dashboard: React.FC = () => {
  console.log('🔍 Dashboard: Component is rendering!');

  const { data: dashboardData, isLoading, error } = useQuery({
    queryKey: ['dashboard'],
    queryFn: async () => {
      const response = await api.get('/admin/api/analytics/dashboard');
      return response.data;
    },
  });

  console.log('🔍 Dashboard: Current state - isLoading:', isLoading, 'error:', error, 'data:', dashboardData);

  const formatCurrency = (amount: number, currencyCode = 'RON') => {
    return new Intl.NumberFormat('ro-RO', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const formatted = Math.abs(value).toFixed(1);
    return `${value >= 0 ? '+' : '-'}${formatted}%`;
  };

  const formatOrderStatus = (status: string): string => {
    const statusTranslations: Record<string, string> = {
      pending: 'În așteptare',
      processing: 'În procesare',
      shipped: 'Expediat',
      delivered: 'Livrat',
      completed: 'Finalizat',
      canceled: 'Anulat',
      cancelled: 'Anulat',
      returned: 'Returnat',
      requires_action: 'Necesită acțiune',
      awaiting_payment: 'Așteaptă plata',
      confirmed: 'Confirmat',
      fulfilled: 'Îndeplinit',
      partially_fulfilled: 'Parțial îndeplinit',
      not_fulfilled: 'Neîndeplinit',
      draft: 'Proiect'
    };
    
    const normalizedStatus = status.toLowerCase().replace(/\s+/g, '_');
    return statusTranslations[normalizedStatus] || status;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="h-6 w-6 bg-gray-200 rounded"></div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    console.log('🔍 Dashboard: Error occurred:', error);
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-gray-900">Dashboard</h1>
          <p className="mt-2 text-sm text-gray-700">
            Welcome back! Here's what's happening with your store today.
          </p>
        </div>
        
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-800">
            Error loading dashboard data. Please try again.
          </div>
          <div className="mt-2 text-xs text-red-600">
            Details: {error instanceof Error ? error.message : 'Unknown error'}
          </div>
        </div>
        
        {/* Simple test content */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">🎉 Dashboard is Working!</h2>
          <p className="text-gray-600">The layout and routing are working properly, but there's an API error.</p>
        </div>
      </div>
    );
  }

  const stats = dashboardData?.stats;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold leading-tight text-gray-900">Dashboard</h1>
        <p className="mt-2 text-sm text-gray-700">
          Welcome back! Here's what's happening with your store today.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatCurrency(stats?.totalRevenue || 0)}
                    </div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      (stats?.revenueGrowth || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {(stats?.revenueGrowth || 0) >= 0 ? (
                        <ArrowUpIcon className="self-center flex-shrink-0 h-5 w-5" />
                      ) : (
                        <ArrowDownIcon className="self-center flex-shrink-0 h-5 w-5" />
                      )}
                      <span className="sr-only">
                        {(stats?.revenueGrowth || 0) >= 0 ? 'Increased' : 'Decreased'} by
                      </span>
                      {formatPercentage(stats?.revenueGrowth || 0)}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ShoppingBagIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {stats?.totalOrders || 0}
                    </div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      (stats?.ordersGrowth || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {(stats?.ordersGrowth || 0) >= 0 ? (
                        <ArrowUpIcon className="self-center flex-shrink-0 h-5 w-5" />
                      ) : (
                        <ArrowDownIcon className="self-center flex-shrink-0 h-5 w-5" />
                      )}
                      {formatPercentage(stats?.ordersGrowth || 0)}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UsersIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Customers</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {stats?.totalCustomers || 0}
                    </div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      (stats?.customersGrowth || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {(stats?.customersGrowth || 0) >= 0 ? (
                        <ArrowUpIcon className="self-center flex-shrink-0 h-5 w-5" />
                      ) : (
                        <ArrowDownIcon className="self-center flex-shrink-0 h-5 w-5" />
                      )}
                      {formatPercentage(stats?.customersGrowth || 0)}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Avg Order Value</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {formatCurrency(stats?.avgOrderValue || 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Sales Overview (Last 30 Days)
            </h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={dashboardData?.salesChart || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={(value) => format(new Date(value), 'MMM d')}
                  />
                  <YAxis 
                    yAxisId="left"
                    tickFormatter={(value) => formatCurrency(value)}
                  />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip 
                    labelFormatter={(value) => format(new Date(value), 'MMM d, yyyy')}
                    formatter={(value, name) => [
                      name === 'revenue' ? formatCurrency(Number(value)) : value,
                      name === 'revenue' ? 'Revenue' : 'Orders'
                    ]}
                  />
                  <Area
                    yAxisId="left"
                    type="monotone"
                    dataKey="revenue"
                    stackId="1"
                    stroke="#3B82F6"
                    fill="#3B82F6"
                    fillOpacity={0.1}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="orders"
                    stroke="#10B981"
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Orders by Status */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Orders by Status
            </h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={dashboardData?.ordersByStatus || []}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ status, percentage }) => `${formatOrderStatus(status)} (${percentage}%)`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {(dashboardData?.ordersByStatus || []).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Orders */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Recent Orders
              </h3>
              <Link 
                to="/orders" 
                className="text-sm text-indigo-600 hover:text-indigo-900"
              >
                View all
              </Link>
            </div>
            <div className="space-y-3">
              {(dashboardData?.recentOrders || []).slice(0, 5).map((order) => (
                <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <div className="text-sm font-medium text-gray-900">
                        Comanda #{order.display_id}
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                        order.status === 'completed' || order.status === 'delivered' 
                          ? 'bg-green-100 text-green-800' 
                          : order.status === 'pending' 
                          ? 'bg-yellow-100 text-yellow-800'
                          : order.status === 'canceled' || order.status === 'cancelled'
                          ? 'bg-red-100 text-red-800'
                          : order.status === 'shipped'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {formatOrderStatus(order.status)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {order.customer 
                        ? `${order.customer.first_name || ''} ${order.customer.last_name || ''}`.trim()
                        : 'Client invitat'
                      }
                    </div>
                    <div className="text-xs text-gray-400">
                      {format(new Date(order.created_at), 'dd MMM yyyy, HH:mm')}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(order.total || 0, order.currency_code)}
                    </div>
                  </div>
                </div>
              ))}
              
              {(dashboardData?.recentOrders || []).length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>Nu există comenzi recente</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Top Products
              </h3>
              <Link 
                to="/analytics/products" 
                className="text-sm text-indigo-600 hover:text-indigo-900"
              >
                View all
              </Link>
            </div>
            <div className="space-y-3">
              {(dashboardData?.topProducts || []).slice(0, 5).map((product) => (
                <div key={product.id} className="flex items-center">
                  <div className="h-10 w-10 flex-shrink-0">
                    {product.thumbnail ? (
                      <img 
                        className="h-10 w-10 rounded-lg object-cover" 
                        src={product.thumbnail} 
                        alt=""
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                        <span className="text-xs text-gray-500">No image</span>
                      </div>
                    )}
                  </div>
                  <div className="ml-4 flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {product.title}
                    </div>
                    <div className="text-sm text-gray-500">
                      {product.sales} sales
                    </div>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    {formatCurrency(product.revenue)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Low Stock Alert */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Low Stock Alert
              </h3>
              <Link 
                to="/inventory" 
                className="text-sm text-indigo-600 hover:text-indigo-900"
              >
                View all
              </Link>
            </div>
            {(dashboardData?.lowStockProducts || []).length === 0 ? (
              <div className="text-center py-4">
                <div className="text-sm text-gray-500">All products are well stocked!</div>
              </div>
            ) : (
              <div className="space-y-3">
                {(dashboardData?.lowStockProducts || []).slice(0, 5).map((product) => (
                  <div key={product.id} className="flex items-center">
                    <div className="flex-shrink-0">
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3 flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {product.title}
                      </div>
                      <div className="text-sm text-gray-500">
                        {product.inventory_quantity} left
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard; 