import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  CubeIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface InventoryItem {
  id: string;
  sku: string;
  title: string;
  description?: string;
  thumbnail?: string;
  origin_country?: string;
  hs_code?: string;
  mid_code?: string;
  material?: string;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  requires_shipping: boolean;
  metadata?: any;
  total_quantity?: number;
  available_quantity?: number;
  reserved_quantity?: number;
  locations?: InventoryLocation[];
  created_at: string;
  updated_at: string;
}

interface InventoryLocation {
  location_id: string;
  location_name: string;
  stocked_quantity: number;
  reserved_quantity: number;
  available_quantity: number;
}

interface CreateItemData {
  sku: string;
  title: string;
  description?: string;
  thumbnail?: string;
  origin_country?: string;
  hs_code?: string;
  mid_code?: string;
  material?: string;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  requires_shipping: boolean;
  metadata?: any;
}

const InventoryItems: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [lowStockFilter, setLowStockFilter] = useState(false);
  const [requiresShippingFilter, setRequiresShippingFilter] = useState('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [formData, setFormData] = useState<CreateItemData>({
    sku: '',
    title: '',
    description: '',
    thumbnail: '',
    origin_country: '',
    hs_code: '',
    mid_code: '',
    material: '',
    weight: 0,
    length: 0,
    height: 0,
    width: 0,
    requires_shipping: true,
  });

  const queryClient = useQueryClient();

  // Fetch inventory items
  const { data: itemsData, isLoading, error } = useQuery({
    queryKey: ['inventory-items', currentPage, searchTerm, lowStockFilter, requiresShippingFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
      });
      
      if (searchTerm) params.append('search', searchTerm);
      if (lowStockFilter) params.append('low_stock', 'true');
      if (requiresShippingFilter !== 'all') params.append('requires_shipping', requiresShippingFilter);

      const response = await api.get(`${endpoints.inventory.items}?${params}`);
      return response.data;
    },
  });

  // Create item mutation
  const createItemMutation = useMutation({
    mutationFn: async (data: CreateItemData) => {
      return api.post(endpoints.inventory.createItem, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory-items'] });
      toast.success('Inventory item created successfully');
      setIsCreateModalOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create inventory item');
    },
  });

  // Update item mutation
  const updateItemMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: CreateItemData }) => {
      return api.put(endpoints.inventory.updateItem(id), data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory-items'] });
      toast.success('Inventory item updated successfully');
      setEditingItem(null);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update inventory item');
    },
  });

  // Delete item mutation
  const deleteItemMutation = useMutation({
    mutationFn: async (id: string) => {
      return api.delete(endpoints.inventory.deleteItem(id));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory-items'] });
      toast.success('Inventory item deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete inventory item');
    },
  });

  const resetForm = () => {
    setFormData({
      sku: '',
      title: '',
      description: '',
      thumbnail: '',
      origin_country: '',
      hs_code: '',
      mid_code: '',
      material: '',
      weight: 0,
      length: 0,
      height: 0,
      width: 0,
      requires_shipping: true,
    });
  };

  const handleCreateItem = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.sku.trim() || !formData.title.trim()) {
      toast.error('SKU and title are required');
      return;
    }
    createItemMutation.mutate(formData);
  };

  const handleUpdateItem = (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingItem || !formData.sku.trim() || !formData.title.trim()) {
      toast.error('SKU and title are required');
      return;
    }
    updateItemMutation.mutate({ id: editingItem.id, data: formData });
  };

  const handleDeleteItem = (id: string) => {
    if (confirm('Are you sure you want to delete this inventory item? This action cannot be undone.')) {
      deleteItemMutation.mutate(id);
    }
  };

  const handleEdit = (item: InventoryItem) => {
    setEditingItem(item);
    setFormData({
      sku: item.sku,
      title: item.title,
      description: item.description || '',
      thumbnail: item.thumbnail || '',
      origin_country: item.origin_country || '',
      hs_code: item.hs_code || '',
      mid_code: item.mid_code || '',
      material: item.material || '',
      weight: item.weight || 0,
      length: item.length || 0,
      height: item.height || 0,
      width: item.width || 0,
      requires_shipping: item.requires_shipping,
      metadata: item.metadata,
    });
    setIsCreateModalOpen(true);
  };

  const getStockStatus = (item: InventoryItem) => {
    const available = item.available_quantity || 0;
    if (available === 0) return { status: 'out-of-stock', color: 'text-red-600', bg: 'bg-red-100' };
    if (available < 10) return { status: 'low-stock', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    return { status: 'in-stock', color: 'text-green-600', bg: 'bg-green-100' };
  };

  const toggleItemSelection = (itemId: string) => {
    setSelectedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedItems.length === itemsData?.items?.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(itemsData?.items?.map((item: InventoryItem) => item.id) || []);
    }
  };

  const items = itemsData?.items || [];
  const totalPages = Math.ceil((itemsData?.total || 0) / 20);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load inventory items. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Inventory Items</h1>
          <p className="text-gray-600">Manage your inventory items and track stock levels</p>
        </div>
        <button
          onClick={() => {
            setEditingItem(null);
            resetForm();
            setIsCreateModalOpen(true);
          }}
          className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Item
        </button>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search by SKU, title, or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={lowStockFilter}
            onChange={(e) => setLowStockFilter(e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700">Low Stock Only</span>
        </label>

        <select
          value={requiresShippingFilter}
          onChange={(e) => setRequiresShippingFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Items</option>
          <option value="true">Requires Shipping</option>
          <option value="false">Digital Items</option>
        </select>
      </div>

      {/* Items List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedItems.length === items.length && items.length > 0}
                onChange={toggleSelectAll}
                className="mr-3"
              />
              <h3 className="text-lg font-medium text-gray-900">
                Inventory Items ({itemsData?.total || 0})
              </h3>
            </div>
            {selectedItems.length > 0 && (
              <span className="text-sm text-gray-600">
                {selectedItems.length} selected
              </span>
            )}
          </div>
        </div>

        {items.length === 0 ? (
          <div className="text-center py-12">
            <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No inventory items found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'No items match your search criteria.' : 'Get started by adding your first inventory item.'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => {
                  setEditingItem(null);
                  resetForm();
                  setIsCreateModalOpen(true);
                }}
                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
              >
                Add Item
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {items.map((item: InventoryItem) => {
              const stockStatus = getStockStatus(item);
              
              return (
                <div key={item.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(item.id)}
                        onChange={() => toggleItemSelection(item.id)}
                        className="mr-3"
                      />
                      
                      <div className="flex items-start space-x-4">
                        {item.thumbnail ? (
                          <img
                            src={item.thumbnail}
                            alt={item.title}
                            className="w-12 h-12 object-cover rounded-md"
                          />
                        ) : (
                          <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                            <CubeIcon className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <h4 className="font-medium text-gray-900">{item.title}</h4>
                            <span className="text-sm text-gray-500">SKU: {item.sku}</span>
                            {!item.requires_shipping && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Digital
                              </span>
                            )}
                          </div>
                          
                          {item.description && (
                            <p className="text-sm text-gray-500 mt-1">{item.description}</p>
                          )}
                          
                          <div className="flex items-center space-x-4 mt-2 text-sm">
                            <div className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${stockStatus.bg} ${stockStatus.color}`}>
                              {stockStatus.status === 'out-of-stock' && <ExclamationTriangleIcon className="h-3 w-3 mr-1" />}
                              {stockStatus.status === 'low-stock' && <ClockIcon className="h-3 w-3 mr-1" />}
                              {stockStatus.status === 'in-stock' && <CheckCircleIcon className="h-3 w-3 mr-1" />}
                              {item.available_quantity || 0} available
                            </div>
                            
                            {item.reserved_quantity && item.reserved_quantity > 0 && (
                              <span className="text-gray-500">
                                {item.reserved_quantity} reserved
                              </span>
                            )}
                            
                            {item.locations && item.locations.length > 0 && (
                              <span className="text-gray-500">
                                {item.locations.length} location{item.locations.length !== 1 ? 's' : ''}
                              </span>
                            )}
                            
                            <span className="text-gray-400">
                              Created {new Date(item.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEdit(item)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteItem(item.id)}
                        disabled={deleteItemMutation.isPending}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {(currentPage - 1) * 20 + 1} to {Math.min(currentPage * 20, itemsData?.total || 0)} of {itemsData?.total || 0} items
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create/Edit Item Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-96 overflow-y-auto">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {editingItem ? 'Edit Inventory Item' : 'Create New Inventory Item'}
            </h3>
            <form onSubmit={editingItem ? handleUpdateItem : handleCreateItem}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SKU *
                  </label>
                  <input
                    type="text"
                    value={formData.sku}
                    onChange={(e) => setFormData({ ...formData, sku: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter SKU"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter title"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter description"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Origin Country
                  </label>
                  <input
                    type="text"
                    value={formData.origin_country}
                    onChange={(e) => setFormData({ ...formData, origin_country: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., US, CN, DE"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Material
                  </label>
                  <input
                    type="text"
                    value={formData.material}
                    onChange={(e) => setFormData({ ...formData, material: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Cotton, Metal, Plastic"
                  />
                </div>

                <div className="grid grid-cols-4 gap-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Weight (g)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.weight}
                      onChange={(e) => setFormData({ ...formData, weight: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Length (cm)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.length}
                      onChange={(e) => setFormData({ ...formData, length: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Width (cm)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.width}
                      onChange={(e) => setFormData({ ...formData, width: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Height (cm)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.height}
                      onChange={(e) => setFormData({ ...formData, height: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.requires_shipping}
                      onChange={(e) => setFormData({ ...formData, requires_shipping: e.target.checked })}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Requires Shipping</span>
                  </label>
                  <p className="text-xs text-gray-500 mt-1">
                    Uncheck for digital products that don't need physical shipping
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setIsCreateModalOpen(false);
                    setEditingItem(null);
                    resetForm();
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createItemMutation.isPending || updateItemMutation.isPending}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {createItemMutation.isPending || updateItemMutation.isPending 
                    ? 'Saving...' 
                    : editingItem 
                      ? 'Update Item' 
                      : 'Create Item'
                  }
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryItems; 