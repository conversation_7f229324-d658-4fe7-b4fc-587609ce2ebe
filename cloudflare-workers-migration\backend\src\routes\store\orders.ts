import { Hono } from 'hono';
import { WorkerEnv } from 'handmadein-shared';
import { DatabaseService } from '../../services/database';
import { z } from 'zod';
import { verify } from '@tsndr/cloudflare-worker-jwt';

export const orderRoutes = new Hono<{ Bindings: WorkerEnv }>();

// Validation schemas
const createOrderSchema = z.object({
  cart_id: z.string(),
  customer_id: z.string().optional(),
  email: z.string().email(),
  billing_address: z.object({
    first_name: z.string(),
    last_name: z.string(),
    address_1: z.string(),
    city: z.string(),
    country_code: z.string(),
    postal_code: z.string(),
    phone: z.string().optional(),
  }),
  shipping_address: z.object({
    first_name: z.string(),
    last_name: z.string(),
    address_1: z.string(),
    city: z.string(),
    country_code: z.string(),
    postal_code: z.string(),
    phone: z.string().optional(),
  }).optional(),
  shipping_method_id: z.string().optional(),
  payment_method: z.enum(['stripe', 'cash_on_delivery']),
});

// Create order from cart
orderRoutes.post('/', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const body = await c.req.json();
    
    const validatedData = createOrderSchema.parse(body);

    // Get cart with items
    const cart = await db.findCartWithItems(validatedData.cart_id);
    if (!cart) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    if (!cart.items || cart.items.length === 0) {
      return c.json({
        success: false,
        error: 'Cart is empty',
      }, 400);
    }

    // Create billing address
    const billingAddress = await db.create('addresses', {
      ...validatedData.billing_address,
      customer_id: validatedData.customer_id,
    });

    // Create shipping address (use billing if not provided)
    const shippingAddressData = validatedData.shipping_address || validatedData.billing_address;
    const shippingAddress = await db.create('addresses', {
      ...shippingAddressData,
      customer_id: validatedData.customer_id,
    });

    // Calculate order totals
    let subtotal = 0;
    for (const item of cart.items) {
      subtotal += item.unit_price * item.quantity;
    }

    const taxRate = 0.19; // 19% VAT for Romania
    const taxTotal = Math.round(subtotal * taxRate);
    const shippingTotal = 1500; // 15 RON default shipping
    const total = subtotal + taxTotal + shippingTotal;

    // Generate display ID
    const orderCount = await db.count('orders');
    const displayId = orderCount + 1;

    // Create order
    const order = await db.create('orders', {
      status: 'pending',
      fulfillment_status: 'not_fulfilled',
      payment_status: validatedData.payment_method === 'cash_on_delivery' ? 'not_paid' : 'awaiting',
      display_id: displayId,
      cart_id: validatedData.cart_id,
      customer_id: validatedData.customer_id,
      email: validatedData.email,
      billing_address_id: billingAddress.id,
      shipping_address_id: shippingAddress.id,
      region_id: cart.region_id,
      currency_code: 'RON',
      tax_rate: taxRate,
      metadata: JSON.stringify({
        payment_method: validatedData.payment_method,
        subtotal,
        tax_total: taxTotal,
        shipping_total: shippingTotal,
        total,
      }),
    });

    // Move line items from cart to order
    for (const item of cart.items) {
      await db.update('lineItems', item.id, {
        order_id: order.id,
        cart_id: null,
      });
    }

    // Mark cart as completed
    await db.update('carts', cart.id, {
      completed_at: new Date().toISOString(),
    });

    // Create payment session if needed
    if (validatedData.payment_method === 'stripe') {
      // This would integrate with Stripe
      // For now, just create a placeholder payment session
      await db.create('paymentSessions', {
        cart_id: cart.id,
        provider_id: 'stripe',
        is_selected: true,
        is_initiated: false,
        status: 'pending',
        data: JSON.stringify({
          amount: total,
          currency: 'ron',
        }),
        amount: total,
      });
    }

    // Get the complete order with items
    const completeOrder = await db.findById('orders', order.id);
    
    // Get order items with product details using JOIN - only latest version of each item
    const itemsQuery = `
      SELECT 
        oi.*,
        oli.title,
        oli.subtitle,
        oli.thumbnail,
        oli.variant_id,
        oli.product_id,
        oli.product_title,
        oli.product_description,
        oli.product_subtitle,
        oli.product_type,
        oli.product_handle,
        oli.variant_sku,
        oli.variant_barcode,
        oli.variant_title,
        oli.variant_option_values,
        oli.requires_shipping,
        oli.is_discountable,
        oli.is_tax_inclusive
      FROM "order_item" oi
      LEFT JOIN "order_line_item" oli ON oi.item_id = oli.id
      WHERE oi.order_id = ? 
        AND (oi.deleted_at IS NULL OR oi.deleted_at = '')
        AND oi.id IN (
          SELECT id FROM "order_item" oi2 
          WHERE oi2.order_id = oi.order_id 
            AND oi2.item_id = oi.item_id 
            AND (oi2.deleted_at IS NULL OR oi2.deleted_at = '')
          ORDER BY oi2.version DESC 
          LIMIT 1
        )
    `;
    const itemsResult = await c.env.DB.prepare(itemsQuery).bind(order.id).all();
    completeOrder.items = itemsResult.results || [];

    return c.json({
      success: true,
      data: completeOrder,
    });
  } catch (error) {
    console.error('Error creating order:', error);
    return c.json({
      success: false,
      error: error instanceof z.ZodError ? 'Invalid input data' : 'Failed to create order',
    }, 400);
  }
});

// Get order by ID
orderRoutes.get('/:id', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const orderId = c.req.param('id');

    const order = await db.findById('orders', orderId);
    if (!order) {
      return c.json({
        success: false,
        error: 'Order not found',
      }, 404);
    }

    // Get order items
    const itemsQuery = `
      SELECT 
        oi.*,
        oli.title,
        oli.subtitle,
        oli.thumbnail,
        oli.variant_id,
        oli.product_id,
        oli.product_title,
        oli.product_description,
        oli.product_subtitle,
        oli.product_type,
        oli.product_handle,
        oli.variant_sku,
        oli.variant_barcode,
        oli.variant_title,
        oli.variant_option_values,
        oli.requires_shipping,
        oli.is_discountable,
        oli.is_tax_inclusive
      FROM "order_item" oi
      LEFT JOIN "order_line_item" oli ON oi.item_id = oli.id
      WHERE oi.order_id = ? 
        AND (oi.deleted_at IS NULL OR oi.deleted_at = '')
        AND oi.id IN (
          SELECT id FROM "order_item" oi2 
          WHERE oi2.order_id = oi.order_id 
            AND oi2.item_id = oi.item_id 
            AND (oi2.deleted_at IS NULL OR oi2.deleted_at = '')
          ORDER BY oi2.version DESC 
          LIMIT 1
        )
    `;
    const itemsResult = await c.env.DB.prepare(itemsQuery).bind(orderId).all();
    order.items = itemsResult.results || [];

    // Calculate totals from items if order total is missing or zero
    let calculatedTotal = 0;
    if (order.items && order.items.length > 0) {
      calculatedTotal = order.items.reduce((sum: number, item: any) => {
        const unitPrice = item.unit_price || 0;
        const quantity = item.quantity || 0;
        return sum + (unitPrice * quantity);
      }, 0);
    }
    
    // Use calculated total if database total is null/zero, otherwise keep existing total
    if (!order.total || order.total === 0) {
      order.total = calculatedTotal;
    }
    
    // Ensure currency_code is set
    if (!order.currency_code) {
      order.currency_code = 'RON';
    }

    // Get addresses
    if (order.billing_address_id) {
      order.billing_address = await db.findById('addresses', order.billing_address_id);
    }
    if (order.shipping_address_id) {
      order.shipping_address = await db.findById('addresses', order.shipping_address_id);
    }

    return c.json({
      success: true,
      data: order,
    });
  } catch (error) {
    console.error('Error fetching order:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch order',
    }, 500);
  }
});

// Get orders for customer
orderRoutes.get('/', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    let customerId = c.req.query('customer_id');
    let email = c.req.query('email');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;

    // Check for JWT authentication first
    const authHeader = c.req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const isValid = await verify(token, c.env.JWT_SECRET);
        if (isValid) {
          const payload = JSON.parse(atob(token.split('.')[1]));
          customerId = payload.userId || payload.sub; // Support both userId and sub claims
          email = payload.email;
        }
      } catch (tokenError) {
        console.error('Error verifying JWT token:', tokenError);
        // Continue with query params if token verification fails
      }
    }

    if (!customerId && !email) {
      return c.json({
        success: false,
        error: 'Customer ID or email is required',
      }, 400);
    }

    // Get customer orders using raw SQL to match the pattern used in customers.ts
    let ordersQuery;
    let bindParams;
    
    if (customerId) {
      ordersQuery = `
        SELECT * FROM "order" 
        WHERE customer_id = ? AND (deleted_at IS NULL OR deleted_at = '')
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;
      bindParams = [customerId, limit, offset];
    } else {
      ordersQuery = `
        SELECT * FROM "order" 
        WHERE email = ? AND (deleted_at IS NULL OR deleted_at = '')
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;
      bindParams = [email, limit, offset];
    }

    const ordersResult = await c.env.DB.prepare(ordersQuery).bind(...bindParams).all();
    const orders = ordersResult.results || [];

    // Get items for each order using raw SQL and calculate totals
    for (const order of orders) {
      const itemsQuery = `
        SELECT 
          oi.*,
          oli.title,
          oli.subtitle,
          oli.thumbnail,
          oli.variant_id,
          oli.product_id,
          oli.product_title,
          oli.product_description,
          oli.product_subtitle,
          oli.product_type,
          oli.product_handle,
          oli.variant_sku,
          oli.variant_barcode,
          oli.variant_title,
          oli.variant_option_values,
          oli.requires_shipping,
          oli.is_discountable,
          oli.is_tax_inclusive
        FROM "order_item" oi
        LEFT JOIN "order_line_item" oli ON oi.item_id = oli.id
        WHERE oi.order_id = ? 
          AND (oi.deleted_at IS NULL OR oi.deleted_at = '')
          AND oi.id IN (
            SELECT id FROM "order_item" oi2 
            WHERE oi2.order_id = oi.order_id 
              AND oi2.item_id = oi.item_id 
              AND (oi2.deleted_at IS NULL OR oi2.deleted_at = '')
            ORDER BY oi2.version DESC 
            LIMIT 1
          )
      `;
      const itemsResult = await c.env.DB.prepare(itemsQuery).bind(order.id).all();
      order.items = itemsResult.results || [];
      
      // Calculate totals from items (similar to admin dashboard)
      let calculatedTotal = 0;
      if (order.items && order.items.length > 0) {
        calculatedTotal = order.items.reduce((sum: number, item: any) => {
          const unitPrice = item.unit_price || 0;
          const quantity = item.quantity || 0;
          return sum + (unitPrice * quantity);
        }, 0);
      }
      
      // Use calculated total if database total is null/zero, otherwise keep existing total
      if (!order.total || order.total === 0) {
        order.total = calculatedTotal;
      }
      
      // Ensure currency_code is set
      if (!order.currency_code) {
        order.currency_code = 'RON';
      }
    }

    // Get total count for pagination
    let countQuery;
    let countParams;
    
    if (customerId) {
      countQuery = `SELECT COUNT(*) as count FROM "order" WHERE customer_id = ? AND (deleted_at IS NULL OR deleted_at = '')`;
      countParams = [customerId];
    } else {
      countQuery = `SELECT COUNT(*) as count FROM "order" WHERE email = ? AND (deleted_at IS NULL OR deleted_at = '')`;
      countParams = [email];
    }

    const countResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();
    const total = countResult?.count || 0;

    return c.json({
      success: true,
      data: orders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch orders',
    }, 500);
  }
});

// Cancel order
orderRoutes.post('/:id/cancel', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const orderId = c.req.param('id');

    const order = await db.findById('orders', orderId);
    if (!order) {
      return c.json({
        success: false,
        error: 'Order not found',
      }, 404);
    }

    if (order.status === 'completed' || order.status === 'canceled') {
      return c.json({
        success: false,
        error: 'Order cannot be canceled',
      }, 400);
    }

    const updatedOrder = await db.update('orders', orderId, {
      status: 'canceled',
      fulfillment_status: 'canceled',
    });

    return c.json({
      success: true,
      data: updatedOrder,
    });
  } catch (error) {
    console.error('Error canceling order:', error);
    return c.json({
      success: false,
      error: 'Failed to cancel order',
    }, 500);
  }
}); 