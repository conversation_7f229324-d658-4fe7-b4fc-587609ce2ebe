CREATE TABLE `activity_logs` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`user_id` text NOT NULL,
	`user_type` text NOT NULL,
	`action` text NOT NULL,
	`resource_type` text NOT NULL,
	`resource_id` text,
	`details` text,
	`ip_address` text,
	`user_agent` text
);
--> statement-breakpoint
CREATE TABLE `addresses` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`customer_id` text,
	`company` text,
	`first_name` text,
	`last_name` text,
	`address_1` text,
	`address_2` text,
	`city` text,
	`country_code` text,
	`province` text,
	`postal_code` text,
	`phone` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `admin_users` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`email` text NOT NULL,
	`password_hash` text NOT NULL,
	`first_name` text,
	`last_name` text,
	`role` text DEFAULT 'viewer' NOT NULL,
	`permissions` text,
	`last_login` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `carts` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`email` text,
	`billing_address_id` text,
	`shipping_address_id` text,
	`region_id` text NOT NULL,
	`customer_id` text,
	`payment_id` text,
	`type` text DEFAULT 'default' NOT NULL,
	`completed_at` text,
	`payment_authorized_at` text,
	`idempotency_key` text,
	`context` text,
	`sales_channel_id` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `collection_images` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`collection_id` text NOT NULL,
	`image_url` text NOT NULL,
	`alt_text` text,
	`sort_order` integer DEFAULT 0
);
--> statement-breakpoint
CREATE TABLE `countries` (
	`id` text PRIMARY KEY NOT NULL,
	`iso_2` text NOT NULL,
	`iso_3` text NOT NULL,
	`num_code` integer NOT NULL,
	`name` text NOT NULL,
	`display_name` text NOT NULL,
	`region_id` text
);
--> statement-breakpoint
CREATE TABLE `customer_group_customers` (
	`customer_group_id` text NOT NULL,
	`customer_id` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `customer_groups` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`name` text NOT NULL,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `customers` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`email` text NOT NULL,
	`first_name` text,
	`last_name` text,
	`billing_address_id` text,
	`phone` text,
	`has_account` integer DEFAULT false NOT NULL,
	`password_hash` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `email_templates` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`name` text NOT NULL,
	`subject` text NOT NULL,
	`html` text NOT NULL,
	`text` text,
	`variables` text,
	`active` integer DEFAULT true
);
--> statement-breakpoint
CREATE TABLE `file_uploads` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`filename` text NOT NULL,
	`original_name` text NOT NULL,
	`mimetype` text NOT NULL,
	`size` integer NOT NULL,
	`url` text NOT NULL,
	`bucket` text NOT NULL,
	`key` text NOT NULL,
	`uploaded_by` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `fulfillments` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`claim_order_id` text,
	`swap_id` text,
	`order_id` text,
	`tracking_numbers` text,
	`data` text NOT NULL,
	`shipped_at` text,
	`canceled_at` text,
	`metadata` text,
	`provider_id` text NOT NULL,
	`location_id` text,
	`no_notification` integer
);
--> statement-breakpoint
CREATE TABLE `journal_entries` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`title` text NOT NULL,
	`content` text NOT NULL,
	`excerpt` text,
	`slug` text NOT NULL,
	`author` text,
	`image_url` text,
	`published` integer DEFAULT false NOT NULL,
	`published_at` text,
	`tags` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `line_items` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`cart_id` text,
	`order_id` text,
	`swap_id` text,
	`claim_order_id` text,
	`original_item_id` text,
	`order_edit_id` text,
	`title` text NOT NULL,
	`description` text,
	`thumbnail` text,
	`is_return` integer DEFAULT false NOT NULL,
	`is_giftcard` integer DEFAULT false NOT NULL,
	`should_merge` integer DEFAULT true NOT NULL,
	`allow_discounts` integer DEFAULT true NOT NULL,
	`has_shipping` integer,
	`unit_price` integer NOT NULL,
	`variant_id` text,
	`quantity` integer NOT NULL,
	`fulfilled_quantity` integer DEFAULT 0,
	`returned_quantity` integer DEFAULT 0,
	`shipped_quantity` integer DEFAULT 0,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `notification_events` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`type` text NOT NULL,
	`data` text NOT NULL,
	`recipient` text NOT NULL,
	`template_id` text,
	`status` text DEFAULT 'pending' NOT NULL,
	`sent_at` text,
	`error_message` text
);
--> statement-breakpoint
CREATE TABLE `orders` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`status` text DEFAULT 'pending' NOT NULL,
	`fulfillment_status` text DEFAULT 'not_fulfilled' NOT NULL,
	`payment_status` text DEFAULT 'not_paid' NOT NULL,
	`display_id` integer NOT NULL,
	`cart_id` text,
	`customer_id` text NOT NULL,
	`email` text NOT NULL,
	`billing_address_id` text,
	`shipping_address_id` text,
	`region_id` text NOT NULL,
	`currency_code` text NOT NULL,
	`tax_rate` real,
	`draft_order_id` text,
	`no_notification` integer,
	`idempotency_key` text,
	`external_id` text,
	`sales_channel_id` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `payment_sessions` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`cart_id` text,
	`provider_id` text NOT NULL,
	`is_selected` integer,
	`is_initiated` integer DEFAULT false NOT NULL,
	`status` text NOT NULL,
	`data` text NOT NULL,
	`amount` integer,
	`payment_authorized_at` text
);
--> statement-breakpoint
CREATE TABLE `payments` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`swap_id` text,
	`cart_id` text,
	`order_id` text,
	`amount` integer NOT NULL,
	`currency_code` text NOT NULL,
	`amount_refunded` integer DEFAULT 0 NOT NULL,
	`provider_id` text NOT NULL,
	`data` text NOT NULL,
	`captured_at` text,
	`canceled_at` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `product_collections` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`title` text NOT NULL,
	`handle` text NOT NULL,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `product_images` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`product_id` text NOT NULL,
	`url` text NOT NULL,
	`alt_text` text,
	`sort_order` integer DEFAULT 0
);
--> statement-breakpoint
CREATE TABLE `product_product_tags` (
	`product_id` text NOT NULL,
	`tag_id` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `product_sales` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`product_id` text NOT NULL,
	`total_sales` integer DEFAULT 0 NOT NULL,
	`quantity_sold` integer DEFAULT 0 NOT NULL,
	`revenue` integer DEFAULT 0 NOT NULL,
	`last_sale_at` text
);
--> statement-breakpoint
CREATE TABLE `product_tags` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`value` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `product_variants` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`title` text NOT NULL,
	`product_id` text NOT NULL,
	`sku` text,
	`barcode` text,
	`ean` text,
	`upc` text,
	`variant_rank` integer,
	`inventory_quantity` integer DEFAULT 0 NOT NULL,
	`allow_backorder` integer DEFAULT false NOT NULL,
	`manage_inventory` integer DEFAULT true NOT NULL,
	`hs_code` text,
	`origin_country` text,
	`mid_code` text,
	`material` text,
	`weight` real,
	`length` real,
	`height` real,
	`width` real,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `products` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`title` text NOT NULL,
	`subtitle` text,
	`description` text,
	`handle` text NOT NULL,
	`is_giftcard` integer DEFAULT false NOT NULL,
	`status` text DEFAULT 'draft' NOT NULL,
	`thumbnail` text,
	`weight` real,
	`length` real,
	`height` real,
	`width` real,
	`hs_code` text,
	`origin_country` text,
	`mid_code` text,
	`material` text,
	`collection_id` text,
	`type_id` text,
	`discountable` integer DEFAULT true NOT NULL,
	`external_id` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `refunds` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`order_id` text,
	`payment_id` text NOT NULL,
	`amount` integer NOT NULL,
	`note` text,
	`reason` text NOT NULL,
	`metadata` text,
	`idempotency_key` text
);
--> statement-breakpoint
CREATE TABLE `regions` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`name` text NOT NULL,
	`currency_code` text NOT NULL,
	`tax_rate` real DEFAULT 0,
	`tax_code` text,
	`gift_cards_taxable` integer DEFAULT true,
	`automatic_taxes` integer DEFAULT true,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `returns` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`status` text DEFAULT 'requested' NOT NULL,
	`order_id` text NOT NULL,
	`swap_id` text,
	`claim_order_id` text,
	`shipping_data` text,
	`refund_amount` integer NOT NULL,
	`received_at` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `sessions` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`user_type` text NOT NULL,
	`expires_at` text NOT NULL,
	`data` text,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE `settings` (
	`key` text PRIMARY KEY NOT NULL,
	`value` text NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE `shipping_methods` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`shipping_option_id` text NOT NULL,
	`order_id` text,
	`cart_id` text,
	`swap_id` text,
	`return_id` text,
	`claim_order_id` text,
	`price` integer NOT NULL,
	`data` text NOT NULL,
	`includes_tax` integer DEFAULT false,
	`subtotal` integer NOT NULL,
	`total` integer NOT NULL,
	`tax_total` integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE `shipping_options` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`name` text NOT NULL,
	`region_id` text NOT NULL,
	`profile_id` text NOT NULL,
	`provider_id` text NOT NULL,
	`price_type` text NOT NULL,
	`amount` integer,
	`is_return` integer DEFAULT false,
	`admin_only` integer DEFAULT false,
	`data` text,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `trusted_shop_reviews` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`rating` integer NOT NULL,
	`comment` text,
	`customer_name` text,
	`product_id` text,
	`order_id` text,
	`verified` integer DEFAULT false,
	`external_id` text
);
--> statement-breakpoint
CREATE UNIQUE INDEX `admin_users_email_unique` ON `admin_users` (`email`);--> statement-breakpoint
CREATE UNIQUE INDEX `customers_email_unique` ON `customers` (`email`);--> statement-breakpoint
CREATE UNIQUE INDEX `email_templates_name_unique` ON `email_templates` (`name`);--> statement-breakpoint
CREATE UNIQUE INDEX `journal_entries_slug_unique` ON `journal_entries` (`slug`);--> statement-breakpoint
CREATE UNIQUE INDEX `product_collections_handle_unique` ON `product_collections` (`handle`);--> statement-breakpoint
CREATE UNIQUE INDEX `products_handle_unique` ON `products` (`handle`);