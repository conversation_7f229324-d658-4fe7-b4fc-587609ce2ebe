const bcrypt = require('bcryptjs');

async function testHash() {
  const password = 'admin123';
  
  // Generate a new hash
  const newHash = await bcrypt.hash(password, 12);
  console.log('New hash for "admin123":', newHash);
  
  // Test the old hash from seed file
  const oldHash = '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/Lj8.X9zFq4mWjHhOyH9oC';
  const isOldValid = await bcrypt.compare(password, oldHash);
  console.log('Old hash valid:', isOldValid);
  
  // Test the new hash
  const isNewValid = await bcrypt.compare(password, newHash);
  console.log('New hash valid:', isNewValid);
  
  console.log('\nUse this SQL to update the admin user:');
  console.log(`UPDATE admin_users SET password_hash = '${newHash}' WHERE email = '<EMAIL>';`);
}

testHash().catch(console.error); 