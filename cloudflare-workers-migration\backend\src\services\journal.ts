import { WorkerEnv, JournalEntry } from 'handmadein-shared';
import { DatabaseService } from './database';

export class JournalService extends DatabaseService {
  constructor(env: WorkerEnv) {
    super(env);
  }

  /**
   * Create a new journal entry
   */
  async createJournalEntry(data: {
    title: string;
    content: string;
    excerpt?: string;
    author?: string;
    image_url?: string;
    published?: boolean;
    published_at?: Date;
    tags?: string[];
    metadata?: Record<string, any>;
  }): Promise<JournalEntry> {
    // Generate slug from title
    const slug = this.generateSlug(data.title);
    
    // Generate excerpt from content if not provided
    const excerpt = data.excerpt || this.generateExcerpt(data.content);
    
    // Set default values
    const author = data.author || "Anonymous";
    const image_url = data.image_url || "";
    const published_at = data.published_at || new Date();
    const published = data.published ?? false;
    
    const entry: Omit<JournalEntry, 'id' | 'created_at' | 'updated_at'> = {
      title: data.title,
      content: data.content,
      excerpt,
      slug,
      author,
      image_url,
      published,
      published_at,
      tags: data.tags || [],
      metadata: data.metadata || {},
      deleted_at: undefined
    };

    const result = await this.create('journal_entries', entry);
    return result as JournalEntry;
  }

  /**
   * Update a journal entry
   */
  async updateJournalEntry(
    id: string,
    data: Partial<{
      title: string;
      content: string;
      excerpt: string;
      author: string;
      image_url: string;
      published: boolean;
      published_at: Date;
      tags: string[];
      metadata: Record<string, any>;
      slug: string;
    }>
  ): Promise<JournalEntry> {
    let updateData = { ...data };
    
    // If title is updated, update slug as well (unless slug is explicitly provided)
    if (data.title && !data.slug) {
      updateData.slug = this.generateSlug(data.title);
    }
    
    // If content is updated but excerpt isn't, generate a new excerpt
    if (data.content && !data.excerpt) {
      updateData.excerpt = this.generateExcerpt(data.content);
    }
    
    // Ensure published_at is set
    if (!updateData.published_at) {
      const entry = await this.getById('journal_entries', id);
      updateData.published_at = entry.published_at || new Date();
    }

    const result = await this.update('journal_entries', id, updateData);
    return result as JournalEntry;
  }

  /**
   * Get a single journal entry by ID
   */
  async getJournalEntry(id: string): Promise<JournalEntry | null> {
    return this.getById('journal_entries', id) as Promise<JournalEntry | null>;
  }

  /**
   * Get a single journal entry by slug
   */
  async getJournalEntryBySlug(slug: string): Promise<JournalEntry | null> {
    try {
      const stmt = this.env.DB.prepare(`
        SELECT * FROM journal_entries 
        WHERE slug = ? AND deleted_at IS NULL
        LIMIT 1
      `);
      
      const result = await stmt.bind(slug).first();
      
      if (!result) {
        return null;
      }
      
      return this.deserializeRecord(result) as JournalEntry;
    } catch (error) {
      console.error(`Error getting journal entry by slug ${slug}:`, error);
      throw error;
    }
  }

  /**
   * Get published journal entries for frontend
   */
  async getPublishedJournalEntries(options: {
    skip?: number;
    take?: number;
    page?: number;
    limit?: number;
    tags?: string[];
  } = {}): Promise<{ entries: JournalEntry[]; count: number }> {
    const { tags, page = 1, limit = 10 } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE published = 1 AND deleted_at IS NULL';
    const params: any[] = [];
    
    // Filter by tags if provided
    if (tags && tags.length > 0) {
      const tagConditions = tags.map(() => 'tags LIKE ?').join(' OR ');
      whereClause += ` AND (${tagConditions})`;
      tags.forEach(tag => params.push(`%"${tag}"%`));
    }
    
    // Get total count
    const countStmt = this.env.DB.prepare(`
      SELECT COUNT(*) as count FROM journal_entries ${whereClause}
    `);
    const countResult = await countStmt.bind(...params).first();
    const count = countResult?.count || 0;
    
    // Get entries
    const stmt = this.env.DB.prepare(`
      SELECT * FROM journal_entries 
      ${whereClause}
      ORDER BY published_at DESC
      LIMIT ? OFFSET ?
    `);
    
    const results = await stmt.bind(...params, limit, offset).all();
    const entries = results.results.map(row => this.deserializeRecord(row)) as JournalEntry[];
    
    return { entries, count };
  }

  /**
   * Get all journal entries (admin)
   */
  async getAllJournalEntries(options: {
    page?: number;
    limit?: number;
    published?: boolean;
  } = {}): Promise<{ entries: JournalEntry[]; count: number }> {
    const { page = 1, limit = 10, published } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE deleted_at IS NULL';
    const params: any[] = [];
    
    if (published !== undefined) {
      whereClause += ' AND published = ?';
      params.push(published ? 1 : 0);
    }
    
    // Get total count
    const countStmt = this.env.DB.prepare(`
      SELECT COUNT(*) as count FROM journal_entries ${whereClause}
    `);
    const countResult = await countStmt.bind(...params).first();
    const count = countResult?.count || 0;
    
    // Get entries
    const stmt = this.env.DB.prepare(`
      SELECT * FROM journal_entries 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `);
    
    const results = await stmt.bind(...params, limit, offset).all();
    const entries = results.results.map(row => this.deserializeRecord(row)) as JournalEntry[];
    
    return { entries, count };
  }

  /**
   * Delete a journal entry (soft delete)
   */
  async deleteJournalEntry(id: string): Promise<boolean> {
    return this.delete('journal_entries', id);
  }

  /**
   * Search journal entries
   */
  async searchJournalEntries(query: string, options: {
    page?: number;
    limit?: number;
    published?: boolean;
  } = {}): Promise<{ entries: JournalEntry[]; count: number }> {
    const { page = 1, limit = 10, published = true } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE deleted_at IS NULL';
    const params: any[] = [];
    
    if (published !== undefined) {
      whereClause += ' AND published = ?';
      params.push(published ? 1 : 0);
    }
    
    // Add search conditions
    whereClause += ` AND (
      title LIKE ? OR 
      content LIKE ? OR 
      excerpt LIKE ? OR 
      tags LIKE ?
    )`;
    const searchTerm = `%${query}%`;
    params.push(searchTerm, searchTerm, searchTerm, searchTerm);
    
    // Get total count
    const countStmt = this.env.DB.prepare(`
      SELECT COUNT(*) as count FROM journal_entries ${whereClause}
    `);
    const countResult = await countStmt.bind(...params).first();
    const count = countResult?.count || 0;
    
    // Get entries
    const stmt = this.env.DB.prepare(`
      SELECT * FROM journal_entries 
      ${whereClause}
      ORDER BY published_at DESC
      LIMIT ? OFFSET ?
    `);
    
    const results = await stmt.bind(...params, limit, offset).all();
    const entries = results.results.map(row => this.deserializeRecord(row)) as JournalEntry[];
    
    return { entries, count };
  }

  /**
   * Get journal entries by tag
   */
  async getJournalEntriesByTag(tag: string, options: {
    page?: number;
    limit?: number;
  } = {}): Promise<{ entries: JournalEntry[]; count: number }> {
    return this.getPublishedJournalEntries({
      ...options,
      tags: [tag]
    });
  }

  /**
   * Get recent journal entries
   */
  async getRecentJournalEntries(limit: number = 5): Promise<JournalEntry[]> {
    const { entries } = await this.getPublishedJournalEntries({ limit });
    return entries;
  }

  /**
   * Get related journal entries
   */
  async getRelatedJournalEntries(entryId: string, limit: number = 3): Promise<JournalEntry[]> {
    try {
      // Get the current entry to find related entries by tags
      const currentEntry = await this.getJournalEntry(entryId);
      if (!currentEntry || !currentEntry.tags || currentEntry.tags.length === 0) {
        // If no tags, return recent entries
        return this.getRecentJournalEntries(limit);
      }
      
      // Find entries with similar tags
      const tagConditions = currentEntry.tags.map(() => 'tags LIKE ?').join(' OR ');
      const params = currentEntry.tags.map(tag => `%"${tag}"%`);
      params.push(entryId, limit);
      
      const stmt = this.env.DB.prepare(`
        SELECT * FROM journal_entries 
        WHERE published = 1 AND deleted_at IS NULL 
        AND id != ? 
        AND (${tagConditions})
        ORDER BY published_at DESC
        LIMIT ?
      `);
      
      const results = await stmt.bind(...params).all();
      return results.results.map(row => this.deserializeRecord(row)) as JournalEntry[];
    } catch (error) {
      console.error('Error getting related journal entries:', error);
      return this.getRecentJournalEntries(limit);
    }
  }

  /**
   * Generate URL-friendly slug from title
   */
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  }

  /**
   * Generate excerpt from content
   */
  private generateExcerpt(content: string, maxLength: number = 150): string {
    // Strip HTML tags if present
    const textContent = content.replace(/<[^>]*>/g, '');
    
    if (textContent.length <= maxLength) {
      return textContent;
    }
    
    // Find the last complete word within the limit
    const truncated = textContent.substring(0, maxLength);
    const lastSpaceIndex = truncated.lastIndexOf(' ');
    
    if (lastSpaceIndex > 0) {
      return truncated.substring(0, lastSpaceIndex) + '...';
    }
    
    return truncated + '...';
  }
} 