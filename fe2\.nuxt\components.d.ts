
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
interface _GlobalComponents {
      'AccountHeader': typeof import("../components/Account/AccountHeader.vue")['default']
    'AccountSettings': typeof import("../components/Account/AccountSettings.vue")['default']
    'AccountAddressManagement': typeof import("../components/Account/AddressManagement.vue")['default']
    'AccountOrderHistory': typeof import("../components/Account/OrderHistory.vue")['default']
    'AccountAddresses': typeof import("../components/AccountAddresses.vue")['default']
    'AddressTypeDisplay': typeof import("../components/AddressTypeDisplay.vue")['default']
    'BillingAddress': typeof import("../components/BillingAddress.vue")['default']
    'BillingAddressToggle': typeof import("../components/BillingAddressToggle.vue")['default']
    'Breadcrumb': typeof import("../components/Breadcrumb.vue")['default']
    'CartAddedPopup': typeof import("../components/CartAddedPopup.vue")['default']
    'CartItem': typeof import("../components/CartItem.vue")['default']
    'CartProvider': typeof import("../components/CartProvider.vue")['default']
    'CategoryHero': typeof import("../components/CategoryHero.vue")['default']
    'CheckoutVerify': typeof import("../components/CheckoutVerify.vue")['default']
    'CollectionHighlights': typeof import("../components/CollectionHighlights.vue")['default']
    'CookieConsent': typeof import("../components/CookieConsent.vue")['default']
    'CustomerInformation': typeof import("../components/CustomerInformation.vue")['default']
    'EasyboxLockerSelector': typeof import("../components/EasyboxLockerSelector.vue")['default']
    'EmptyState': typeof import("../components/EmptyState.vue")['default']
    'ErrorBoundary': typeof import("../components/ErrorBoundary.vue")['default']
    'HeroSection': typeof import("../components/HeroSection.vue")['default']
    'LoadingSkeleton': typeof import("../components/LoadingSkeleton.vue")['default']
    'LoadingSpinner': typeof import("../components/LoadingSpinner.vue")['default']
    'LocaleSelector': typeof import("../components/LocaleSelector.vue")['default']
    'LocaleSwitcher': typeof import("../components/LocaleSwitcher.vue")['default']
    'MobileMenu': typeof import("../components/MobileMenu.vue")['default']
    'PageHero': typeof import("../components/PageHero.vue")['default']
    'PageTransition': typeof import("../components/PageTransition.vue")['default']
    'Pagination': typeof import("../components/Pagination.vue")['default']
    'PaymentMethod': typeof import("../components/PaymentMethod.vue")['default']
    'PaymentProviderSelector': typeof import("../components/PaymentProviderSelector.vue")['default']
    'ProductCard': typeof import("../components/ProductCard.vue")['default']
    'ProductQuickView': typeof import("../components/ProductQuickView.vue")['default']
    'ProductReviews': typeof import("../components/ProductReviews.vue")['default']
    'RecentlyViewed': typeof import("../components/RecentlyViewed.vue")['default']
    'ReviewOrder': typeof import("../components/ReviewOrder.vue")['default']
    'SearchModal': typeof import("../components/SearchModal.vue")['default']
    'ShippingAddress': typeof import("../components/ShippingAddress.vue")['default']
    'ShippingMethod': typeof import("../components/ShippingMethod.vue")['default']
    'StarRating': typeof import("../components/StarRating.vue")['default']
    'StripePaymentElement': typeof import("../components/StripePaymentElement.vue")['default']
    'StripePaymentForm': typeof import("../components/StripePaymentForm.vue")['default']
    'TheFooter': typeof import("../components/TheFooter.vue")['default']
    'TheHeader': typeof import("../components/TheHeader.vue")['default']
    'ThematicTopBar': typeof import("../components/ThematicTopBar.vue")['default']
    'ToastNotification': typeof import("../components/ToastNotification.vue")['default']
    'TrustedShop': typeof import("../components/TrustedShop.vue")['default']
    'UAccordion': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Accordion.vue")['default']
    'UAlert': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Alert.vue")['default']
    'UAvatar': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Avatar.vue")['default']
    'UAvatarGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/AvatarGroup")['default']
    'UBadge': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Badge.vue")['default']
    'UButton': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Button.vue")['default']
    'UButtonGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/ButtonGroup")['default']
    'UCarousel': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Carousel.vue")['default']
    'UChip': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Chip.vue")['default']
    'UDropdown': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Dropdown.vue")['default']
    'UIcon': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Icon.vue")['default']
    'UKbd': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Kbd.vue")['default']
    'ULink': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Link.vue")['default']
    'UMeter': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Meter.vue")['default']
    'UMeterGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/MeterGroup")['default']
    'UProgress': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Progress.vue")['default']
    'UCheckbox': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Checkbox.vue")['default']
    'UForm': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Form.vue")['default']
    'UFormGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/FormGroup.vue")['default']
    'UInput': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Input.vue")['default']
    'UInputMenu': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/InputMenu.vue")['default']
    'URadio': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Radio.vue")['default']
    'URadioGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/RadioGroup.vue")['default']
    'URange': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Range.vue")['default']
    'USelect': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Select.vue")['default']
    'USelectMenu': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/SelectMenu.vue")['default']
    'UTextarea': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Textarea.vue")['default']
    'UToggle': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Toggle.vue")['default']
    'UTable': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/data/Table.vue")['default']
    'UCard': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Card.vue")['default']
    'UContainer': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Container.vue")['default']
    'UDivider': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Divider.vue")['default']
    'USkeleton': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Skeleton.vue")['default']
    'UBreadcrumb': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Breadcrumb.vue")['default']
    'UCommandPalette': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/CommandPalette.vue")['default']
    'UCommandPaletteGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/CommandPaletteGroup.vue")['default']
    'UHorizontalNavigation': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/HorizontalNavigation.vue")['default']
    'UPagination': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Pagination.vue")['default']
    'UTabs': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Tabs.vue")['default']
    'UVerticalNavigation': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/VerticalNavigation.vue")['default']
    'UContextMenu': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/ContextMenu.vue")['default']
    'UModal': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Modal.vue")['default']
    'UModals': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Modals.client.vue")['default']
    'UNotification': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Notification.vue")['default']
    'UNotifications': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Notifications.vue")['default']
    'UPopover': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Popover.vue")['default']
    'USlideover': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Slideover.vue")['default']
    'USlideovers': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Slideovers.client.vue")['default']
    'UTooltip': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Tooltip.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'ClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
    'NuxtPicture': typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
    'Icon': typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'ColorScheme': typeof import("../node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
    'NuxtLinkLocale': typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
    'SwitchLocalePathLink': typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
    'NuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'UModals': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'USlideovers': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyAccountHeader': typeof import("../components/Account/AccountHeader.vue")['default']
    'LazyAccountSettings': typeof import("../components/Account/AccountSettings.vue")['default']
    'LazyAccountAddressManagement': typeof import("../components/Account/AddressManagement.vue")['default']
    'LazyAccountOrderHistory': typeof import("../components/Account/OrderHistory.vue")['default']
    'LazyAccountAddresses': typeof import("../components/AccountAddresses.vue")['default']
    'LazyAddressTypeDisplay': typeof import("../components/AddressTypeDisplay.vue")['default']
    'LazyBillingAddress': typeof import("../components/BillingAddress.vue")['default']
    'LazyBillingAddressToggle': typeof import("../components/BillingAddressToggle.vue")['default']
    'LazyBreadcrumb': typeof import("../components/Breadcrumb.vue")['default']
    'LazyCartAddedPopup': typeof import("../components/CartAddedPopup.vue")['default']
    'LazyCartItem': typeof import("../components/CartItem.vue")['default']
    'LazyCartProvider': typeof import("../components/CartProvider.vue")['default']
    'LazyCategoryHero': typeof import("../components/CategoryHero.vue")['default']
    'LazyCheckoutVerify': typeof import("../components/CheckoutVerify.vue")['default']
    'LazyCollectionHighlights': typeof import("../components/CollectionHighlights.vue")['default']
    'LazyCookieConsent': typeof import("../components/CookieConsent.vue")['default']
    'LazyCustomerInformation': typeof import("../components/CustomerInformation.vue")['default']
    'LazyEasyboxLockerSelector': typeof import("../components/EasyboxLockerSelector.vue")['default']
    'LazyEmptyState': typeof import("../components/EmptyState.vue")['default']
    'LazyErrorBoundary': typeof import("../components/ErrorBoundary.vue")['default']
    'LazyHeroSection': typeof import("../components/HeroSection.vue")['default']
    'LazyLoadingSkeleton': typeof import("../components/LoadingSkeleton.vue")['default']
    'LazyLoadingSpinner': typeof import("../components/LoadingSpinner.vue")['default']
    'LazyLocaleSelector': typeof import("../components/LocaleSelector.vue")['default']
    'LazyLocaleSwitcher': typeof import("../components/LocaleSwitcher.vue")['default']
    'LazyMobileMenu': typeof import("../components/MobileMenu.vue")['default']
    'LazyPageHero': typeof import("../components/PageHero.vue")['default']
    'LazyPageTransition': typeof import("../components/PageTransition.vue")['default']
    'LazyPagination': typeof import("../components/Pagination.vue")['default']
    'LazyPaymentMethod': typeof import("../components/PaymentMethod.vue")['default']
    'LazyPaymentProviderSelector': typeof import("../components/PaymentProviderSelector.vue")['default']
    'LazyProductCard': typeof import("../components/ProductCard.vue")['default']
    'LazyProductQuickView': typeof import("../components/ProductQuickView.vue")['default']
    'LazyProductReviews': typeof import("../components/ProductReviews.vue")['default']
    'LazyRecentlyViewed': typeof import("../components/RecentlyViewed.vue")['default']
    'LazyReviewOrder': typeof import("../components/ReviewOrder.vue")['default']
    'LazySearchModal': typeof import("../components/SearchModal.vue")['default']
    'LazyShippingAddress': typeof import("../components/ShippingAddress.vue")['default']
    'LazyShippingMethod': typeof import("../components/ShippingMethod.vue")['default']
    'LazyStarRating': typeof import("../components/StarRating.vue")['default']
    'LazyStripePaymentElement': typeof import("../components/StripePaymentElement.vue")['default']
    'LazyStripePaymentForm': typeof import("../components/StripePaymentForm.vue")['default']
    'LazyTheFooter': typeof import("../components/TheFooter.vue")['default']
    'LazyTheHeader': typeof import("../components/TheHeader.vue")['default']
    'LazyThematicTopBar': typeof import("../components/ThematicTopBar.vue")['default']
    'LazyToastNotification': typeof import("../components/ToastNotification.vue")['default']
    'LazyTrustedShop': typeof import("../components/TrustedShop.vue")['default']
    'LazyUAccordion': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Accordion.vue")['default']
    'LazyUAlert': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Alert.vue")['default']
    'LazyUAvatar': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Avatar.vue")['default']
    'LazyUAvatarGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/AvatarGroup")['default']
    'LazyUBadge': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Badge.vue")['default']
    'LazyUButton': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Button.vue")['default']
    'LazyUButtonGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/ButtonGroup")['default']
    'LazyUCarousel': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Carousel.vue")['default']
    'LazyUChip': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Chip.vue")['default']
    'LazyUDropdown': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Dropdown.vue")['default']
    'LazyUIcon': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Icon.vue")['default']
    'LazyUKbd': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Kbd.vue")['default']
    'LazyULink': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Link.vue")['default']
    'LazyUMeter': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Meter.vue")['default']
    'LazyUMeterGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/MeterGroup")['default']
    'LazyUProgress': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Progress.vue")['default']
    'LazyUCheckbox': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Checkbox.vue")['default']
    'LazyUForm': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Form.vue")['default']
    'LazyUFormGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/FormGroup.vue")['default']
    'LazyUInput': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Input.vue")['default']
    'LazyUInputMenu': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/InputMenu.vue")['default']
    'LazyURadio': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Radio.vue")['default']
    'LazyURadioGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/RadioGroup.vue")['default']
    'LazyURange': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Range.vue")['default']
    'LazyUSelect': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Select.vue")['default']
    'LazyUSelectMenu': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/SelectMenu.vue")['default']
    'LazyUTextarea': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Textarea.vue")['default']
    'LazyUToggle': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Toggle.vue")['default']
    'LazyUTable': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/data/Table.vue")['default']
    'LazyUCard': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Card.vue")['default']
    'LazyUContainer': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Container.vue")['default']
    'LazyUDivider': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Divider.vue")['default']
    'LazyUSkeleton': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Skeleton.vue")['default']
    'LazyUBreadcrumb': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Breadcrumb.vue")['default']
    'LazyUCommandPalette': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/CommandPalette.vue")['default']
    'LazyUCommandPaletteGroup': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/CommandPaletteGroup.vue")['default']
    'LazyUHorizontalNavigation': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/HorizontalNavigation.vue")['default']
    'LazyUPagination': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Pagination.vue")['default']
    'LazyUTabs': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Tabs.vue")['default']
    'LazyUVerticalNavigation': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/VerticalNavigation.vue")['default']
    'LazyUContextMenu': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/ContextMenu.vue")['default']
    'LazyUModal': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Modal.vue")['default']
    'LazyUModals': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Modals.client.vue")['default']
    'LazyUNotification': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Notification.vue")['default']
    'LazyUNotifications': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Notifications.vue")['default']
    'LazyUPopover': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Popover.vue")['default']
    'LazyUSlideover': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Slideover.vue")['default']
    'LazyUSlideovers': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Slideovers.client.vue")['default']
    'LazyUTooltip': typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Tooltip.vue")['default']
    'LazyNuxtWelcome': typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'LazyNuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'LazyNuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'LazyClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'LazyDevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'LazyServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'LazyNuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'LazyNuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'LazyNuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'LazyNuxtImg': typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
    'LazyNuxtPicture': typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
    'LazyIcon': typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'LazyColorScheme': typeof import("../node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
    'LazyNuxtLinkLocale': typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
    'LazySwitchLocalePathLink': typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
    'LazyNuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'LazyNoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'LazyLink': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'LazyBase': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'LazyTitle': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'LazyMeta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'LazyStyle': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'LazyHead': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'LazyHtml': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'LazyBody': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'LazyNuxtIsland': typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'LazyUModals': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyUSlideovers': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtRouteAnnouncer': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const AccountHeader: typeof import("../components/Account/AccountHeader.vue")['default']
export const AccountSettings: typeof import("../components/Account/AccountSettings.vue")['default']
export const AccountAddressManagement: typeof import("../components/Account/AddressManagement.vue")['default']
export const AccountOrderHistory: typeof import("../components/Account/OrderHistory.vue")['default']
export const AccountAddresses: typeof import("../components/AccountAddresses.vue")['default']
export const AddressTypeDisplay: typeof import("../components/AddressTypeDisplay.vue")['default']
export const BillingAddress: typeof import("../components/BillingAddress.vue")['default']
export const BillingAddressToggle: typeof import("../components/BillingAddressToggle.vue")['default']
export const Breadcrumb: typeof import("../components/Breadcrumb.vue")['default']
export const CartAddedPopup: typeof import("../components/CartAddedPopup.vue")['default']
export const CartItem: typeof import("../components/CartItem.vue")['default']
export const CartProvider: typeof import("../components/CartProvider.vue")['default']
export const CategoryHero: typeof import("../components/CategoryHero.vue")['default']
export const CheckoutVerify: typeof import("../components/CheckoutVerify.vue")['default']
export const CollectionHighlights: typeof import("../components/CollectionHighlights.vue")['default']
export const CookieConsent: typeof import("../components/CookieConsent.vue")['default']
export const CustomerInformation: typeof import("../components/CustomerInformation.vue")['default']
export const EasyboxLockerSelector: typeof import("../components/EasyboxLockerSelector.vue")['default']
export const EmptyState: typeof import("../components/EmptyState.vue")['default']
export const ErrorBoundary: typeof import("../components/ErrorBoundary.vue")['default']
export const HeroSection: typeof import("../components/HeroSection.vue")['default']
export const LoadingSkeleton: typeof import("../components/LoadingSkeleton.vue")['default']
export const LoadingSpinner: typeof import("../components/LoadingSpinner.vue")['default']
export const LocaleSelector: typeof import("../components/LocaleSelector.vue")['default']
export const LocaleSwitcher: typeof import("../components/LocaleSwitcher.vue")['default']
export const MobileMenu: typeof import("../components/MobileMenu.vue")['default']
export const PageHero: typeof import("../components/PageHero.vue")['default']
export const PageTransition: typeof import("../components/PageTransition.vue")['default']
export const Pagination: typeof import("../components/Pagination.vue")['default']
export const PaymentMethod: typeof import("../components/PaymentMethod.vue")['default']
export const PaymentProviderSelector: typeof import("../components/PaymentProviderSelector.vue")['default']
export const ProductCard: typeof import("../components/ProductCard.vue")['default']
export const ProductQuickView: typeof import("../components/ProductQuickView.vue")['default']
export const ProductReviews: typeof import("../components/ProductReviews.vue")['default']
export const RecentlyViewed: typeof import("../components/RecentlyViewed.vue")['default']
export const ReviewOrder: typeof import("../components/ReviewOrder.vue")['default']
export const SearchModal: typeof import("../components/SearchModal.vue")['default']
export const ShippingAddress: typeof import("../components/ShippingAddress.vue")['default']
export const ShippingMethod: typeof import("../components/ShippingMethod.vue")['default']
export const StarRating: typeof import("../components/StarRating.vue")['default']
export const StripePaymentElement: typeof import("../components/StripePaymentElement.vue")['default']
export const StripePaymentForm: typeof import("../components/StripePaymentForm.vue")['default']
export const TheFooter: typeof import("../components/TheFooter.vue")['default']
export const TheHeader: typeof import("../components/TheHeader.vue")['default']
export const ThematicTopBar: typeof import("../components/ThematicTopBar.vue")['default']
export const ToastNotification: typeof import("../components/ToastNotification.vue")['default']
export const TrustedShop: typeof import("../components/TrustedShop.vue")['default']
export const UAccordion: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Accordion.vue")['default']
export const UAlert: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Alert.vue")['default']
export const UAvatar: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Avatar.vue")['default']
export const UAvatarGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/AvatarGroup")['default']
export const UBadge: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Badge.vue")['default']
export const UButton: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Button.vue")['default']
export const UButtonGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/ButtonGroup")['default']
export const UCarousel: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Carousel.vue")['default']
export const UChip: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Chip.vue")['default']
export const UDropdown: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Dropdown.vue")['default']
export const UIcon: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Icon.vue")['default']
export const UKbd: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Kbd.vue")['default']
export const ULink: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Link.vue")['default']
export const UMeter: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Meter.vue")['default']
export const UMeterGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/MeterGroup")['default']
export const UProgress: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Progress.vue")['default']
export const UCheckbox: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Checkbox.vue")['default']
export const UForm: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Form.vue")['default']
export const UFormGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/FormGroup.vue")['default']
export const UInput: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Input.vue")['default']
export const UInputMenu: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/InputMenu.vue")['default']
export const URadio: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Radio.vue")['default']
export const URadioGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/RadioGroup.vue")['default']
export const URange: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Range.vue")['default']
export const USelect: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Select.vue")['default']
export const USelectMenu: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/SelectMenu.vue")['default']
export const UTextarea: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Textarea.vue")['default']
export const UToggle: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Toggle.vue")['default']
export const UTable: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/data/Table.vue")['default']
export const UCard: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Card.vue")['default']
export const UContainer: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Container.vue")['default']
export const UDivider: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Divider.vue")['default']
export const USkeleton: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Skeleton.vue")['default']
export const UBreadcrumb: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Breadcrumb.vue")['default']
export const UCommandPalette: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/CommandPalette.vue")['default']
export const UCommandPaletteGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/CommandPaletteGroup.vue")['default']
export const UHorizontalNavigation: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/HorizontalNavigation.vue")['default']
export const UPagination: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Pagination.vue")['default']
export const UTabs: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Tabs.vue")['default']
export const UVerticalNavigation: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/VerticalNavigation.vue")['default']
export const UContextMenu: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/ContextMenu.vue")['default']
export const UModal: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Modal.vue")['default']
export const UModals: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Modals.client.vue")['default']
export const UNotification: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Notification.vue")['default']
export const UNotifications: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Notifications.vue")['default']
export const UPopover: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Popover.vue")['default']
export const USlideover: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Slideover.vue")['default']
export const USlideovers: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Slideovers.client.vue")['default']
export const UTooltip: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Tooltip.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const ClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
export const NuxtPicture: typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
export const Icon: typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const ColorScheme: typeof import("../node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
export const NuxtLinkLocale: typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
export const SwitchLocalePathLink: typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
export const NuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const UModals: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const USlideovers: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyAccountHeader: typeof import("../components/Account/AccountHeader.vue")['default']
export const LazyAccountSettings: typeof import("../components/Account/AccountSettings.vue")['default']
export const LazyAccountAddressManagement: typeof import("../components/Account/AddressManagement.vue")['default']
export const LazyAccountOrderHistory: typeof import("../components/Account/OrderHistory.vue")['default']
export const LazyAccountAddresses: typeof import("../components/AccountAddresses.vue")['default']
export const LazyAddressTypeDisplay: typeof import("../components/AddressTypeDisplay.vue")['default']
export const LazyBillingAddress: typeof import("../components/BillingAddress.vue")['default']
export const LazyBillingAddressToggle: typeof import("../components/BillingAddressToggle.vue")['default']
export const LazyBreadcrumb: typeof import("../components/Breadcrumb.vue")['default']
export const LazyCartAddedPopup: typeof import("../components/CartAddedPopup.vue")['default']
export const LazyCartItem: typeof import("../components/CartItem.vue")['default']
export const LazyCartProvider: typeof import("../components/CartProvider.vue")['default']
export const LazyCategoryHero: typeof import("../components/CategoryHero.vue")['default']
export const LazyCheckoutVerify: typeof import("../components/CheckoutVerify.vue")['default']
export const LazyCollectionHighlights: typeof import("../components/CollectionHighlights.vue")['default']
export const LazyCookieConsent: typeof import("../components/CookieConsent.vue")['default']
export const LazyCustomerInformation: typeof import("../components/CustomerInformation.vue")['default']
export const LazyEasyboxLockerSelector: typeof import("../components/EasyboxLockerSelector.vue")['default']
export const LazyEmptyState: typeof import("../components/EmptyState.vue")['default']
export const LazyErrorBoundary: typeof import("../components/ErrorBoundary.vue")['default']
export const LazyHeroSection: typeof import("../components/HeroSection.vue")['default']
export const LazyLoadingSkeleton: typeof import("../components/LoadingSkeleton.vue")['default']
export const LazyLoadingSpinner: typeof import("../components/LoadingSpinner.vue")['default']
export const LazyLocaleSelector: typeof import("../components/LocaleSelector.vue")['default']
export const LazyLocaleSwitcher: typeof import("../components/LocaleSwitcher.vue")['default']
export const LazyMobileMenu: typeof import("../components/MobileMenu.vue")['default']
export const LazyPageHero: typeof import("../components/PageHero.vue")['default']
export const LazyPageTransition: typeof import("../components/PageTransition.vue")['default']
export const LazyPagination: typeof import("../components/Pagination.vue")['default']
export const LazyPaymentMethod: typeof import("../components/PaymentMethod.vue")['default']
export const LazyPaymentProviderSelector: typeof import("../components/PaymentProviderSelector.vue")['default']
export const LazyProductCard: typeof import("../components/ProductCard.vue")['default']
export const LazyProductQuickView: typeof import("../components/ProductQuickView.vue")['default']
export const LazyProductReviews: typeof import("../components/ProductReviews.vue")['default']
export const LazyRecentlyViewed: typeof import("../components/RecentlyViewed.vue")['default']
export const LazyReviewOrder: typeof import("../components/ReviewOrder.vue")['default']
export const LazySearchModal: typeof import("../components/SearchModal.vue")['default']
export const LazyShippingAddress: typeof import("../components/ShippingAddress.vue")['default']
export const LazyShippingMethod: typeof import("../components/ShippingMethod.vue")['default']
export const LazyStarRating: typeof import("../components/StarRating.vue")['default']
export const LazyStripePaymentElement: typeof import("../components/StripePaymentElement.vue")['default']
export const LazyStripePaymentForm: typeof import("../components/StripePaymentForm.vue")['default']
export const LazyTheFooter: typeof import("../components/TheFooter.vue")['default']
export const LazyTheHeader: typeof import("../components/TheHeader.vue")['default']
export const LazyThematicTopBar: typeof import("../components/ThematicTopBar.vue")['default']
export const LazyToastNotification: typeof import("../components/ToastNotification.vue")['default']
export const LazyTrustedShop: typeof import("../components/TrustedShop.vue")['default']
export const LazyUAccordion: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Accordion.vue")['default']
export const LazyUAlert: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Alert.vue")['default']
export const LazyUAvatar: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Avatar.vue")['default']
export const LazyUAvatarGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/AvatarGroup")['default']
export const LazyUBadge: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Badge.vue")['default']
export const LazyUButton: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Button.vue")['default']
export const LazyUButtonGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/ButtonGroup")['default']
export const LazyUCarousel: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Carousel.vue")['default']
export const LazyUChip: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Chip.vue")['default']
export const LazyUDropdown: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Dropdown.vue")['default']
export const LazyUIcon: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Icon.vue")['default']
export const LazyUKbd: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Kbd.vue")['default']
export const LazyULink: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Link.vue")['default']
export const LazyUMeter: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Meter.vue")['default']
export const LazyUMeterGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/MeterGroup")['default']
export const LazyUProgress: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/elements/Progress.vue")['default']
export const LazyUCheckbox: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Checkbox.vue")['default']
export const LazyUForm: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Form.vue")['default']
export const LazyUFormGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/FormGroup.vue")['default']
export const LazyUInput: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Input.vue")['default']
export const LazyUInputMenu: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/InputMenu.vue")['default']
export const LazyURadio: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Radio.vue")['default']
export const LazyURadioGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/RadioGroup.vue")['default']
export const LazyURange: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Range.vue")['default']
export const LazyUSelect: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Select.vue")['default']
export const LazyUSelectMenu: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/SelectMenu.vue")['default']
export const LazyUTextarea: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Textarea.vue")['default']
export const LazyUToggle: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/forms/Toggle.vue")['default']
export const LazyUTable: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/data/Table.vue")['default']
export const LazyUCard: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Card.vue")['default']
export const LazyUContainer: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Container.vue")['default']
export const LazyUDivider: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Divider.vue")['default']
export const LazyUSkeleton: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/layout/Skeleton.vue")['default']
export const LazyUBreadcrumb: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Breadcrumb.vue")['default']
export const LazyUCommandPalette: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/CommandPalette.vue")['default']
export const LazyUCommandPaletteGroup: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/CommandPaletteGroup.vue")['default']
export const LazyUHorizontalNavigation: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/HorizontalNavigation.vue")['default']
export const LazyUPagination: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Pagination.vue")['default']
export const LazyUTabs: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/Tabs.vue")['default']
export const LazyUVerticalNavigation: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/navigation/VerticalNavigation.vue")['default']
export const LazyUContextMenu: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/ContextMenu.vue")['default']
export const LazyUModal: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Modal.vue")['default']
export const LazyUModals: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Modals.client.vue")['default']
export const LazyUNotification: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Notification.vue")['default']
export const LazyUNotifications: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Notifications.vue")['default']
export const LazyUPopover: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Popover.vue")['default']
export const LazyUSlideover: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Slideover.vue")['default']
export const LazyUSlideovers: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Slideovers.client.vue")['default']
export const LazyUTooltip: typeof import("../node_modules/@nuxt/ui/dist/runtime/components/overlays/Tooltip.vue")['default']
export const LazyNuxtWelcome: typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const LazyNuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const LazyNuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const LazyClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const LazyDevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const LazyServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const LazyNuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const LazyNuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const LazyNuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const LazyNuxtImg: typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
export const LazyNuxtPicture: typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
export const LazyIcon: typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const LazyColorScheme: typeof import("../node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
export const LazyNuxtLinkLocale: typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
export const LazySwitchLocalePathLink: typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
export const LazyNuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const LazyNoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const LazyLink: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const LazyBase: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const LazyTitle: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const LazyMeta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const LazyStyle: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const LazyHead: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const LazyHtml: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const LazyBody: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const LazyNuxtIsland: typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const LazyUModals: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyUSlideovers: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtRouteAnnouncer: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>

export const componentNames: string[]
