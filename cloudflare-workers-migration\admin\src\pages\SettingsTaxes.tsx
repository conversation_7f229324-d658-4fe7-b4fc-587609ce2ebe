import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import { 
  ChevronLeftIcon,
  CalculatorIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PercentBadgeIcon,
  MapPinIcon,
  BuildingOfficeIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';

// SettingsTaxes Component - Tax Rate Management
interface TaxRate {
  id: string;
  name: string;
  code: string;
  rate: number;
  is_default: boolean;
  is_combinable: boolean;
  tax_region_id: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface TaxRegion {
  id: string;
  country_code: string;
  province_code?: string;
  provider_id?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

const SettingsTaxes: React.FC = () => {
  const queryClient = useQueryClient();
  const [isAddingTaxRate, setIsAddingTaxRate] = useState(false);
  const [editingTaxRate, setEditingTaxRate] = useState<TaxRate | null>(null);
  const [newTaxRate, setNewTaxRate] = useState({
    name: '',
    code: '',
    rate: 0,
    is_default: false,
    is_combinable: false,
    tax_region_id: '',
    description: '',
  });

  // Fetch tax data
  const { data: taxData, isLoading } = useQuery({
    queryKey: ['admin-taxes'],
    queryFn: () => api.get('/admin/api/tax-rates').then(res => res.data),
  });

  // Create tax rate mutation
  const createTaxRateMutation = useMutation({
    mutationFn: (data: any) => api.post('/admin/api/tax-rates', data),
    onSuccess: () => {
      toast.success('Tax rate created successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-taxes'] });
      setIsAddingTaxRate(false);
      setNewTaxRate({ name: '', code: '', rate: 0, is_default: false, is_combinable: false, tax_region_id: '', description: '' });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create tax rate');
    },
  });

  // Update tax rate mutation
  const updateTaxRateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      api.put(`/admin/api/tax-rates/${id}`, data),
    onSuccess: () => {
      toast.success('Tax rate updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-taxes'] });
      setEditingTaxRate(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update tax rate');
    },
  });

  // Delete tax rate mutation
  const deleteTaxRateMutation = useMutation({
    mutationFn: (taxRateId: string) => api.delete(`/admin/api/tax-rates/${taxRateId}`),
    onSuccess: () => {
      toast.success('Tax rate deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-taxes'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete tax rate');
    },
  });

  const handleCreateTaxRate = () => {
    if (!newTaxRate.name.trim() || !newTaxRate.code.trim()) {
      toast.error('Tax rate name and code are required');
      return;
    }

    if (newTaxRate.rate < 0 || newTaxRate.rate > 100) {
      toast.error('Tax rate must be between 0 and 100');
      return;
    }

    createTaxRateMutation.mutate({
      name: newTaxRate.name,
      code: newTaxRate.code,
      rate: newTaxRate.rate / 100, // Convert percentage to decimal
      is_default: newTaxRate.is_default ? 1 : 0,
      is_combinable: newTaxRate.is_combinable ? 1 : 0,
      tax_region_id: newTaxRate.tax_region_id || null,
      metadata: { description: newTaxRate.description },
    });
  };

  const handleUpdateTaxRate = (taxRate: TaxRate) => {
    updateTaxRateMutation.mutate({
      id: taxRate.id,
      data: {
        name: taxRate.name,
        code: taxRate.code,
        rate: taxRate.rate,
        is_default: taxRate.is_default ? 1 : 0,
        is_combinable: taxRate.is_combinable ? 1 : 0,
        metadata: taxRate.metadata,
      },
    });
  };

  const handleDeleteTaxRate = (taxRateId: string) => {
    if (window.confirm('Are you sure you want to delete this tax rate?')) {
      deleteTaxRateMutation.mutate(taxRateId);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const taxRates = taxData?.data?.taxRates || [];
  const taxRegions = taxData?.data?.taxRegions || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/admin/settings"
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Settings
          </Link>
          <span className="text-gray-400">/</span>
          <div className="flex items-center space-x-2">
            <CalculatorIcon className="h-5 w-5 text-green-600" />
            <h1 className="text-2xl font-semibold text-gray-900">Taxes</h1>
          </div>
        </div>
        <button
          onClick={() => setIsAddingTaxRate(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Tax Rate</span>
        </button>
      </div>

      {/* Description */}
      <div className="bg-green-50 border border-green-200 rounded-md p-4">
        <p className="text-green-800 text-sm">
          Configure tax rates for different regions and tax types. Tax rates can be combined and set as defaults for automatic calculation.
        </p>
      </div>

      {/* Add Tax Rate Form */}
      {isAddingTaxRate && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Tax Rate</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tax Name *
              </label>
              <input
                type="text"
                value={newTaxRate.name}
                onChange={(e) => setNewTaxRate({ ...newTaxRate, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., VAT, Sales Tax, GST"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tax Code *
              </label>
              <input
                type="text"
                value={newTaxRate.code}
                onChange={(e) => setNewTaxRate({ ...newTaxRate, code: e.target.value.toUpperCase() })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., VAT, ST, GST"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tax Rate (%) *
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                max="100"
                value={newTaxRate.rate}
                onChange={(e) => setNewTaxRate({ ...newTaxRate, rate: parseFloat(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tax Region
              </label>
              <select
                value={newTaxRate.tax_region_id}
                onChange={(e) => setNewTaxRate({ ...newTaxRate, tax_region_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a region (optional)</option>
                {taxRegions.map((region: TaxRegion) => (
                  <option key={region.id} value={region.id}>
                    {region.country_code}{region.province_code ? ` - ${region.province_code}` : ''}
                  </option>
                ))}
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={newTaxRate.description}
                onChange={(e) => setNewTaxRate({ ...newTaxRate, description: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Optional description for this tax rate"
              />
            </div>
            <div className="md:col-span-2 space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={newTaxRate.is_default}
                  onChange={(e) => setNewTaxRate({ ...newTaxRate, is_default: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Set as default tax rate</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={newTaxRate.is_combinable}
                  onChange={(e) => setNewTaxRate({ ...newTaxRate, is_combinable: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Can be combined with other taxes</span>
              </label>
            </div>
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setIsAddingTaxRate(false)}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateTaxRate}
              disabled={createTaxRateMutation.isPending}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200"
            >
              {createTaxRateMutation.isPending ? 'Creating...' : 'Create Tax Rate'}
            </button>
          </div>
        </div>
      )}

      {/* Tax Rates List */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Tax Rates</h3>
          <p className="text-sm text-gray-600 mt-1">
            {taxRates.length} tax rate{taxRates.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {taxRates.length === 0 ? (
          <div className="p-6 text-center">
            <CalculatorIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No tax rates configured</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first tax rate.
            </p>
            <button
              onClick={() => setIsAddingTaxRate(true)}
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200"
            >
              Add Tax Rate
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {taxRates.map((taxRate: TaxRate) => {
              const isEditing = editingTaxRate?.id === taxRate.id;

              return (
                <div key={taxRate.id} className="p-6">
                  {isEditing ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Tax Name
                          </label>
                          <input
                            type="text"
                            value={editingTaxRate.name}
                            onChange={(e) => setEditingTaxRate({ ...editingTaxRate, name: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Tax Code
                          </label>
                          <input
                            type="text"
                            value={editingTaxRate.code}
                            onChange={(e) => setEditingTaxRate({ ...editingTaxRate, code: e.target.value.toUpperCase() })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Tax Rate (%)
                          </label>
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            max="100"
                            value={(editingTaxRate.rate * 100).toFixed(2)}
                            onChange={(e) => setEditingTaxRate({ ...editingTaxRate, rate: (parseFloat(e.target.value) || 0) / 100 })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={editingTaxRate.is_default}
                            onChange={(e) => setEditingTaxRate({ ...editingTaxRate, is_default: e.target.checked })}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Set as default tax rate</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={editingTaxRate.is_combinable}
                            onChange={(e) => setEditingTaxRate({ ...editingTaxRate, is_combinable: e.target.checked })}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Can be combined with other taxes</span>
                        </label>
                      </div>
                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={() => setEditingTaxRate(null)}
                          className="px-3 py-1 text-gray-600 hover:text-gray-800 transition-colors duration-200 flex items-center space-x-1"
                        >
                          <XMarkIcon className="h-4 w-4" />
                          <span>Cancel</span>
                        </button>
                        <button
                          onClick={() => handleUpdateTaxRate(editingTaxRate)}
                          disabled={updateTaxRateMutation.isPending}
                          className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 transition-colors duration-200 flex items-center space-x-1"
                        >
                          <CheckIcon className="h-4 w-4" />
                          <span>{updateTaxRateMutation.isPending ? 'Saving...' : 'Save'}</span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="text-lg font-medium text-gray-900">{taxRate.name}</h4>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {taxRate.code}
                          </span>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {(taxRate.rate * 100).toFixed(2)}%
                          </span>
                          {taxRate.is_default && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              Default
                            </span>
                          )}
                          {taxRate.is_combinable && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Combinable
                            </span>
                          )}
                        </div>
                        
                        <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <PercentBadgeIcon className="h-4 w-4" />
                            <span>Rate: {(taxRate.rate * 100).toFixed(2)}%</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <MapPinIcon className="h-4 w-4" />
                            <span>Region: {taxRate.tax_region_id ? 'Configured' : 'Global'}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <BuildingOfficeIcon className="h-4 w-4" />
                            <span>Created {new Date(taxRate.created_at).toLocaleDateString()}</span>
                          </div>
                        </div>

                        {taxRate.metadata?.description && (
                          <div className="mt-3">
                            <p className="text-sm text-gray-600">{taxRate.metadata.description}</p>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => setEditingTaxRate(taxRate)}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
                          title="Edit tax rate"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteTaxRate(taxRate.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors duration-200"
                          title="Delete tax rate"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Tax Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CalculatorIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{taxRates.length}</div>
              <div className="text-sm text-gray-600">Total Tax Rates</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <PercentBadgeIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {taxRates.length > 0 ? 
                  ((taxRates.reduce((sum: number, rate: TaxRate) => sum + rate.rate, 0) / taxRates.length) * 100).toFixed(1) + '%' 
                  : '0%'
                }
              </div>
              <div className="text-sm text-gray-600">Average Tax Rate</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <MapPinIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{taxRegions.length}</div>
              <div className="text-sm text-gray-600">Tax Regions</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsTaxes; 