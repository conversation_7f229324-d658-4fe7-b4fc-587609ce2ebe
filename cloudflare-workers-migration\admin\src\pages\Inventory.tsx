import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { 
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  PencilIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  CubeIcon,
  BuildingStorefrontIcon,
  MapPinIcon,
  InboxIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface InventoryItem {
  id: string;
  sku?: string;
  title?: string;
  thumbnail?: string;
  stocked_quantity: number;
  reserved_quantity: number;
  incoming_quantity: number;
  location_id: string;
  location_name: string;
  variant_id?: string;
  variant_title?: string;
  variant_sku?: string;
  product_title?: string;
  price?: number;
  currency_code?: string;
  raw_amount?: string;
  requires_shipping: boolean;
  created_at: string;
  updated_at: string;
}

interface StockLocation {
  id: string;
  name: string;
  address_1?: string;
  address_2?: string;
  city?: string;
  country_code?: string;
  province?: string;
  postal_code?: string;
  company?: string;
  phone?: string;
  inventory_count: number;
  created_at: string;
}

interface InventoryUpdateData {
  stocked_quantity: number;
  reserved_quantity: number;
  incoming_quantity: number;
  location_id: string;
  metadata?: any;
}

const Inventory: React.FC = () => {
  // State variables for inventory management
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState<string>('');
  const [stockStatus, setStockStatus] = useState<string>('');
  const [showLowStock, setShowLowStock] = useState(false);
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null);

  const queryClient = useQueryClient();

  // Fetch inventory data
  const { data: inventoryResponse, isLoading, error } = useQuery({
    queryKey: ['inventory', { page, limit, search: searchTerm, locationFilter, stockStatus }],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(locationFilter && { location_id: locationFilter }),
        ...(stockStatus && { stock_status: stockStatus }),
      });
      
      const response = await api.get(`${endpoints.inventory.items}?${params}`);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch stock locations
  const { data: locationsResponse } = useQuery({
    queryKey: ['stock-locations'],
    queryFn: async () => {
      const response = await api.get(endpoints.inventory.locations);
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Update inventory mutation
  const updateInventoryMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: InventoryUpdateData }) => {
      const response = await api.put(endpoints.inventory.updateItem(id), data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast.success('Inventory updated successfully');
      setEditingItem(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update inventory');
    },
  });

  const inventory = inventoryResponse?.data?.items || [];
  const pagination = inventoryResponse?.data?.pagination;
  const stats = inventoryResponse?.data?.stats;
  const locations = locationsResponse?.locations || [];

  // Filter for low stock items and ensure we have valid inventory data
  const filteredItems = inventory
    .filter((item: InventoryItem) => {
      // Filter out items without valid inventory data
      if (item.stocked_quantity === null || item.stocked_quantity === undefined) return false;
      if (item.reserved_quantity === null || item.reserved_quantity === undefined) return false;
      if (!item.location_id) return false;
      
      // Apply low stock filter if enabled
      if (showLowStock) {
        const available = item.stocked_quantity - item.reserved_quantity;
        return available <= 5;
      }
      
      return true;
    });

  const handleUpdateInventory = (id: string, data: InventoryUpdateData) => {
    updateInventoryMutation.mutate({ id, data });
  };

  const getStockStatus = (item: InventoryItem) => {
    const available = item.stocked_quantity - item.reserved_quantity;
    if (available <= 0) return { status: 'out-of-stock', color: 'text-red-600', label: 'Out of Stock' };
    if (available <= 5) return { status: 'low-stock', color: 'text-yellow-600', label: 'Low Stock' };
    return { status: 'in-stock', color: 'text-green-600', label: 'In Stock' };
  };

  const getStockIcon = (status: string) => {
    switch (status) {
      case 'out-of-stock':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'low-stock':
        return <ArrowDownIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ArrowUpIcon className="h-5 w-5 text-green-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">
            <h3 className="text-lg font-medium">Error loading inventory</h3>
            <p className="mt-2 text-sm">{(error as Error).message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl">
            Inventory Management
      </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage stock levels and inventory across all locations
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <InboxIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Items
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {pagination?.total || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowDownIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Low Stock
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {inventory.filter((item: InventoryItem) => 
                      item.stocked_quantity - item.reserved_quantity <= 5
                    ).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Out of Stock
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {inventory.filter((item: InventoryItem) => 
                      item.stocked_quantity - item.reserved_quantity <= 0
                    ).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <InboxIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Locations
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {locations.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search inventory..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="relative">
          <select
            className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
            value={locationFilter}
            onChange={(e) => setLocationFilter(e.target.value)}
          >
            <option value="">All Locations</option>
            {locations.map((location: StockLocation) => (
              <option key={location.id} value={location.id}>
                {location.name}
              </option>
            ))}
          </select>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="low_stock_filter"
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            checked={showLowStock}
            onChange={(e) => setShowLowStock(e.target.checked)}
          />
          <label htmlFor="low_stock_filter" className="ml-2 block text-sm text-gray-900">
            Show only low stock items
          </label>
        </div>
      </div>

      {/* Inventory Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  SKU
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Available
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stocked
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reserved
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Incoming
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredItems.map((item: InventoryItem) => {
                const stockStatus = getStockStatus(item);
                const available = item.stocked_quantity - item.reserved_quantity;
                
                return (
                  <tr key={`${item.id}-${item.location_id}`} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {item.thumbnail && (
                          <div className="flex-shrink-0 h-10 w-10">
                            <img className="h-10 w-10 rounded-full object-cover" src={item.thumbnail} alt="" />
                          </div>
                        )}
                        <div className={item.thumbnail ? "ml-4" : ""}>
                          <div className="text-sm font-medium text-gray-900">
                            {item.product_title || item.title || 'Untitled'}
                          </div>
                          {item.variant_title && (
                            <div className="text-sm text-gray-500">
                              {item.variant_title}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.variant_sku || item.sku || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.location_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.price ? `${(item.price).toFixed(2)} ${item.currency_code || 'RON'}` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStockIcon(stockStatus.status)}
                        <span className={`ml-2 text-sm font-medium ${stockStatus.color}`}>
                          {available}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.stocked_quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.reserved_quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.incoming_quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        stockStatus.status === 'out-of-stock' 
                          ? 'bg-red-100 text-red-800'
                          : stockStatus.status === 'low-stock'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {stockStatus.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => setEditingItem(item)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Edit Inventory"
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <InboxIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No inventory items found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search criteria or filters.
            </p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination && pagination.pages > 1 && (
        <div className="mt-6 flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setPage(Math.min(pagination.pages, page + 1))}
              disabled={page === pagination.pages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{(page - 1) * limit + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(page * limit, pagination.total)}
                </span>{' '}
                of <span className="font-medium">{pagination.total}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const pageNum = i + 1;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setPage(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNum === page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
                <button
                  onClick={() => setPage(Math.min(pagination.pages, page + 1))}
                  disabled={page === pagination.pages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Edit Inventory Modal */}
      {editingItem && (
        <InventoryEditModal
          item={editingItem}
          locations={locations}
          onClose={() => setEditingItem(null)}
          onSubmit={(data) => handleUpdateInventory(editingItem.id, data)}
          isSubmitting={updateInventoryMutation.isPending}
        />
      )}
    </div>
  );
};

// Inventory Edit Modal Component
interface InventoryEditModalProps {
  item: InventoryItem;
  locations: StockLocation[];
  onClose: () => void;
  onSubmit: (data: InventoryUpdateData) => void;
  isSubmitting: boolean;
}

const InventoryEditModal: React.FC<InventoryEditModalProps> = ({
  item,
  locations,
  onClose,
  onSubmit,
  isSubmitting
}) => {
  const [formData, setFormData] = useState<InventoryUpdateData>({
    stocked_quantity: item.stocked_quantity,
    reserved_quantity: item.reserved_quantity,
    incoming_quantity: item.incoming_quantity,
    location_id: item.location_id,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={onClose}></div>
        </div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="w-full mt-3 text-center sm:mt-0 sm:text-left">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Edit Inventory: {item.product_title || item.title}
                  </h3>
                  {item.variant_title && (
                    <p className="text-sm text-gray-500 mb-4">Variant: {item.variant_title}</p>
                  )}

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Location
                      </label>
                      <select
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={formData.location_id}
                        onChange={(e) => setFormData({ ...formData, location_id: e.target.value })}
                      >
                        {locations.map((location) => (
                          <option key={location.id} value={location.id}>
                            {location.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Stocked Quantity
                      </label>
                      <input
                        type="number"
                        min="0"
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={formData.stocked_quantity}
                        onChange={(e) => setFormData({ 
                          ...formData, 
                          stocked_quantity: parseInt(e.target.value) || 0 
                        })}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Reserved Quantity
                      </label>
                      <input
                        type="number"
                        min="0"
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={formData.reserved_quantity}
                        onChange={(e) => setFormData({ 
                          ...formData, 
                          reserved_quantity: parseInt(e.target.value) || 0 
                        })}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Incoming Quantity
                      </label>
                      <input
                        type="number"
                        min="0"
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={formData.incoming_quantity}
                        onChange={(e) => setFormData({ 
                          ...formData, 
                          incoming_quantity: parseInt(e.target.value) || 0 
                        })}
                      />
                    </div>

                    <div className="bg-gray-50 p-3 rounded-md">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Available Stock</h4>
                      <p className="text-lg font-semibold text-gray-900">
                        {formData.stocked_quantity - formData.reserved_quantity} units
                      </p>
                      <p className="text-xs text-gray-500">
                        (Stocked - Reserved)
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Updating...' : 'Update'}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Inventory; 