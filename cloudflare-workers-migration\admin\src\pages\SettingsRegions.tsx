import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import { 
  ChevronLeftIcon,
  GlobeAltIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  MapPinIcon,
  UsersIcon,
  CurrencyDollarIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';

interface Region {
  id: string;
  name: string;
  currency_code: string;
  automatic_taxes: boolean;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface Country {
  iso_2: string;
  iso_3: string;
  num_code: string;
  name: string;
  display_name: string;
  region_id?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface Currency {
  code: string;
  symbol: string;
  name: string;
}

const SettingsRegions: React.FC = () => {
  const queryClient = useQueryClient();
  const [isAddingRegion, setIsAddingRegion] = useState(false);
  const [editingRegion, setEditingRegion] = useState<Region | null>(null);
  const [newRegion, setNewRegion] = useState({
    name: '',
    currency_code: 'EUR',
    automatic_taxes: true,
    description: '',
  });

  // Fetch regions data
  const { data: regionsData, isLoading } = useQuery({
    queryKey: ['admin-regions'],
    queryFn: () => api.get('/admin/api/regions').then(res => res.data),
  });

  // Fetch countries data
  const { data: countriesData } = useQuery({
    queryKey: ['admin-countries'],
    queryFn: () => api.get('/admin/api/regions').then(res => res.data),
  });

  // Fetch currencies for dropdown
  const { data: currenciesData } = useQuery({
    queryKey: ['admin-currencies'],
    queryFn: () => api.get('/admin/api/currencies').then(res => res.data),
  });

  // Create region mutation
  const createRegionMutation = useMutation({
    mutationFn: (data: any) => api.post('/admin/api/regions', data),
    onSuccess: () => {
      toast.success('Region created successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-regions'] });
      setIsAddingRegion(false);
      setNewRegion({ name: '', currency_code: 'EUR', automatic_taxes: true, description: '' });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create region');
    },
  });

  // Update region mutation
  const updateRegionMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      api.put(`/admin/api/regions/${id}`, data),
    onSuccess: () => {
      toast.success('Region updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-regions'] });
      setEditingRegion(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update region');
    },
  });

  // Delete region mutation
  const deleteRegionMutation = useMutation({
    mutationFn: (regionId: string) => api.delete(`/admin/api/regions/${regionId}`),
    onSuccess: () => {
      toast.success('Region deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-regions'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete region');
    },
  });

  const handleCreateRegion = () => {
    if (!newRegion.name.trim()) {
      toast.error('Region name is required');
      return;
    }

    createRegionMutation.mutate({
      name: newRegion.name,
      currency_code: newRegion.currency_code,
      automatic_taxes: newRegion.automatic_taxes ? 1 : 0,
      metadata: { description: newRegion.description },
    });
  };

  const handleUpdateRegion = (region: Region) => {
    updateRegionMutation.mutate({
      id: region.id,
      data: {
        name: region.name,
        currency_code: region.currency_code,
        automatic_taxes: region.automatic_taxes ? 1 : 0,
        metadata: region.metadata,
      },
    });
  };

  const handleDeleteRegion = (regionId: string) => {
    if (window.confirm('Are you sure you want to delete this region?')) {
      deleteRegionMutation.mutate(regionId);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const regions = regionsData?.data?.regions || [];
  const countries = countriesData?.data?.countries || [];
  const currencies = currenciesData?.data?.currencies || [];

  const getCountriesForRegion = (regionId: string) => {
    return countries.filter((country: Country) => country.region_id === regionId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/admin/settings"
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Settings
          </Link>
          <span className="text-gray-400">/</span>
          <div className="flex items-center space-x-2">
            <GlobeAltIcon className="h-5 w-5 text-purple-600" />
            <h1 className="text-2xl font-semibold text-gray-900">Regions</h1>
          </div>
        </div>
        <button
          onClick={() => setIsAddingRegion(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Region</span>
        </button>
      </div>

      {/* Description */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <p className="text-blue-800 text-sm">
          Regions help you organize your customers and define geographical areas for shipping, taxes, and currency settings.
        </p>
      </div>

      {/* Add Region Form */}
      {isAddingRegion && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Region</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Region Name *
              </label>
              <input
                type="text"
                value={newRegion.name}
                onChange={(e) => setNewRegion({ ...newRegion, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Europe, North America"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Currency
              </label>
              <select
                value={newRegion.currency_code}
                onChange={(e) => setNewRegion({ ...newRegion, currency_code: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {currencies.map((currency: Currency) => (
                  <option key={currency.code} value={currency.code}>
                    {currency.code} - {currency.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={newRegion.description}
                onChange={(e) => setNewRegion({ ...newRegion, description: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Optional description for this region"
              />
            </div>
            <div className="md:col-span-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={newRegion.automatic_taxes}
                  onChange={(e) => setNewRegion({ ...newRegion, automatic_taxes: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Enable automatic tax calculation</span>
              </label>
            </div>
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setIsAddingRegion(false)}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateRegion}
              disabled={createRegionMutation.isPending}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200"
            >
              {createRegionMutation.isPending ? 'Creating...' : 'Create Region'}
            </button>
          </div>
        </div>
      )}

      {/* Regions List */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Configured Regions</h3>
          <p className="text-sm text-gray-600 mt-1">
            {regions.length} region{regions.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {regions.length === 0 ? (
          <div className="p-6 text-center">
            <GlobeAltIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No regions configured</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first region.
            </p>
            <button
              onClick={() => setIsAddingRegion(true)}
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200"
            >
              Add Region
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {regions.map((region: Region) => {
              const regionCountries = getCountriesForRegion(region.id);
              const isEditing = editingRegion?.id === region.id;

              return (
                <div key={region.id} className="p-6">
                  {isEditing ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Region Name
                          </label>
                          <input
                            type="text"
                            value={editingRegion.name}
                            onChange={(e) => setEditingRegion({ ...editingRegion, name: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Currency
                          </label>
                          <select
                            value={editingRegion.currency_code}
                            onChange={(e) => setEditingRegion({ ...editingRegion, currency_code: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          >
                            {currencies.map((currency: Currency) => (
                              <option key={currency.code} value={currency.code}>
                                {currency.code} - {currency.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                      <div>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={editingRegion.automatic_taxes}
                            onChange={(e) => setEditingRegion({ ...editingRegion, automatic_taxes: e.target.checked })}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Enable automatic tax calculation</span>
                        </label>
                      </div>
                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={() => setEditingRegion(null)}
                          className="px-3 py-1 text-gray-600 hover:text-gray-800 transition-colors duration-200 flex items-center space-x-1"
                        >
                          <XMarkIcon className="h-4 w-4" />
                          <span>Cancel</span>
                        </button>
                        <button
                          onClick={() => handleUpdateRegion(editingRegion)}
                          disabled={updateRegionMutation.isPending}
                          className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 transition-colors duration-200 flex items-center space-x-1"
                        >
                          <CheckIcon className="h-4 w-4" />
                          <span>{updateRegionMutation.isPending ? 'Saving...' : 'Save'}</span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="text-lg font-medium text-gray-900">{region.name}</h4>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {region.currency_code}
                          </span>
                          {region.automatic_taxes && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Auto Tax
                            </span>
                          )}
                        </div>
                        
                        <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <MapPinIcon className="h-4 w-4" />
                            <span>{regionCountries.length} countries</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <CurrencyDollarIcon className="h-4 w-4" />
                            <span>{region.currency_code}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <UsersIcon className="h-4 w-4" />
                            <span>Created {new Date(region.created_at).toLocaleDateString()}</span>
                          </div>
                        </div>

                        {regionCountries.length > 0 && (
                          <div className="mt-3">
                            <p className="text-sm font-medium text-gray-700 mb-2">Countries:</p>
                            <div className="flex flex-wrap gap-2">
                              {regionCountries.slice(0, 5).map((country: Country) => (
                                <span
                                  key={country.iso_2}
                                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                                >
                                  {country.display_name}
                                </span>
                              ))}
                              {regionCountries.length > 5 && (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                  +{regionCountries.length - 5} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => setEditingRegion(region)}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
                          title="Edit region"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteRegion(region.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors duration-200"
                          title="Delete region"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Region Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <GlobeAltIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{regions.length}</div>
              <div className="text-sm text-gray-600">Total Regions</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <MapPinIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{countries.length}</div>
              <div className="text-sm text-gray-600">Total Countries</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {new Set(regions.map((r: Region) => r.currency_code)).size}
              </div>
              <div className="text-sm text-gray-600">Currencies Used</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsRegions; 