import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  TagIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface Tag {
  id: string;
  name: string;
  usage_count: number;
  created_at: string;
  updated_at: string;
}

interface CreateTagData {
  name: string;
}

const Tags: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [newTagName, setNewTagName] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const queryClient = useQueryClient();

  // Fetch tags
  const { data: tagsData, isLoading, error } = useQuery({
    queryKey: ['tags', searchTerm],
    queryFn: async () => {
      const response = await api.get(endpoints.tags.list, {
        params: { search: searchTerm }
      });
      return response.data;
    },
  });

  // Create tag mutation
  const createTagMutation = useMutation({
    mutationFn: async (data: CreateTagData) => {
      return api.post(endpoints.tags.create, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      toast.success('Tag created successfully');
      setIsCreateModalOpen(false);
      setNewTagName('');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create tag');
    },
  });

  // Update tag mutation
  const updateTagMutation = useMutation({
    mutationFn: async (data: { id: string; name: string }) => {
      return api.put(endpoints.tags.update(data.id), { name: data.name });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      toast.success('Tag updated successfully');
      setEditingTag(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update tag');
    },
  });

  // Delete tag mutation
  const deleteTagMutation = useMutation({
    mutationFn: async (id: string) => {
      return api.delete(endpoints.tags.delete(id));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      toast.success('Tag deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete tag');
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: async (tagIds: string[]) => {
      return api.post(endpoints.tags.bulkDelete, { tagIds });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      toast.success(`${selectedTags.length} tags deleted successfully`);
      setSelectedTags([]);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete tags');
    },
  });

  const handleCreateTag = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTagName.trim()) {
      toast.error('Tag name is required');
      return;
    }
    createTagMutation.mutate({ name: newTagName.trim() });
  };

  const handleUpdateTag = (tag: Tag) => {
    if (!tag.name.trim()) {
      toast.error('Tag name is required');
      return;
    }
    updateTagMutation.mutate({ id: tag.id, name: tag.name.trim() });
  };

  const handleDeleteTag = (id: string) => {
    if (confirm('Are you sure you want to delete this tag? This will remove it from all products.')) {
      deleteTagMutation.mutate(id);
    }
  };

  const handleBulkDelete = () => {
    if (selectedTags.length === 0) return;
    if (confirm(`Are you sure you want to delete ${selectedTags.length} tags? This will remove them from all products.`)) {
      bulkDeleteMutation.mutate(selectedTags);
    }
  };

  const toggleTagSelection = (tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedTags.length === tagsData?.tags?.length) {
      setSelectedTags([]);
    } else {
      setSelectedTags(tagsData?.tags?.map((tag: Tag) => tag.id) || []);
    }
  };

  const tags = tagsData?.tags || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load tags. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Tags</h1>
          <p className="text-gray-600">Manage product tags and organize your catalog</p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Tag
        </button>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {selectedTags.length > 0 && (
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-600">
              {selectedTags.length} selected
            </span>
            <button
              onClick={handleBulkDelete}
              disabled={bulkDeleteMutation.isPending}
              className="flex items-center text-red-600 hover:text-red-800 text-sm"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete Selected
            </button>
          </div>
        )}
      </div>

      {/* Tags List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={selectedTags.length === tags.length && tags.length > 0}
              onChange={toggleSelectAll}
              className="mr-3"
            />
            <h3 className="text-lg font-medium text-gray-900">All Tags ({tags.length})</h3>
          </div>
        </div>

        {tags.length === 0 ? (
          <div className="text-center py-12">
            <TagIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No tags found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'No tags match your search.' : 'Get started by creating your first tag.'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
              >
                Add Tag
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {tags.map((tag: Tag) => (
              <div key={tag.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedTags.includes(tag.id)}
                      onChange={() => toggleTagSelection(tag.id)}
                      className="mr-3"
                    />
                    <div className="flex-1">
                      {editingTag?.id === tag.id ? (
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={editingTag.name}
                            onChange={(e) => setEditingTag({ ...editingTag, name: e.target.value })}
                            className="px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleUpdateTag(editingTag);
                              } else if (e.key === 'Escape') {
                                setEditingTag(null);
                              }
                            }}
                            autoFocus
                          />
                          <button
                            onClick={() => handleUpdateTag(editingTag)}
                            disabled={updateTagMutation.isPending}
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                          >
                            Save
                          </button>
                          <button
                            onClick={() => setEditingTag(null)}
                            className="px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-400"
                          >
                            Cancel
                          </button>
                        </div>
                      ) : (
                        <div>
                          <div className="flex items-center space-x-3">
                            <span className="font-medium text-gray-900">{tag.name}</span>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {tag.usage_count} products
                            </span>
                          </div>
                          <p className="text-sm text-gray-500">
                            Created {new Date(tag.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {editingTag?.id !== tag.id && (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setEditingTag(tag)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteTag(tag.id)}
                        disabled={deleteTagMutation.isPending}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create Tag Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Tag</h3>
            <form onSubmit={handleCreateTag}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tag Name
                </label>
                <input
                  type="text"
                  value={newTagName}
                  onChange={(e) => setNewTagName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter tag name"
                  required
                  autoFocus
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setIsCreateModalOpen(false);
                    setNewTagName('');
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createTagMutation.isPending}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {createTagMutation.isPending ? 'Creating...' : 'Create Tag'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Tags; 