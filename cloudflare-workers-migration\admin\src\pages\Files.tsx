import React, { useState, useRef, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { 
  PlusIcon, 
  MagnifyingGlassIcon, 
  TrashIcon, 
  PencilIcon,
  CloudArrowUpIcon,
  FolderIcon,
  FolderOpenIcon,
  DocumentIcon,
  PhotoIcon,
  VideoCameraIcon,
  SpeakerWaveIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  Square3Stack3DIcon,
  ChartBarIcon,
  XMarkIcon,
  ChevronRightIcon,
  HomeIcon
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';

interface FileItem {
  id: string;
  name: string;
  original_name: string;
  mime_type: string;
  size: number;
  url: string;
  thumbnail_url?: string;
  r2_key: string;
  folder_id?: string;
  folder_name?: string;
  folder_path?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface Folder {
  id: string;
  name: string;
  parent_id?: string;
  path: string;
  subfolder_count: number;
  file_count: number;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface FileAnalytics {
  total_files: number;
  total_size: number;
  file_types: {
    type: string;
    count: number;
    total_size: number;
  }[];
  recent_uploads: {
    uploads: number;
    date: string;
  }[];
}

const Files: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [mimeTypeFilter, setMimeTypeFilter] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [folderBreadcrumb, setFolderBreadcrumb] = useState<Folder[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showFolderModal, setShowFolderModal] = useState(false);
  const [showMoveModal, setShowMoveModal] = useState(false);
  const [editingFile, setEditingFile] = useState<FileItem | null>(null);
  const [editingFolder, setEditingFolder] = useState<Folder | null>(null);
  const [previewFile, setPreviewFile] = useState<FileItem | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const queryClient = useQueryClient();

  // Fetch files
  const { data: filesData, isLoading: filesLoading } = useQuery({
    queryKey: ['files', currentPage, searchTerm, mimeTypeFilter, currentFolder],
    queryFn: () => 
      api.get('/admin/api/files', {
        params: {
          page: currentPage,
          limit: 20,
          search: searchTerm,
          mime_type: mimeTypeFilter,
          folder_id: currentFolder || ''
        }
      }).then(res => res.data)
  });

  // Fetch folders
  const { data: foldersData } = useQuery({
    queryKey: ['folders', currentFolder],
    queryFn: () => 
      api.get('/admin/api/files/folders', {
        params: {
          parent_id: currentFolder || ''
        }
      }).then(res => res.data)
  });

  // Fetch analytics
  const { data: analyticsData } = useQuery<{analytics: FileAnalytics}>({
    queryKey: ['file-analytics'],
    queryFn: () => api.get('/admin/api/files/analytics').then(res => res.data)
  });

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: (formData: FormData) => 
      api.post('/admin/api/files/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      queryClient.invalidateQueries({ queryKey: ['file-analytics'] });
      toast.success('File uploaded successfully');
      setShowUploadModal(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to upload file');
    }
  });

  // Create folder mutation
  const createFolderMutation = useMutation({
    mutationFn: (data: { name: string; description?: string }) =>
      api.post('/admin/api/files/folders', { 
        ...data, 
        parent_id: currentFolder 
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['folders'] });
      toast.success('Folder created successfully');
      setShowFolderModal(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create folder');
    }
  });

  // Delete file mutation
  const deleteFileMutation = useMutation({
    mutationFn: (id: string) => api.delete(`/admin/api/files/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      queryClient.invalidateQueries({ queryKey: ['file-analytics'] });
      toast.success('File deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete file');
    }
  });

  // Delete folder mutation
  const deleteFolderMutation = useMutation({
    mutationFn: (id: string) => api.delete(`/admin/api/files/folders/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['folders'] });
      toast.success('Folder deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete folder');
    }
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: (fileIds: string[]) => 
      api.post('/admin/api/files/bulk-delete', { fileIds }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      queryClient.invalidateQueries({ queryKey: ['file-analytics'] });
      setSelectedFiles([]);
      toast.success('Files deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete files');
    }
  });

  // Move files mutation
  const moveFilesMutation = useMutation({
    mutationFn: ({ fileIds, folder_id }: { fileIds: string[]; folder_id: string | null }) =>
      api.post('/admin/api/files/move', { fileIds, folder_id }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      setSelectedFiles([]);
      setShowMoveModal(false);
      toast.success('Files moved successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to move files');
    }
  });

  const handleFileUpload = useCallback((files: FileList) => {
    Array.from(files).forEach(file => {
      const formData = new FormData();
      formData.append('file', file);
      if (currentFolder) {
        formData.append('folder_id', currentFolder);
      }
      uploadMutation.mutate(formData);
    });
  }, [currentFolder, uploadMutation]);

  const handleFolderClick = (folder: Folder) => {
    setCurrentFolder(folder.id);
    setFolderBreadcrumb([...folderBreadcrumb, folder]);
  };

  const handleBreadcrumbClick = (index: number) => {
    if (index === -1) {
      setCurrentFolder(null);
      setFolderBreadcrumb([]);
    } else {
      const folder = folderBreadcrumb[index];
      setCurrentFolder(folder.id);
      setFolderBreadcrumb(folderBreadcrumb.slice(0, index + 1));
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <PhotoIcon className="h-5 w-5" />;
    if (mimeType.startsWith('video/')) return <VideoCameraIcon className="h-5 w-5" />;
    if (mimeType.startsWith('audio/')) return <SpeakerWaveIcon className="h-5 w-5" />;
    return <DocumentIcon className="h-5 w-5" />;
  };

  const isImageFile = (mimeType: string) => mimeType.startsWith('image/');

  const files = filesData?.files || [];
  const folders = foldersData?.folders || [];
  const analytics = analyticsData?.analytics;
  const pagination = filesData?.pagination;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Files</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your uploaded files and media assets
          </p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={() => setShowFolderModal(true)}
            className="bg-white text-gray-700 border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center"
          >
            <FolderIcon className="h-4 w-4 mr-2" />
            New Folder
          </button>
          <button
            onClick={() => setShowUploadModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <CloudArrowUpIcon className="h-4 w-4 mr-2" />
            Upload Files
          </button>
        </div>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Square3Stack3DIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Files</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.total_files.toLocaleString()}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Size</p>
                <p className="text-2xl font-bold text-gray-900">{formatFileSize(analytics.total_size)}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <PhotoIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Images</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.file_types.find(t => t.type === 'Images')?.count || 0}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentIcon className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Documents</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.file_types.find(t => t.type === 'Documents')?.count || 0}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search files..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={mimeTypeFilter}
            onChange={(e) => setMimeTypeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Types</option>
            <option value="image">Images</option>
            <option value="video">Videos</option>
            <option value="document">Documents</option>
            <option value="audio">Audio</option>
          </select>

          <div className="flex rounded-lg border border-gray-300">
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-2 text-sm ${
                viewMode === 'grid' 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Grid
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-2 text-sm border-l border-gray-300 ${
                viewMode === 'list' 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              List
            </button>
          </div>

          {selectedFiles.length > 0 && (
            <div className="flex space-x-2">
              <button
                onClick={() => setShowMoveModal(true)}
                className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
              >
                Move ({selectedFiles.length})
              </button>
              <button
                onClick={() => bulkDeleteMutation.mutate(selectedFiles)}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Delete ({selectedFiles.length})
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Breadcrumb */}
      <nav className="flex" aria-label="Breadcrumb">
        <ol className="flex items-center space-x-4">
          <li>
            <button
              onClick={() => handleBreadcrumbClick(-1)}
              className="text-gray-400 hover:text-gray-500"
            >
              <HomeIcon className="h-5 w-5" />
            </button>
          </li>
          {folderBreadcrumb.map((folder, index) => (
            <li key={folder.id} className="flex items-center">
              <ChevronRightIcon className="h-5 w-5 text-gray-400" />
              <button
                onClick={() => handleBreadcrumbClick(index)}
                className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                {folder.name}
              </button>
            </li>
          ))}
        </ol>
      </nav>

      {/* Files and Folders */}
      <div className="bg-white rounded-lg border border-gray-200">
        {filesLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {/* Folders */}
            {folders.length > 0 && (
              <div className="border-b border-gray-200 p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Folders</h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {folders.map((folder) => (
                    <div
                      key={folder.id}
                      className="group relative p-4 border border-gray-200 rounded-lg hover:border-blue-300 cursor-pointer"
                      onDoubleClick={() => handleFolderClick(folder)}
                    >
                      <div className="text-center">
                        <FolderIcon className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                        <p className="text-sm font-medium text-gray-900 truncate">{folder.name}</p>
                        <p className="text-xs text-gray-500">
                          {folder.file_count} files, {folder.subfolder_count} folders
                        </p>
                      </div>
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteFolderMutation.mutate(folder.id);
                          }}
                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Files */}
            <div className="p-6">
              {files.length === 0 ? (
                <div className="text-center py-12">
                  <DocumentIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No files found</h3>
                  <p className="text-gray-500">Upload some files to get started</p>
                </div>
              ) : viewMode === 'grid' ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {files.map((file) => (
                    <div
                      key={file.id}
                      className={`group relative border-2 rounded-lg p-3 cursor-pointer transition-all ${
                        selectedFiles.includes(file.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => {
                        if (selectedFiles.includes(file.id)) {
                          setSelectedFiles(selectedFiles.filter(id => id !== file.id));
                        } else {
                          setSelectedFiles([...selectedFiles, file.id]);
                        }
                      }}
                    >
                      <div className="text-center">
                        {isImageFile(file.mime_type) && file.thumbnail_url ? (
                          <img
                            src={file.thumbnail_url}
                            alt={file.name}
                            className="h-16 w-16 object-cover rounded mx-auto mb-2"
                          />
                        ) : (
                          <div className="h-16 w-16 bg-gray-100 rounded flex items-center justify-center mx-auto mb-2">
                            {getFileIcon(file.mime_type)}
                          </div>
                        )}
                        <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                      </div>
                      
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setPreviewFile(file);
                          }}
                          className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteFileMutation.mutate(file.id);
                          }}
                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <input
                            type="checkbox"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedFiles(files.map(f => f.id));
                              } else {
                                setSelectedFiles([]);
                              }
                            }}
                            checked={selectedFiles.length === files.length && files.length > 0}
                          />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Size
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Uploaded
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {files.map((file) => (
                        <tr key={file.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="checkbox"
                              checked={selectedFiles.includes(file.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedFiles([...selectedFiles, file.id]);
                                } else {
                                  setSelectedFiles(selectedFiles.filter(id => id !== file.id));
                                }
                              }}
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {isImageFile(file.mime_type) && file.thumbnail_url ? (
                                <img
                                  src={file.thumbnail_url}
                                  alt={file.name}
                                  className="h-8 w-8 object-cover rounded mr-3"
                                />
                              ) : (
                                <div className="h-8 w-8 bg-gray-100 rounded flex items-center justify-center mr-3">
                                  {getFileIcon(file.mime_type)}
                                </div>
                              )}
                              <div>
                                <div className="text-sm font-medium text-gray-900">{file.name}</div>
                                <div className="text-sm text-gray-500">{file.original_name}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {file.mime_type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatFileSize(file.size)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(file.created_at).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex justify-end space-x-2">
                              <button
                                onClick={() => setPreviewFile(file)}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => setEditingFile(file)}
                                className="text-yellow-600 hover:text-yellow-900"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </button>
                              <a
                                href={file.url}
                                download
                                className="text-green-600 hover:text-green-900"
                              >
                                <ArrowDownTrayIcon className="h-4 w-4" />
                              </a>
                              <button
                                onClick={() => deleteFileMutation.mutate(file.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
              
              {/* Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="mt-6 flex justify-between items-center">
                  <p className="text-sm text-gray-700">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} files
                  </p>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === pagination.totalPages}
                      className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {/* Upload Modal */}
      <UploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleFileUpload}
        isUploading={uploadMutation.isPending}
      />

      {/* Folder Modal */}
      <FolderModal
        isOpen={showFolderModal}
        onClose={() => setShowFolderModal(false)}
        folder={editingFolder}
        onSubmit={(data) => createFolderMutation.mutate(data)}
        isSubmitting={createFolderMutation.isPending}
      />

      {/* Move Files Modal */}
      <MoveFilesModal
        isOpen={showMoveModal}
        onClose={() => setShowMoveModal(false)}
        fileIds={selectedFiles}
        onMove={(folderId) => moveFilesMutation.mutate({ fileIds: selectedFiles, folder_id: folderId })}
        isMoving={moveFilesMutation.isPending}
      />

      {/* File Preview Modal */}
      {previewFile && (
        <FilePreviewModal
          file={previewFile}
          onClose={() => setPreviewFile(null)}
        />
      )}

      {/* Hidden file input for drag and drop */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        onChange={(e) => {
          if (e.target.files) {
            handleFileUpload(e.target.files);
          }
        }}
      />
    </div>
  );
};

// Upload Modal Component
interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (files: FileList) => void;
  isUploading: boolean;
}

const UploadModal: React.FC<UploadModalProps> = ({ isOpen, onClose, onUpload, isUploading }) => {
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      onUpload(e.dataTransfer.files);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-md w-full mx-4">
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-lg font-medium">Upload Files</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        
        <div className="p-6">
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center ${
              dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <CloudArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-900 mb-2">
              Drop files here to upload
            </p>
            <p className="text-gray-500 mb-4">or</p>
            <button
              onClick={() => fileInputRef.current?.click()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              disabled={isUploading}
            >
              {isUploading ? 'Uploading...' : 'Choose Files'}
            </button>
            <p className="text-xs text-gray-500 mt-2">
              Maximum file size: 10MB
            </p>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            onChange={(e) => {
              if (e.target.files) {
                onUpload(e.target.files);
              }
            }}
          />
        </div>
      </div>
    </div>
  );
};

// Folder Modal Component
interface FolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  folder?: Folder | null;
  onSubmit: (data: { name: string; description?: string }) => void;
  isSubmitting: boolean;
}

const FolderModal: React.FC<FolderModalProps> = ({ isOpen, onClose, folder, onSubmit, isSubmitting }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });

  React.useEffect(() => {
    if (folder) {
      setFormData({
        name: folder.name,
        description: folder.metadata?.description || ''
      });
    } else {
      setFormData({ name: '', description: '' });
    }
  }, [folder]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name.trim()) {
      onSubmit({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-md w-full mx-4">
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-lg font-medium">
            {folder ? 'Edit Folder' : 'Create New Folder'}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Folder Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter folder name"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description (Optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter folder description"
              rows={3}
            />
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !formData.name.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isSubmitting ? 'Creating...' : folder ? 'Update' : 'Create'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Move Files Modal Component
interface MoveFilesModalProps {
  isOpen: boolean;
  onClose: () => void;
  fileIds: string[];
  onMove: (folderId: string | null) => void;
  isMoving: boolean;
}

const MoveFilesModal: React.FC<MoveFilesModalProps> = ({ isOpen, onClose, fileIds, onMove, isMoving }) => {
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);

  // Fetch all folders for the move operation
  const { data: allFoldersData } = useQuery({
    queryKey: ['all-folders'],
    queryFn: () => api.get('/admin/api/files/folders').then(res => res.data),
    enabled: isOpen
  });

  const handleMove = () => {
    onMove(selectedFolder);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-md w-full mx-4">
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-lg font-medium">
            Move {fileIds.length} file{fileIds.length > 1 ? 's' : ''}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        
        <div className="p-6">
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="folder"
                checked={selectedFolder === null}
                onChange={() => setSelectedFolder(null)}
                className="mr-2"
              />
              <span>Root folder</span>
            </label>
            
            {allFoldersData?.folders?.map((folder: Folder) => (
              <label key={folder.id} className="flex items-center">
                <input
                  type="radio"
                  name="folder"
                  checked={selectedFolder === folder.id}
                  onChange={() => setSelectedFolder(folder.id)}
                  className="mr-2"
                />
                <span>{folder.path}</span>
              </label>
            ))}
          </div>
          
          <div className="flex justify-end space-x-3 pt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleMove}
              disabled={isMoving}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isMoving ? 'Moving...' : 'Move Files'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// File Preview Modal Component
interface FilePreviewModalProps {
  file: FileItem;
  onClose: () => void;
}

const FilePreviewModal: React.FC<FilePreviewModalProps> = ({ file, onClose }) => {
  const isImage = file.mime_type.startsWith('image/');
  const isVideo = file.mime_type.startsWith('video/');
  const isAudio = file.mime_type.startsWith('audio/');

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl max-h-screen w-full mx-4 overflow-hidden">
        <div className="flex justify-between items-center p-4 border-b">
          <div>
            <h3 className="text-lg font-medium">{file.name}</h3>
            <p className="text-sm text-gray-500">
              {file.mime_type} • {file.size ? `${(file.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size'}
            </p>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        
        <div className="p-4 max-h-96 overflow-auto">
          {isImage ? (
            <img
              src={file.url}
              alt={file.name}
              className="max-w-full max-h-full object-contain mx-auto"
            />
          ) : isVideo ? (
            <video
              src={file.url}
              controls
              className="max-w-full max-h-full mx-auto"
            />
          ) : isAudio ? (
            <audio
              src={file.url}
              controls
              className="w-full"
            />
          ) : (
            <div className="text-center py-12">
              <DocumentIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Preview not available for this file type</p>
              <a
                href={file.url}
                download
                className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Download File
              </a>
            </div>
          )}
        </div>
        
        <div className="p-4 border-t bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              Created: {new Date(file.created_at).toLocaleString()}
            </div>
            <a
              href={file.url}
              download
              className="inline-flex items-center px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
              Download
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Files; 