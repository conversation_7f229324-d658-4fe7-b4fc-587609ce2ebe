import { Hono } from 'hono';
import { WorkerEnv } from 'handmadein-shared';
import { DatabaseService } from '../../services/database';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import { sign, verify } from '@tsndr/cloudflare-worker-jwt';
import { eq, desc } from 'drizzle-orm';

export const customerRoutes = new Hono<{ Bindings: WorkerEnv }>();

// Validation schemas
const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  first_name: z.string().min(1),
  last_name: z.string().min(1),
  phone: z.string().optional(),
});

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string(),
});

const updateProfileSchema = z.object({
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  phone: z.string().optional(),
});

// Helper function to generate JWT
async function generateToken(customer: any, jwtSecret: string) {
  return await sign(
    {
      sub: customer.id,
      email: customer.email,
      role: 'customer',
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
    },
    jwtSecret
  );
}

// Register new customer
customerRoutes.post('/register', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const body = await c.req.json();
    
    // Validate input data with detailed error messages
    let validatedData;
    try {
      validatedData = registerSchema.parse(body);
    } catch (zodError) {
      if (zodError instanceof z.ZodError) {
        const firstError = zodError.errors[0];
        let errorMessage = 'Datele introduse nu sunt valide.';
        
        if (firstError.path.includes('email')) {
          errorMessage = 'Te rugăm să introduci o adresă de email validă.';
        } else if (firstError.path.includes('password')) {
          errorMessage = 'Parola trebuie să aibă cel puțin 8 caractere.';
        } else if (firstError.path.includes('first_name')) {
          errorMessage = 'Te rugăm să introduci prenumele.';
        } else if (firstError.path.includes('last_name')) {
          errorMessage = 'Te rugăm să introduci numele de familie.';
        }
        
        return c.json({
          success: false,
          error: errorMessage,
          message: errorMessage,
          code: 'VALIDATION_ERROR'
        }, 400);
      }
    }

    // Ensure we have validated data before proceeding
    if (!validatedData) {
      return c.json({
        success: false,
        error: 'Datele introduse nu sunt valide.',
        message: 'Te rugăm să completezi toate câmpurile obligatorii.',
        code: 'VALIDATION_ERROR'
      }, 400);
    }

    // Check if customer already exists
    const existingCustomer = await db.findCustomerByEmail(validatedData.email);
    if (existingCustomer && existingCustomer.has_account) {
      return c.json({
        success: false,
        error: 'Un cont cu această adresă de email există deja.',
        message: 'Un cont cu această adresă de email există deja. Te rugăm să te autentifici sau să folosești o altă adresă de email.',
        code: 'EMAIL_ALREADY_EXISTS'
      }, 409); // 409 Conflict is more appropriate than 400
    }

    // Validate password strength
    if (validatedData.password.length < 8) {
      return c.json({
        success: false,
        error: 'Parola trebuie să aibă cel puțin 8 caractere.',
        message: 'Te rugăm să alegi o parolă cu cel puțin 8 caractere pentru securitatea contului tău.',
        code: 'PASSWORD_TOO_SHORT'
      }, 400);
    }

    // Hash password
    let passwordHash;
    try {
      passwordHash = await bcrypt.hash(validatedData.password, 12);
    } catch (hashError) {
      console.error('Password hashing error:', hashError);
      return c.json({
        success: false,
        error: 'A apărut o eroare la crearea contului.',
        message: 'Te rugăm să încerci din nou în câteva momente.',
        code: 'INTERNAL_ERROR'
      }, 500);
    }

    // Create or update customer record
    let customer;
    try {
      if (existingCustomer) {
        // Update existing customer to have an account
        const updateQuery = `
          UPDATE customer 
          SET has_account = 1, 
              first_name = ?, 
              last_name = ?, 
              phone = ?, 
              updated_at = ? 
          WHERE id = ?
        `;
        
        await c.env.DB.prepare(updateQuery).bind(
          validatedData.first_name,
          validatedData.last_name,
          validatedData.phone || null,
          new Date().toISOString(),
          existingCustomer.id
        ).run();
        
        // Get updated customer
        const getQuery = `SELECT * FROM customer WHERE id = ?`;
        customer = await c.env.DB.prepare(getQuery).bind(existingCustomer.id).first();
      } else {
        // Create new customer
        const customerId = `cus_${crypto.randomUUID().replace(/-/g, '').substring(0, 26).toUpperCase()}`;
        const createQuery = `
          INSERT INTO customer (id, email, first_name, last_name, phone, has_account, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, 1, ?, ?)
        `;
        
        await c.env.DB.prepare(createQuery).bind(
          customerId,
          validatedData.email,
          validatedData.first_name,
          validatedData.last_name,
          validatedData.phone || null,
          new Date().toISOString(),
          new Date().toISOString()
        ).run();
        
        // Get created customer
        const getQuery = `SELECT * FROM customer WHERE id = ?`;
        customer = await c.env.DB.prepare(getQuery).bind(customerId).first();
      }
    } catch (dbError) {
      console.error('Database error during customer creation/update:', dbError);
      return c.json({
        success: false,
        error: 'A apărut o eroare la crearea contului.',
        message: 'Te rugăm să încerci din nou în câteva momente.',
        code: 'DATABASE_ERROR'
      }, 500);
    }

    // Create provider_identity record for authentication
    try {
      const providerIdentityId = `01${crypto.randomUUID().replace(/-/g, '').substring(0, 24).toUpperCase()}`;
      const authIdentityId = `authid_01${crypto.randomUUID().replace(/-/g, '').substring(0, 21).toUpperCase()}`;
      
      const providerMetadata = JSON.stringify({
        password: passwordHash
      });
      
      const insertProviderQuery = `
        INSERT OR REPLACE INTO provider_identity 
        (id, entity_id, provider, auth_identity_id, user_metadata, provider_metadata, created_at, updated_at)
        VALUES (?, ?, 'emailpass', ?, NULL, ?, ?, ?)
      `;
      
      await c.env.DB.prepare(insertProviderQuery).bind(
        providerIdentityId,
        validatedData.email,
        authIdentityId,
        providerMetadata,
        new Date().toISOString(),
        new Date().toISOString()
      ).run();
    } catch (providerError) {
      console.error('Error creating provider_identity record:', providerError);
      // Don't fail registration if provider_identity creation fails, but log it
      console.warn('Customer created but provider_identity failed - authentication may not work');
    }

    // Generate JWT token
    let token;
    try {
      token = await generateToken(customer, c.env.JWT_SECRET);
    } catch (tokenError) {
      console.error('Token generation error:', tokenError);
      return c.json({
        success: false,
        error: 'A apărut o eroare la autentificare.',
        message: 'Contul a fost creat, dar te rugăm să te autentifici manual.',
        code: 'TOKEN_ERROR'
      }, 500);
    }

    // Send welcome email (don't fail registration if email fails)
    let emailSent = false;
    try {
      const { EmailService } = await import('../../services/email');
      const emailService = new EmailService(c.env);
      emailSent = await emailService.sendWelcomeEmail(customer);
      
      if (emailSent) {
        console.log(`Welcome email sent successfully to ${customer.email}`);
      } else {
        console.warn(`Failed to send welcome email to ${customer.email}`);
      }
    } catch (emailError) {
      console.error('Welcome email error:', emailError);
      // Continue with successful registration even if email fails
    }

    return c.json({
      success: true,
      message: 'Contul tău a fost creat cu succes! Bine ai venit la Handmade in RO!',
      data: {
        customer,
        token,
        email_sent: emailSent
      },
      code: 'REGISTRATION_SUCCESS'
    }, 201); // 201 Created is more appropriate for successful creation
    
  } catch (error) {
    console.error('Unexpected error during customer registration:', error);
    return c.json({
      success: false,
      error: 'A apărut o eroare neașteptată.',
      message: 'Te rugăm să încerci din nou în câteva momente. Dacă problema persistă, contactează-<NAME_EMAIL>',
      code: 'UNEXPECTED_ERROR'
    }, 500);
  }
});

// Login customer
customerRoutes.post('/login', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const body = await c.req.json();
    
    // Validate input data
    let validatedData;
    try {
      validatedData = loginSchema.parse(body);
    } catch (zodError) {
      if (zodError instanceof z.ZodError) {
        const firstError = zodError.errors[0];
        let errorMessage = 'Datele introduse nu sunt valide.';
        
        if (firstError.path.includes('email')) {
          errorMessage = 'Te rugăm să introduci o adresă de email validă.';
        } else if (firstError.path.includes('password')) {
          errorMessage = 'Te rugăm să introduci parola.';
        }
        
        return c.json({
          success: false,
          error: errorMessage,
          message: errorMessage,
          code: 'VALIDATION_ERROR'
        }, 400);
      }
    }

    // Ensure we have validated data
    if (!validatedData) {
      return c.json({
        success: false,
        error: 'Te rugăm să completezi toate câmpurile.',
        message: 'Email-ul și parola sunt obligatorii pentru autentificare.',
        code: 'VALIDATION_ERROR'
      }, 400);
    }

    // Find customer by email
    const customer = await db.findCustomerByEmail(validatedData.email);
    if (!customer || !customer.has_account) {
      return c.json({
        success: false,
        error: 'Email sau parolă incorectă.',
        message: 'Nu am găsit un cont cu această combinație de email și parolă. Te rugăm să verifici datele introduse.',
        code: 'INVALID_CREDENTIALS'
      }, 401);
    }

    // Get password from provider_identity table
    let providerIdentity;
    try {
      const providerQuery = `
        SELECT provider_metadata FROM provider_identity 
        WHERE entity_id = ? AND provider = 'emailpass'
      `;
      providerIdentity = await c.env.DB.prepare(providerQuery).bind(validatedData.email).first();
    } catch (providerError) {
      console.error('Error fetching provider_identity:', providerError);
      return c.json({
        success: false,
        error: 'A apărut o eroare la autentificare.',
        message: 'Te rugăm să încerci din nou în câteva momente.',
        code: 'AUTH_ERROR'
      }, 500);
    }

    if (!providerIdentity || !providerIdentity.provider_metadata) {
      return c.json({
        success: false,
        error: 'Email sau parolă incorectă.',
        message: 'Nu am găsit un cont cu această combinație de email și parolă. Te rugăm să verifici datele introduse.',
        code: 'INVALID_CREDENTIALS'
      }, 401);
    }

    // Parse provider metadata and verify password
    let storedPasswordHash;
    try {
      const metadata = JSON.parse(providerIdentity.provider_metadata);
      storedPasswordHash = metadata.password;
    } catch (parseError) {
      console.error('Error parsing provider_metadata:', parseError);
      return c.json({
        success: false,
        error: 'A apărut o eroare la autentificare.',
        message: 'Te rugăm să încerci din nou în câteva momente.',
        code: 'AUTH_ERROR'
      }, 500);
    }

    // Verify password
    let isValidPassword = false;
    try {
      isValidPassword = await bcrypt.compare(validatedData.password, storedPasswordHash);
    } catch (compareError) {
      console.error('Error comparing passwords:', compareError);
      return c.json({
        success: false,
        error: 'A apărut o eroare la autentificare.',
        message: 'Te rugăm să încerci din nou în câteva momente.',
        code: 'AUTH_ERROR'
      }, 500);
    }

    if (!isValidPassword) {
      return c.json({
        success: false,
        error: 'Email sau parolă incorectă.',
        message: 'Nu am găsit un cont cu această combinație de email și parolă. Te rugăm să verifici datele introduse.',
        code: 'INVALID_CREDENTIALS'
      }, 401);
    }

    // Generate JWT token
    let token;
    try {
      token = await generateToken(customer, c.env.JWT_SECRET);
    } catch (tokenError) {
      console.error('Token generation error during login:', tokenError);
      return c.json({
        success: false,
        error: 'A apărut o eroare la autentificare.',
        message: 'Te rugăm să încerci din nou în câteva momente.',
        code: 'TOKEN_ERROR'
      }, 500);
    }

    return c.json({
      success: true,
      message: `Bine ai revenit, ${customer.first_name || 'Client'}!`,
      data: {
        customer,
        token,
      },
      code: 'LOGIN_SUCCESS'
    });
  } catch (error) {
    console.error('Unexpected error during customer login:', error);
    return c.json({
      success: false,
      error: 'A apărut o eroare neașteptată la autentificare.',
      message: 'Te rugăm să încerci din nou în câteva momente. Dacă problema persistă, contactează-<NAME_EMAIL>',
      code: 'UNEXPECTED_ERROR'
    }, 500);
  }
});

// Get customer profile (requires authentication)
customerRoutes.get('/profile', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    
    // Get customer ID from JWT token (this would be set by auth middleware)
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    const customer = await db.findById('customer', payload.userId);
    if (!customer) {
      return c.json({
        success: false,
        error: 'Customer not found',
      }, 404);
    }

    return c.json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Error fetching customer profile:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch profile',
    }, 500);
  }
});

// GET /customers/me - Alias for profile (used by frontend)
customerRoutes.get('/me', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    
    // Get customer ID from JWT token (this would be set by auth middleware)
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    const customer = await db.findById('customer', payload.userId);
    if (!customer) {
      return c.json({
        success: false,
        error: 'Customer not found',
      }, 404);
    }

    return c.json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Error fetching customer profile:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch profile',
    }, 500);
  }
});

// GET /customers/me/orders - Get customer orders (used by frontend)
customerRoutes.get('/me/orders', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Get customer orders using raw SQL
    const ordersQuery = `
      SELECT * FROM "order" 
      WHERE customer_id = ? AND (deleted_at IS NULL OR deleted_at = '')
      ORDER BY created_at DESC
    `;
    
    const ordersResult = await c.env.DB.prepare(ordersQuery).bind(payload.userId).all();
    const orders = ordersResult.results || [];

    // Get items for each order
    for (const order of orders) {
      const itemsQuery = `
        SELECT * FROM line_item 
        WHERE order_id = ? AND (deleted_at IS NULL OR deleted_at = '')
      `;
      const itemsResult = await c.env.DB.prepare(itemsQuery).bind(order.id).all();
      order.items = itemsResult.results || [];
    }

    return c.json({
      success: true,
      data: orders,
    });
  } catch (error) {
    console.error('Error fetching customer orders:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch orders',
    }, 500);
  }
});

// GET /customers/me/addresses - Get customer addresses (used by frontend)
customerRoutes.get('/me/addresses', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Get customer addresses using raw SQL
    const addressesQuery = `
      SELECT * FROM customer_address 
      WHERE customer_id = ? AND (deleted_at IS NULL OR deleted_at = '')
      ORDER BY is_default_shipping DESC, is_default_billing DESC, created_at DESC
    `;
    
    const addressResult = await c.env.DB.prepare(addressesQuery).bind(payload.userId).all();
    const addresses = addressResult.results || [];

    return c.json({
      success: true,
      data: addresses,
    });
  } catch (error) {
    console.error('Error fetching customer addresses:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch addresses',
    }, 500);
  }
});

// POST /customers/me/addresses - Add new address (used by frontend)
customerRoutes.post('/me/addresses', async (c) => {
  try {
    const body = await c.req.json();
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));
    const addressId = crypto.randomUUID();

    // If setting as default shipping, remove default from other addresses
    if (body.is_default_shipping) {
      const removeShippingDefaultQuery = `UPDATE customer_address SET is_default_shipping = 0, updated_at = ? WHERE customer_id = ? AND id != ?`;
      await c.env.DB.prepare(removeShippingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId,
        addressId
      ).run();
    }
    
    // If setting as default billing, remove default from other addresses
    if (body.is_default_billing) {
      const removeBillingDefaultQuery = `UPDATE customer_address SET is_default_billing = 0, updated_at = ? WHERE customer_id = ? AND id != ?`;
      await c.env.DB.prepare(removeBillingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId,
        addressId
      ).run();
    }

    // Create address using raw SQL
    const createQuery = `
      INSERT INTO customer_address (
        id, customer_id, first_name, last_name, company, address_1, address_2, 
        city, country_code, province, postal_code, phone, is_default_shipping, 
        is_default_billing, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await c.env.DB.prepare(createQuery).bind(
      addressId,
      payload.userId,
      body.first_name || '',
      body.last_name || '',
      body.company || '',
      body.address_1 || '',
      body.address_2 || '',
      body.city || '',
      body.country_code || 'RO',
      body.province || '',
      body.postal_code || '',
      body.phone || '',
      body.is_default_shipping ? 1 : 0,
      body.is_default_billing ? 1 : 0,
      JSON.stringify(body.metadata || {}),
      new Date().toISOString(),
      new Date().toISOString()
    ).run();

    // Get the created address
    const getQuery = `SELECT * FROM customer_address WHERE id = ?`;
    const result = await c.env.DB.prepare(getQuery).bind(addressId).first();

    return c.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error creating address:', error);
    return c.json({
      success: false,
      error: 'Failed to create address',
    }, 500);
  }
});

// PUT /customers/me/addresses/:id - Update address (used by frontend)
customerRoutes.put('/me/addresses/:id', async (c) => {
  try {
    const addressId = c.req.param('id');
    const body = await c.req.json();
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Verify address belongs to customer
    const checkQuery = `SELECT * FROM customer_address WHERE id = ? AND customer_id = ?`;
    const existingAddress = await c.env.DB.prepare(checkQuery).bind(addressId, payload.userId).first();
    
    if (!existingAddress) {
      return c.json({
        success: false,
        error: 'Address not found',
      }, 404);
    }

    // If setting as default shipping, remove default from other addresses
    if (body.is_default_shipping) {
      const removeShippingDefaultQuery = `UPDATE customer_address SET is_default_shipping = 0, updated_at = ? WHERE customer_id = ? AND id != ?`;
      await c.env.DB.prepare(removeShippingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId,
        addressId
      ).run();
    }
    
    // If setting as default billing, remove default from other addresses
    if (body.is_default_billing) {
      const removeBillingDefaultQuery = `UPDATE customer_address SET is_default_billing = 0, updated_at = ? WHERE customer_id = ? AND id != ?`;
      await c.env.DB.prepare(removeBillingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId,
        addressId
      ).run();
    }

    // Update address using raw SQL
    const updateQuery = `
      UPDATE customer_address SET
        first_name = ?, last_name = ?, company = ?, address_1 = ?, address_2 = ?,
        city = ?, country_code = ?, province = ?, postal_code = ?, phone = ?,
        is_default_shipping = ?, is_default_billing = ?, metadata = ?, updated_at = ?
      WHERE id = ?
    `;
    
    await c.env.DB.prepare(updateQuery).bind(
      body.first_name || existingAddress.first_name,
      body.last_name || existingAddress.last_name,
      body.company || existingAddress.company,
      body.address_1 || existingAddress.address_1,
      body.address_2 || existingAddress.address_2,
      body.city || existingAddress.city,
      body.country_code || existingAddress.country_code,
      body.province || existingAddress.province,
      body.postal_code || existingAddress.postal_code,
      body.phone || existingAddress.phone,
      body.is_default_shipping ? 1 : 0,
      body.is_default_billing ? 1 : 0,
      JSON.stringify(body.metadata || {}),
      new Date().toISOString(),
      addressId
    ).run();

    // Get updated address
    const getQuery = `SELECT * FROM customer_address WHERE id = ?`;
    const result = await c.env.DB.prepare(getQuery).bind(addressId).first();

    return c.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error updating address:', error);
    return c.json({
      success: false,
      error: 'Failed to update address',
    }, 500);
  }
});

// DELETE /customers/me/addresses/:id - Delete address (used by frontend)
customerRoutes.delete('/me/addresses/:id', async (c) => {
  try {
    const addressId = c.req.param('id');
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Verify address belongs to customer
    const checkQuery = `SELECT * FROM customer_address WHERE id = ? AND customer_id = ?`;
    const existingAddress = await c.env.DB.prepare(checkQuery).bind(addressId, payload.userId).first();
    
    if (!existingAddress) {
      return c.json({
        success: false,
        error: 'Address not found',
      }, 404);
    }

    // Soft delete address
    const deleteQuery = `UPDATE customer_address SET deleted_at = ?, updated_at = ? WHERE id = ?`;
    await c.env.DB.prepare(deleteQuery).bind(
      new Date().toISOString(),
      new Date().toISOString(),
      addressId
    ).run();

    return c.json({
      success: true,
      message: 'Address deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting address:', error);
    return c.json({
      success: false,
      error: 'Failed to delete address',
    }, 500);
  }
});

// POST /customers/me/addresses/:id/default - Set default address (used by frontend)
customerRoutes.post('/me/addresses/:id/default', async (c) => {
  try {
    const addressId = c.req.param('id');
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Verify address belongs to customer
    const checkQuery = `SELECT * FROM customer_address WHERE id = ? AND customer_id = ?`;
    const existingAddress = await c.env.DB.prepare(checkQuery).bind(addressId, payload.userId).first();
    
    if (!existingAddress) {
      return c.json({
        success: false,
        error: 'Address not found',
      }, 404);
    }

    // Remove default flag from all customer addresses using raw SQL
    // We need to determine what type of default this should be from the request body or metadata
    const body = await c.req.json();
    const isShippingDefault = body.is_default_shipping || body.address_type === 'shipping' || body.address_type === 'both';
    const isBillingDefault = body.is_default_billing || body.address_type === 'billing' || body.address_type === 'both';
    
    if (isShippingDefault) {
      const removeShippingDefaultQuery = `UPDATE customer_address SET is_default_shipping = 0, updated_at = ? WHERE customer_id = ? AND id != ?`;
      await c.env.DB.prepare(removeShippingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId,
        addressId
      ).run();
      
      const setShippingDefaultQuery = `UPDATE customer_address SET is_default_shipping = 1, updated_at = ? WHERE id = ?`;
      await c.env.DB.prepare(setShippingDefaultQuery).bind(
        new Date().toISOString(),
        addressId
      ).run();
    }
    
    if (isBillingDefault) {
      const removeBillingDefaultQuery = `UPDATE customer_address SET is_default_billing = 0, updated_at = ? WHERE customer_id = ? AND id != ?`;
      await c.env.DB.prepare(removeBillingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId,
        addressId
      ).run();
      
      const setBillingDefaultQuery = `UPDATE customer_address SET is_default_billing = 1, updated_at = ? WHERE id = ?`;
      await c.env.DB.prepare(setBillingDefaultQuery).bind(
        new Date().toISOString(),
        addressId
      ).run();
    }

    // Get updated address
    const getQuery = `SELECT * FROM customer_address WHERE id = ?`;
    const result = await c.env.DB.prepare(getQuery).bind(addressId).first();

    return c.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error setting default address:', error);
    return c.json({
      success: false,
      error: 'Failed to set default address',
    }, 500);
  }
});

// Update customer profile
customerRoutes.put('/profile', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const body = await c.req.json();
    
    const validatedData = updateProfileSchema.parse(body);

    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    const updatedCustomer = await db.update('customer', payload.userId, validatedData);
    if (!updatedCustomer) {
      return c.json({
        success: false,
        error: 'Customer not found',
      }, 404);
    }

    return c.json({
      success: true,
      data: updatedCustomer,
    });
  } catch (error) {
    console.error('Error updating customer profile:', error);
    return c.json({
      success: false,
      error: error instanceof z.ZodError ? 'Invalid input data' : 'Failed to update profile',
    }, 400);
  }
});

// Get customer addresses
customerRoutes.get('/addresses', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Get customer addresses using raw SQL
    const addressesQuery = `
      SELECT * FROM customer_address 
      WHERE customer_id = ? AND (deleted_at IS NULL OR deleted_at = '')
      ORDER BY is_default_shipping DESC, is_default_billing DESC, created_at DESC
    `;
    
    const addressResult = await c.env.DB.prepare(addressesQuery).bind(payload.userId).all();
    const addresses = addressResult.results || [];

    return c.json({
      success: true,
      data: addresses,
    });
  } catch (error) {
    console.error('Error fetching customer addresses:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch addresses',
    }, 500);
  }
});

// Get customer orders
customerRoutes.get('/orders', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Get customer orders using raw SQL
    const ordersQuery = `
      SELECT * FROM "order" 
      WHERE customer_id = ? AND (deleted_at IS NULL OR deleted_at = '')
      ORDER BY created_at DESC
    `;
    
    const ordersResult = await c.env.DB.prepare(ordersQuery).bind(payload.userId).all();
    const orders = ordersResult.results || [];

    // Get items for each order
    for (const order of orders) {
      const itemsQuery = `
        SELECT * FROM line_item 
        WHERE order_id = ? AND (deleted_at IS NULL OR deleted_at = '')
      `;
      const itemsResult = await c.env.DB.prepare(itemsQuery).bind(order.id).all();
      order.items = itemsResult.results || [];
    }

    return c.json({
      success: true,
      data: orders,
    });
  } catch (error) {
    console.error('Error fetching customer orders:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch orders',
    }, 500);
  }
});

// POST /customers/addresses - Add new address
customerRoutes.post('/addresses', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const body = await c.req.json();
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // If setting as default shipping, remove default from other addresses
    if (body.is_default_shipping) {
      const removeShippingDefaultQuery = `UPDATE addresses SET is_default_shipping = 0, updated_at = ? WHERE customer_id = ?`;
      await c.env.DB.prepare(removeShippingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId
      ).run();
    }
    
    // If setting as default billing, remove default from other addresses
    if (body.is_default_billing) {
      const removeBillingDefaultQuery = `UPDATE addresses SET is_default_billing = 0, updated_at = ? WHERE customer_id = ?`;
      await c.env.DB.prepare(removeBillingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId
      ).run();
    }

    // Create address
    const address = await db.create('addresses', {
      id: crypto.randomUUID(),
      customer_id: payload.userId,
      first_name: body.first_name || '',
      last_name: body.last_name || '',
      company: body.company || '',
      address_1: body.address_1 || '',
      address_2: body.address_2 || '',
      city: body.city || '',
      country_code: body.country_code || 'RO',
      province: body.province || '',
      postal_code: body.postal_code || '',
      phone: body.phone || '',
      metadata: JSON.stringify(body.metadata || {}),
    });

    return c.json({
      success: true,
      data: address,
    });
  } catch (error) {
    console.error('Error creating address:', error);
    return c.json({
      success: false,
      error: 'Failed to create address',
    }, 500);
  }
});

// PUT /customers/addresses/:id - Update address
customerRoutes.put('/addresses/:id', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const addressId = c.req.param('id');
    const body = await c.req.json();
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Verify address belongs to customer
    const existingAddress = await db.findById('addresses', addressId);
    if (!existingAddress || existingAddress.customer_id !== payload.userId) {
      return c.json({
        success: false,
        error: 'Address not found',
      }, 404);
    }

    // If setting as default shipping, remove default from other addresses
    if (body.is_default_shipping) {
      const removeShippingDefaultQuery = `UPDATE addresses SET is_default_shipping = 0, updated_at = ? WHERE customer_id = ?`;
      await c.env.DB.prepare(removeShippingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId
      ).run();
    }
    
    // If setting as default billing, remove default from other addresses
    if (body.is_default_billing) {
      const removeBillingDefaultQuery = `UPDATE addresses SET is_default_billing = 0, updated_at = ? WHERE customer_id = ?`;
      await c.env.DB.prepare(removeBillingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId
      ).run();
    }

    // Update address
    const updatedAddress = await db.update('addresses', addressId, {
      first_name: body.first_name || existingAddress.first_name,
      last_name: body.last_name || existingAddress.last_name,
      company: body.company || existingAddress.company,
      address_1: body.address_1 || existingAddress.address_1,
      address_2: body.address_2 || existingAddress.address_2,
      city: body.city || existingAddress.city,
      country_code: body.country_code || existingAddress.country_code,
      province: body.province || existingAddress.province,
      postal_code: body.postal_code || existingAddress.postal_code,
      phone: body.phone || existingAddress.phone,
      metadata: JSON.stringify(body.metadata || {}),
    });

    return c.json({
      success: true,
      data: updatedAddress,
    });
  } catch (error) {
    console.error('Error updating address:', error);
    return c.json({
      success: false,
      error: 'Failed to update address',
    }, 500);
  }
});

// DELETE /customers/addresses/:id - Delete address
customerRoutes.delete('/addresses/:id', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const addressId = c.req.param('id');
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Verify address belongs to customer
    const existingAddress = await db.findById('addresses', addressId);
    if (!existingAddress || existingAddress.customer_id !== payload.userId) {
      return c.json({
        success: false,
        error: 'Address not found',
      }, 404);
    }

    // Delete address
    await db.softDelete('addresses', addressId);

    return c.json({
      success: true,
      message: 'Address deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting address:', error);
    return c.json({
      success: false,
      error: 'Failed to delete address',
    }, 500);
  }
});

// POST /customers/addresses/:id/default - Set default address
customerRoutes.post('/addresses/:id/default', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const addressId = c.req.param('id');
    
    // Get customer ID from JWT token
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const token = authHeader.substring(7);
    const isValid = await verify(token, c.env.JWT_SECRET);
    if (!isValid) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    const payload = JSON.parse(atob(token.split('.')[1]));

    // Verify address belongs to customer
    const existingAddress = await db.findById('addresses', addressId);
    if (!existingAddress || existingAddress.customer_id !== payload.userId) {
      return c.json({
        success: false,
        error: 'Address not found',
      }, 404);
    }

    // Remove default flag from all customer addresses using raw SQL
    // We need to determine what type of default this should be from the request body or metadata
    const body = await c.req.json();
    const isShippingDefault = body.is_default_shipping || body.address_type === 'shipping' || body.address_type === 'both';
    const isBillingDefault = body.is_default_billing || body.address_type === 'billing' || body.address_type === 'both';
    
    if (isShippingDefault) {
      const removeShippingDefaultQuery = `UPDATE addresses SET is_default_shipping = 0, updated_at = ? WHERE customer_id = ?`;
      await c.env.DB.prepare(removeShippingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId
      ).run();
      
      const setShippingDefaultQuery = `UPDATE addresses SET is_default_shipping = 1, updated_at = ? WHERE id = ?`;
      await c.env.DB.prepare(setShippingDefaultQuery).bind(
        new Date().toISOString(),
        addressId
      ).run();
    }
    
    if (isBillingDefault) {
      const removeBillingDefaultQuery = `UPDATE addresses SET is_default_billing = 0, updated_at = ? WHERE customer_id = ?`;
      await c.env.DB.prepare(removeBillingDefaultQuery).bind(
        new Date().toISOString(),
        payload.userId
      ).run();
      
      const setBillingDefaultQuery = `UPDATE addresses SET is_default_billing = 1, updated_at = ? WHERE id = ?`;
      await c.env.DB.prepare(setBillingDefaultQuery).bind(
        new Date().toISOString(),
        addressId
      ).run();
    }

    // Get updated address
    const getQuery = `SELECT * FROM addresses WHERE id = ?`;
    const result = await c.env.DB.prepare(getQuery).bind(addressId).first();

    return c.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error setting default address:', error);
    return c.json({
      success: false,
      error: 'Failed to set default address',
    }, 500);
  }
}); 