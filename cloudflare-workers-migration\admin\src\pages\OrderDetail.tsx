import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  ArrowLeftIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  PencilIcon,
  PrinterIcon,
  EnvelopeIcon,
  UserIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';

interface Order {
  id: string;
  number: string;
  customer_id?: string;
  email: string;
  currency_code: string;
  region_id: string;
  status: string;
  financial_status: string;
  fulfillment_status: string;
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total_amount: number;
  billing_address: Address;
  shipping_address: Address;
  notes?: string;
  customer?: Customer;
  items: OrderItem[];
  payments: Payment[];
  fulfillments: Fulfillment[];
  created_at: string;
  updated_at: string;
  cancelled_at?: string;
}

interface Customer {
  id: string;
  first_name?: string;
  last_name?: string;
  email: string;
  phone?: string;
}

interface Address {
  first_name?: string;
  last_name?: string;
  company?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state?: string;
  postal_code: string;
  country_code: string;
  phone?: string;
}

interface OrderItem {
  id: string;
  variant_id: string;
  product_title: string;
  variant_title: string;
  sku?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  fulfilled_quantity: number;
  refunded_quantity: number;
}

interface Payment {
  id: string;
  provider: string;
  type: string;
  status: string;
  amount: number;
  currency_code: string;
  processed_at?: string;
  created_at: string;
}

interface Fulfillment {
  id: string;
  status: string;
  tracking_number?: string;
  tracking_url?: string;
  carrier?: string;
  shipped_at?: string;
  delivered_at?: string;
  items: FulfillmentItem[];
  created_at: string;
}

interface FulfillmentItem {
  order_item_id: string;
  quantity: number;
}

const ORDER_STATUSES = [
  { value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'confirmed', label: 'Confirmed', color: 'bg-blue-100 text-blue-800' },
  { value: 'processing', label: 'Processing', color: 'bg-indigo-100 text-indigo-800' },
  { value: 'shipped', label: 'Shipped', color: 'bg-purple-100 text-purple-800' },
  { value: 'delivered', label: 'Delivered', color: 'bg-green-100 text-green-800' },
  { value: 'cancelled', label: 'Cancelled', color: 'bg-red-100 text-red-800' },
  { value: 'refunded', label: 'Refunded', color: 'bg-gray-100 text-gray-800' },
];

const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [notes, setNotes] = useState('');
  const [showCreateFulfillment, setShowCreateFulfillment] = useState(false);

  // Fetch order details
  const { data: order, isLoading, error } = useQuery({
    queryKey: ['order', id],
    queryFn: async () => {
      const response = await api.get(`/admin/api/orders/${id}`);
      return response.data.order;
    },
    enabled: !!id,
  });

  // Update order status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async ({ status }: { status: string }) => {
      return api.put(`/admin/api/orders/${id}/status`, { status });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['order', id] });
      toast.success('Order status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update order status');
    },
  });

  // Update notes mutation
  const updateNotesMutation = useMutation({
    mutationFn: async ({ notes }: { notes: string }) => {
      return api.put(`/admin/api/orders/${id}/notes`, { notes });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['order', id] });
      toast.success('Notes updated successfully');
      setIsEditingNotes(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update notes');
    },
  });

  // Create fulfillment mutation
  const createFulfillmentMutation = useMutation({
    mutationFn: async (fulfillmentData: any) => {
      return api.post(`/admin/api/orders/${id}/fulfillments`, fulfillmentData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['order', id] });
      toast.success('Fulfillment created successfully');
      setShowCreateFulfillment(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create fulfillment');
    },
  });

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatAddress = (address: Address) => {
    if (!address) return 'No address provided';
    
    const parts = [];
    if (address.first_name || address.last_name) {
      parts.push(`${address.first_name || ''} ${address.last_name || ''}`.trim());
    }
    if (address.company) parts.push(address.company);
    parts.push(address.address_line_1);
    if (address.address_line_2) parts.push(address.address_line_2);
    parts.push(`${address.city}, ${address.state || ''} ${address.postal_code}`.trim());
    parts.push(address.country_code.toUpperCase());
    
    return parts.filter(Boolean).join('\n');
  };

  const getStatusColor = (status: string) => {
    const statusObj = ORDER_STATUSES.find(s => s.value === status);
    return statusObj?.color || 'bg-gray-100 text-gray-800';
  };

  const handleUpdateNotes = () => {
    updateNotesMutation.mutate({ notes });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load order. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/orders')}
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-1" />
            Orders
          </button>
          <span className="text-gray-400">/</span>
          <h1 className="text-2xl font-semibold text-gray-900">
            Order #{order.number}
          </h1>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
            {ORDER_STATUSES.find(s => s.value === order.status)?.label}
          </span>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => window.print()}
            className="flex items-center text-gray-700 border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50"
          >
            <PrinterIcon className="h-5 w-5 mr-2" />
            Print
          </button>
          <select
            value={order.status}
            onChange={(e) => updateStatusMutation.mutate({ status: e.target.value })}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {ORDER_STATUSES.map(status => (
              <option key={status.value} value={status.value}>{status.label}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Items */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Items ({order.items.length})</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {order.items.map((item) => (
                <div key={item.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-start space-x-4">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{item.product_title}</h4>
                          {item.variant_title && (
                            <p className="text-sm text-gray-500">{item.variant_title}</p>
                          )}
                          {item.sku && (
                            <p className="text-xs text-gray-400">SKU: {item.sku}</p>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-medium text-gray-900">
                        {formatCurrency(item.total_price, order.currency_code)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatCurrency(item.unit_price, order.currency_code)} × {item.quantity}
                      </div>
                      {item.fulfilled_quantity > 0 && (
                        <div className="text-xs text-green-600">
                          {item.fulfilled_quantity} fulfilled
                        </div>
                      )}
                      {item.refunded_quantity > 0 && (
                        <div className="text-xs text-red-600">
                          {item.refunded_quantity} refunded
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Order Totals */}
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="text-gray-900">{formatCurrency(order.subtotal, order.currency_code)}</span>
                </div>
                {order.discount_amount > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Discount:</span>
                    <span className="text-red-600">-{formatCurrency(order.discount_amount, order.currency_code)}</span>
                  </div>
                )}
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping:</span>
                  <span className="text-gray-900">{formatCurrency(order.shipping_amount, order.currency_code)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Tax:</span>
                  <span className="text-gray-900">{formatCurrency(order.tax_amount, order.currency_code)}</span>
                </div>
                <div className="flex justify-between text-lg font-semibold pt-2 border-t border-gray-200">
                  <span className="text-gray-900">Total:</span>
                  <span className="text-gray-900">{formatCurrency(order.total_amount, order.currency_code)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Fulfillments */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Fulfillments</h3>
              <button
                onClick={() => setShowCreateFulfillment(true)}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Create Fulfillment
              </button>
            </div>
            <div className="p-6">
              {order.fulfillments && order.fulfillments.length > 0 ? (
                <div className="space-y-4">
                  {order.fulfillments.map((fulfillment) => (
                    <div key={fulfillment.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <TruckIcon className="h-5 w-5 text-gray-400" />
                          <span className="font-medium text-gray-900">
                            Fulfillment #{fulfillment.id.slice(-6)}
                          </span>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            fulfillment.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                            fulfillment.status === 'delivered' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {fulfillment.status}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(fulfillment.created_at).toLocaleDateString()}
                        </div>
                      </div>
                      {fulfillment.tracking_number && (
                        <div className="text-sm text-gray-600">
                          Tracking: {fulfillment.tracking_url ? (
                            <a href={fulfillment.tracking_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              {fulfillment.tracking_number}
                            </a>
                          ) : fulfillment.tracking_number}
                        </div>
                      )}
                      {fulfillment.carrier && (
                        <div className="text-sm text-gray-600">
                          Carrier: {fulfillment.carrier}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <TruckIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No fulfillments</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Create a fulfillment to start shipping this order.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Notes</h3>
              <button
                onClick={() => {
                  setNotes(order.notes || '');
                  setIsEditingNotes(true);
                }}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                <PencilIcon className="h-4 w-4 inline mr-1" />
                Edit
              </button>
            </div>
            <div className="p-6">
              {isEditingNotes ? (
                <div className="space-y-4">
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Add notes about this order..."
                  />
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => setIsEditingNotes(false)}
                      className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleUpdateNotes}
                      disabled={updateNotesMutation.isPending}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                      {updateNotesMutation.isPending ? 'Saving...' : 'Save Notes'}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-gray-700">
                  {order.notes || 'No notes added'}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Customer</h3>
            </div>
            <div className="p-6">
              {order.customer ? (
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <UserIcon className="h-5 w-5 text-gray-400" />
                    <span className="font-medium text-gray-900">
                      {order.customer.first_name && order.customer.last_name 
                        ? `${order.customer.first_name} ${order.customer.last_name}`
                        : 'Customer'
                      }
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                    <a href={`mailto:${order.customer.email}`} className="text-blue-600 hover:underline">
                      {order.customer.email}
                    </a>
                  </div>
                  {order.customer.phone && (
                    <div className="flex items-center space-x-2">
                      <span className="h-5 w-5 text-gray-400">📞</span>
                      <a href={`tel:${order.customer.phone}`} className="text-blue-600 hover:underline">
                        {order.customer.phone}
                      </a>
                    </div>
                  )}
                  <div className="pt-2">
                    <Link
                      to={`/customers/${order.customer.id}`}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      View customer details →
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="text-gray-500">
                  <div className="text-sm">Guest customer</div>
                  <div className="flex items-center space-x-2 mt-2">
                    <EnvelopeIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">{order.email}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Shipping Address */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Shipping Address</h3>
            </div>
            <div className="p-6">
              <div className="flex items-start space-x-2">
                <MapPinIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-gray-700 whitespace-pre-line">
                  {formatAddress(order.shipping_address)}
                </div>
              </div>
            </div>
          </div>

          {/* Billing Address */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Billing Address</h3>
            </div>
            <div className="p-6">
              <div className="flex items-start space-x-2">
                <MapPinIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-gray-700 whitespace-pre-line">
                  {formatAddress(order.billing_address)}
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Payment</h3>
            </div>
            <div className="p-6">
              {order.payments && order.payments.length > 0 ? (
                <div className="space-y-3">
                  {order.payments.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {payment.provider}
                        </div>
                        <div className="text-xs text-gray-500">
                          {payment.type} • {payment.status}
                        </div>
                        {payment.processed_at && (
                          <div className="text-xs text-gray-500">
                            {new Date(payment.processed_at).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(payment.amount, payment.currency_code)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <CurrencyDollarIcon className="mx-auto h-8 w-8 text-gray-400" />
                  <div className="mt-2 text-sm">No payments recorded</div>
                </div>
              )}
            </div>
          </div>

          {/* Order Timeline */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Timeline</h3>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Order created</div>
                    <div className="text-xs text-gray-500">
                      {new Date(order.created_at).toLocaleString()}
                    </div>
                  </div>
                </div>
                {order.cancelled_at && (
                  <div className="flex items-center space-x-3">
                    <XCircleIcon className="h-5 w-5 text-red-500" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">Order cancelled</div>
                      <div className="text-xs text-gray-500">
                        {new Date(order.cancelled_at).toLocaleString()}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetail; 