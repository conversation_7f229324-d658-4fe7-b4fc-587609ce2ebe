import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api, endpoints } from '../lib/api';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  MagnifyingGlassIcon,
  GiftIcon,
  CreditCardIcon,
  PlayIcon,
  PauseIcon,
  ClockIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

interface GiftCard {
  id: string;
  code: string;
  value: number;
  balance: number;
  region_id?: string;
  order_id?: string;
  is_disabled: boolean;
  ends_at?: string;
  tax_inclusive: boolean;
  metadata?: any;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  region?: {
    name: string;
    currency_code: string;
  };
  transactions?: GiftCardTransaction[];
}

interface GiftCardTransaction {
  id: string;
  gift_card_id: string;
  order_id?: string;
  amount: number;
  is_taxable: boolean;
  tax_rate?: number;
  created_at: string;
}

interface GiftCardFormData {
  value: number;
  region_id?: string;
  ends_at?: string;
  tax_inclusive: boolean;
  metadata?: any;
}

const GiftCards: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingGiftCard, setEditingGiftCard] = useState<GiftCard | null>(null);
  const [selectedGiftCard, setSelectedGiftCard] = useState<GiftCard | null>(null);
  const [showTransactions, setShowTransactions] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'disabled' | 'expired'>('all');

  const queryClient = useQueryClient();

  // Fetch gift cards
  const { data: giftCardsResponse, isLoading, error } = useQuery({
    queryKey: ['gift-cards'],
    queryFn: async () => {
      const response = await api.get(endpoints.giftCards.list);
      return response.data;
    },
  });

  const giftCards = giftCardsResponse?.data || [];

  // Filter gift cards
  const filteredGiftCards = giftCards.filter((card: GiftCard) => {
    const matchesSearch = card.code.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (statusFilter === 'active') {
      return matchesSearch && !card.is_disabled && (!card.ends_at || new Date(card.ends_at) > new Date());
    } else if (statusFilter === 'disabled') {
      return matchesSearch && card.is_disabled;
    } else if (statusFilter === 'expired') {
      return matchesSearch && card.ends_at && new Date(card.ends_at) <= new Date();
    }
    
    return matchesSearch;
  });

  // Create gift card mutation
  const createGiftCardMutation = useMutation({
    mutationFn: async (cardData: GiftCardFormData) => {
      const response = await api.post(endpoints.giftCards.create, cardData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gift-cards'] });
      setShowForm(false);
      setEditingGiftCard(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create gift card');
    },
  });

  // Update gift card mutation
  const updateGiftCardMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<GiftCardFormData> }) => {
      const response = await api.put(endpoints.giftCards.update(id), data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gift-cards'] });
      setShowForm(false);
      setEditingGiftCard(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update gift card');
    },
  });

  // Delete gift card mutation
  const deleteGiftCardMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await api.delete(endpoints.giftCards.delete(id));
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gift-cards'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete gift card');
    },
  });

  // Toggle gift card status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: async ({ id, action }: { id: string; action: 'activate' | 'deactivate' }) => {
      const endpoint = action === 'activate' 
        ? endpoints.giftCards.activate(id) 
        : endpoints.giftCards.deactivate(id);
      const response = await api.post(endpoint);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gift-cards'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update gift card status');
    },
  });

  const getGiftCardStatus = (card: GiftCard) => {
    if (card.is_disabled) return 'disabled';
    if (card.ends_at && new Date(card.ends_at) <= new Date()) return 'expired';
    if (card.balance <= 0) return 'depleted';
    return 'active';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'disabled': return 'bg-red-100 text-red-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      case 'depleted': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleEdit = (card: GiftCard) => {
    setEditingGiftCard(card);
    setShowForm(true);
  };

  const handleDelete = (card: GiftCard) => {
    if (window.confirm(`Are you sure you want to delete gift card "${card.code}"? This action cannot be undone.`)) {
      deleteGiftCardMutation.mutate(card.id);
    }
  };

  const handleToggleStatus = (card: GiftCard) => {
    const action = card.is_disabled ? 'activate' : 'deactivate';
    toggleStatusMutation.mutate({ id: card.id, action });
  };

  const handleViewTransactions = (card: GiftCard) => {
    setSelectedGiftCard(card);
    setShowTransactions(true);
  };

  const GiftCardForm: React.FC = () => {
    const [formData, setFormData] = useState<GiftCardFormData>({
      value: editingGiftCard?.value || 0,
      region_id: editingGiftCard?.region_id || '',
      ends_at: editingGiftCard?.ends_at ? editingGiftCard.ends_at.split('T')[0] : '',
      tax_inclusive: editingGiftCard?.tax_inclusive ?? false,
      metadata: editingGiftCard?.metadata || {},
    });

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      
      const submitData = {
        ...formData,
        region_id: formData.region_id || undefined,
        ends_at: formData.ends_at ? `${formData.ends_at}T23:59:59.999Z` : undefined,
      };
      
      if (editingGiftCard) {
        updateGiftCardMutation.mutate({ id: editingGiftCard.id, data: submitData });
      } else {
        createGiftCardMutation.mutate(submitData);
      }
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-md">
          <h3 className="text-lg font-medium mb-4">
            {editingGiftCard ? 'Edit Gift Card' : 'Create New Gift Card'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Value *
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.value}
                  onChange={(e) => setFormData(prev => ({ ...prev, value: parseFloat(e.target.value) || 0 }))}
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Expiration Date (Optional)
              </label>
              <input
                type="date"
                value={formData.ends_at}
                onChange={(e) => setFormData(prev => ({ ...prev, ends_at: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={formData.tax_inclusive}
                onChange={(e) => setFormData(prev => ({ ...prev, tax_inclusive: e.target.checked }))}
                className="mr-2"
              />
              <label className="text-sm text-gray-700">
                Tax Inclusive
              </label>
            </div>
            
            <div className="flex justify-end space-x-2 pt-4">
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setEditingGiftCard(null);
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createGiftCardMutation.isPending || updateGiftCardMutation.isPending}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {editingGiftCard ? 'Update' : 'Create'}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const TransactionsModal: React.FC = () => {
    if (!selectedGiftCard) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">
              Gift Card Transactions - {selectedGiftCard.code}
            </h3>
            <button
              onClick={() => setShowTransactions(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          
          <div className="mb-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Current Balance:</span>
                <span className="ml-2 font-medium">${selectedGiftCard.balance.toFixed(2)}</span>
              </div>
              <div>
                <span className="text-gray-500">Original Value:</span>
                <span className="ml-2 font-medium">${selectedGiftCard.value.toFixed(2)}</span>
              </div>
            </div>
          </div>
          
          {selectedGiftCard.transactions && selectedGiftCard.transactions.length > 0 ? (
            <div className="space-y-2">
              {selectedGiftCard.transactions.map((transaction) => (
                <div key={transaction.id} className="p-3 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-sm font-medium">
                        ${Math.abs(transaction.amount).toFixed(2)}
                      </span>
                      <span className={`ml-2 text-xs px-2 py-1 rounded-full ${
                        transaction.amount > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {transaction.amount > 0 ? 'Credit' : 'Debit'}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(transaction.created_at).toLocaleString()}
                    </div>
                  </div>
                  {transaction.order_id && (
                    <div className="mt-1 text-xs text-gray-500">
                      Order: {transaction.order_id}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No transactions found
            </div>
          )}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-64 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-96 mb-8"></div>
          <div className="bg-white shadow rounded-lg p-6">
            <div className="space-y-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-100 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800">Error loading gift cards. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Gift Cards
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage gift cards for your store. Track balances, set expiration dates, and monitor usage.
            </p>
          </div>
          <button
            onClick={() => {
              setEditingGiftCard(null);
              setShowForm(true);
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Create Gift Card</span>
          </button>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search gift cards..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="disabled">Disabled</option>
                <option value="expired">Expired</option>
              </select>
            </div>
            <div className="text-sm text-gray-500">
              {filteredGiftCards.length} gift card{filteredGiftCards.length !== 1 ? 's' : ''}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {filteredGiftCards.length === 0 ? (
            <div className="text-center py-12">
              <GiftIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {searchTerm ? 'No gift cards found' : 'No gift cards'}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm 
                  ? 'Try adjusting your search or filters to find what you\'re looking for.'
                  : 'Get started by creating your first gift card.'
                }
              </p>
              {!searchTerm && (
                <div className="mt-6">
                  <button
                    onClick={() => {
                      setEditingGiftCard(null);
                      setShowForm(true);
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 mx-auto"
                  >
                    <PlusIcon className="h-5 w-5" />
                    <span>Create Gift Card</span>
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredGiftCards.map((card: GiftCard) => {
                const status = getGiftCardStatus(card);
                const statusColor = getStatusColor(status);
                
                return (
                  <div key={card.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <CreditCardIcon className="h-6 w-6 text-purple-600" />
                        </div>
                        
                        <div>
                          <div className="flex items-center space-x-2">
                            <h3 className="text-sm font-medium text-gray-900 font-mono">
                              {card.code}
                            </h3>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColor}`}>
                              {status.charAt(0).toUpperCase() + status.slice(1)}
                            </span>
                          </div>
                          
                          <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                            <span>Balance: ${card.balance.toFixed(2)}</span>
                            <span>•</span>
                            <span>Value: ${card.value.toFixed(2)}</span>
                            {card.ends_at && (
                              <>
                                <span>•</span>
                                <span className="flex items-center space-x-1">
                                  <ClockIcon className="h-4 w-4" />
                                  <span>Expires: {new Date(card.ends_at).toLocaleDateString()}</span>
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleViewTransactions(card)}
                          className="p-2 text-gray-400 hover:text-blue-600"
                          title="View Transactions"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        
                        <button
                          onClick={() => handleToggleStatus(card)}
                          className={`p-2 hover:text-white hover:bg-gray-600 rounded ${
                            card.is_disabled ? 'text-green-600' : 'text-red-600'
                          }`}
                          title={card.is_disabled ? 'Activate' : 'Deactivate'}
                        >
                          {card.is_disabled ? (
                            <PlayIcon className="h-4 w-4" />
                          ) : (
                            <PauseIcon className="h-4 w-4" />
                          )}
                        </button>
                        
                        <button
                          onClick={() => handleEdit(card)}
                          className="p-2 text-gray-400 hover:text-blue-600"
                          title="Edit"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        
                        <button
                          onClick={() => handleDelete(card)}
                          className="p-2 text-gray-400 hover:text-red-600"
                          title="Delete"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {showForm && <GiftCardForm />}
      {showTransactions && <TransactionsModal />}
    </div>
  );
};

export default GiftCards; 