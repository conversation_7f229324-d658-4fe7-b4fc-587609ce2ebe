// Local type definitions to avoid dependency issues
interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

import { DatabaseService } from './database';

// Stripe types (simplified)
interface StripePaymentIntent {
  id: string;
  client_secret: string;
  status: string;
  amount: number;
  currency: string;
  metadata: Record<string, any>;
}

interface StripeEvent {
  type: string;
  data: {
    object: any;
  };
}

// Mock Stripe class for now
class MockStripe {
  constructor(secretKey: string, options: any) {}
  
  paymentIntents = {
    create: async (params: any): Promise<StripePaymentIntent> => {
      return {
        id: `pi_${Date.now()}`,
        client_secret: `pi_${Date.now()}_secret`,
        status: 'requires_payment_method',
        amount: params.amount,
        currency: params.currency,
        metadata: params.metadata || {},
      };
    },
    retrieve: async (id: string): Promise<StripePaymentIntent> => {
      return {
        id,
        client_secret: `${id}_secret`,
        status: 'succeeded',
        amount: 1000,
        currency: 'ron',
        metadata: {},
      };
    },
    capture: async (id: string): Promise<StripePaymentIntent> => {
      return {
        id,
        client_secret: `${id}_secret`,
        status: 'succeeded',
        amount: 1000,
        currency: 'ron',
        metadata: {},
      };
    },
    cancel: async (id: string): Promise<StripePaymentIntent> => {
      return {
        id,
        client_secret: `${id}_secret`,
        status: 'canceled',
        amount: 1000,
        currency: 'ron',
        metadata: {},
      };
    },
  };
}

export interface PaymentSession {
  id: string;
  cart_id: string;
  provider_id: string;
  amount: number;
  currency_code: string;
  status: 'pending' | 'authorized' | 'captured' | 'canceled' | 'failed';
  data: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface PaymentProvider {
  id: string;
  name: string;
  enabled: boolean;
}

export class PaymentService {
  private db: DatabaseService;
  private stripe?: MockStripe;
  private env: WorkerEnv;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.db = new DatabaseService(env);
    
    if (env.STRIPE_SECRET_KEY) {
      this.stripe = new MockStripe(env.STRIPE_SECRET_KEY, {
        apiVersion: '2024-06-20',
      });
    }
  }

  /**
   * Get available payment providers
   */
  async getProviders(): Promise<PaymentProvider[]> {
    const providers: PaymentProvider[] = [
      {
        id: 'cash_on_delivery',
        name: 'Cash on Delivery',
        enabled: true,
      },
    ];

    if (this.stripe) {
      providers.push({
        id: 'stripe',
        name: 'Credit Card',
        enabled: true,
      });
    }

    return providers;
  }

  /**
   * Create a payment session
   */
  async createPaymentSession(data: {
    cart_id: string;
    provider_id: string;
    amount: number;
    currency_code: string;
    customer_email?: string;
    metadata?: Record<string, any>;
  }): Promise<PaymentSession> {
    const sessionId = `ps_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    let providerData: Record<string, any> = {};
    let status: PaymentSession['status'] = 'pending';

    if (data.provider_id === 'cash_on_delivery') {
      // For cash on delivery, we immediately authorize
      providerData = {
        type: 'cash_on_delivery',
        status: 'authorized',
      };
      status = 'authorized';
    } else if (data.provider_id === 'stripe' && this.stripe) {
      // Create Stripe payment intent
      try {
        const paymentIntent = await this.stripe.paymentIntents.create({
          amount: data.amount,
          currency: data.currency_code,
          metadata: {
            cart_id: data.cart_id,
            session_id: sessionId,
            ...data.metadata,
          },
          automatic_payment_methods: {
            enabled: true,
          },
        });

        providerData = {
          payment_intent_id: paymentIntent.id,
          client_secret: paymentIntent.client_secret,
          status: paymentIntent.status,
        };
      } catch (error) {
        console.error('Error creating Stripe payment intent:', error);
        throw new Error('Failed to create payment session');
      }
    } else {
      throw new Error(`Unsupported payment provider: ${data.provider_id}`);
    }

    const session: PaymentSession = {
      id: sessionId,
      cart_id: data.cart_id,
      provider_id: data.provider_id,
      amount: data.amount,
      currency_code: data.currency_code,
      status,
      data: providerData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Store in database
    await this.db.create('paymentSessions', {
      id: session.id,
      cart_id: session.cart_id,
      provider_id: session.provider_id,
      amount: session.amount,
      status: session.status,
      data: JSON.stringify(session.data),
      created_at: session.created_at,
      updated_at: session.updated_at,
    });

    return session;
  }

  /**
   * Get payment session by ID
   */
  async getPaymentSession(sessionId: string): Promise<PaymentSession | null> {
    const session = await this.db.findById('paymentSessions', sessionId);
    if (!session) return null;

    return {
      id: session.id,
      cart_id: session.cart_id,
      provider_id: session.provider_id,
      amount: session.amount,
      currency_code: session.currency_code || 'RON',
      status: session.status,
      data: typeof session.data === 'string' ? JSON.parse(session.data) : session.data,
      created_at: session.created_at,
      updated_at: session.updated_at,
    };
  }

  /**
   * Authorize a payment session
   */
  async authorizePaymentSession(sessionId: string): Promise<PaymentSession> {
    const session = await this.getPaymentSession(sessionId);
    if (!session) {
      throw new Error('Payment session not found');
    }

    if (session.status === 'authorized') {
      return session;
    }

    if (session.provider_id === 'cash_on_delivery') {
      // Cash on delivery is automatically authorized
      session.status = 'authorized';
      session.data.status = 'authorized';
    } else if (session.provider_id === 'stripe' && this.stripe) {
      // For Stripe, check the payment intent status
      const paymentIntentId = session.data.payment_intent_id;
      if (paymentIntentId) {
        const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
        if (paymentIntent.status === 'succeeded') {
          session.status = 'captured';
          session.data.status = 'captured';
        } else if (paymentIntent.status === 'requires_capture') {
          session.status = 'authorized';
          session.data.status = 'authorized';
        }
      }
    }

    // Update in database
    await this.db.update('paymentSessions', sessionId, {
      status: session.status,
      data: JSON.stringify(session.data),
      updated_at: new Date().toISOString(),
    });

    return session;
  }

  /**
   * Capture a payment session
   */
  async capturePaymentSession(sessionId: string): Promise<PaymentSession> {
    const session = await this.getPaymentSession(sessionId);
    if (!session) {
      throw new Error('Payment session not found');
    }

    if (session.status === 'captured') {
      return session;
    }

    if (session.provider_id === 'cash_on_delivery') {
      // Cash on delivery is captured when order is delivered
      session.status = 'captured';
      session.data.status = 'captured';
    } else if (session.provider_id === 'stripe' && this.stripe) {
      // Capture Stripe payment intent
      const paymentIntentId = session.data.payment_intent_id;
      if (paymentIntentId) {
        await this.stripe.paymentIntents.capture(paymentIntentId);
        session.status = 'captured';
        session.data.status = 'captured';
      }
    }

    // Update in database
    await this.db.update('paymentSessions', sessionId, {
      status: session.status,
      data: JSON.stringify(session.data),
      updated_at: new Date().toISOString(),
    });

    return session;
  }

  /**
   * Cancel a payment session
   */
  async cancelPaymentSession(sessionId: string): Promise<PaymentSession> {
    const session = await this.getPaymentSession(sessionId);
    if (!session) {
      throw new Error('Payment session not found');
    }

    if (session.provider_id === 'stripe' && this.stripe) {
      // Cancel Stripe payment intent
      const paymentIntentId = session.data.payment_intent_id;
      if (paymentIntentId) {
        await this.stripe.paymentIntents.cancel(paymentIntentId);
      }
    }

    session.status = 'canceled';
    session.data.status = 'canceled';

    // Update in database
    await this.db.update('paymentSessions', sessionId, {
      status: session.status,
      data: JSON.stringify(session.data),
      updated_at: new Date().toISOString(),
    });

    return session;
  }

  /**
   * Handle Stripe webhook
   */
  async handleStripeWebhook(event: StripeEvent): Promise<void> {
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as StripePaymentIntent;
        const sessionId = paymentIntent.metadata.session_id;
        if (sessionId) {
          await this.authorizePaymentSession(sessionId);
        }
        break;
      
      case 'payment_intent.payment_failed':
        const failedIntent = event.data.object as StripePaymentIntent;
        const failedSessionId = failedIntent.metadata.session_id;
        if (failedSessionId) {
          const session = await this.getPaymentSession(failedSessionId);
          if (session) {
            session.status = 'failed';
            await this.db.update('paymentSessions', failedSessionId, {
              status: 'failed',
              updated_at: new Date().toISOString(),
            });
          }
        }
        break;
    }
  }
} 