import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { api, endpoints } from '../lib/api';

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Check for existing auth on mount
  useEffect(() => {
    const checkAuth = async () => {
      console.log('🔍 AuthContext: Starting auth check...');
      try {
        const token = localStorage.getItem('auth_token');
        console.log('🔍 AuthContext: Token found:', !!token);
        
        if (token) {
          // Set the token in API headers
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          
          console.log('🔍 AuthContext: Calling /me endpoint...');
          // Verify the token and get user info
          const response = await api.get(endpoints.auth.me);
          console.log('🔍 AuthContext: /me response:', response.data);
          
          // Backend returns: { user: {...} }
          setUser(response.data.user);
          console.log('🔍 AuthContext: User set successfully');
        }
      } catch (error) {
        console.log('🔍 AuthContext: Auth check failed:', error);
        // Token is invalid, remove it
        localStorage.removeItem('auth_token');
        delete api.defaults.headers.common['Authorization'];
      } finally {
        console.log('🔍 AuthContext: Setting loading to false');
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await api.post(endpoints.auth.login, {
        email,
        password,
      });

      // Backend returns: { token: "...", user: {...} }
      const { token, user: userData } = response.data;
      
      // Store token
      localStorage.setItem('auth_token', token);
      
      // Set authorization header
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Set user
      setUser(userData);
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    // Clear user state
    setUser(null);
    
    // Remove token from storage
    localStorage.removeItem('auth_token');
    
    // Remove authorization header
    delete api.defaults.headers.common['Authorization'];
    
    // Optional: Call logout endpoint
    api.post(endpoints.auth.logout).catch(() => {
      // Ignore errors on logout
    });
  };

  const refreshToken = async () => {
    try {
      const response = await api.post(endpoints.auth.refresh);
      const { token } = response.data;
      
      localStorage.setItem('auth_token', token);
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } catch (error) {
      // If refresh fails, logout user
      logout();
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Explicit exports
export { AuthContext };
export default AuthProvider; 