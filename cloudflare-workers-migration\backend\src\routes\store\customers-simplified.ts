import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { Context } from 'hono';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Utility functions
function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error(error);
  return c.json({ success: false, error: message, details: error.message }, 500);
}

function safeParseJson(value: any): any {
  if (!value) return null;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }
  return value;
}

// Helper function to enrich customer with related data
async function enrichCustomer(customer: any, db: D1Database): Promise<any> {
  const enriched = { ...customer };

  // Parse metadata
  enriched.metadata = safeParseJson(enriched.metadata) || {};

  // Get customer addresses
  const addressesQuery = `
    SELECT * FROM customer_addresses 
    WHERE customer_id = ? AND deleted_at IS NULL
    ORDER BY is_default DESC, created_at ASC
  `;
  const addresses = await db.prepare(addressesQuery).bind(customer.id).all();
  enriched.addresses = addresses.results || [];

  // Get customer group if exists
  if (customer.group_id) {
    const groupQuery = `SELECT * FROM customer_groups WHERE id = ?`;
    const group = await db.prepare(groupQuery).bind(customer.group_id).first();
    if (group) {
      enriched.group = {
        ...group,
        metadata: safeParseJson(group.metadata) || {}
      };
    }
  }

  // Get order statistics
  const orderStatsQuery = `
    SELECT 
      COUNT(*) as order_count,
      COALESCE(SUM(total), 0) as total_spent,
      MAX(created_at) as last_order_date
    FROM orders 
    WHERE customer_id = ? AND deleted_at IS NULL
  `;
  const orderStats = await db.prepare(orderStatsQuery).bind(customer.id).first();
  if (orderStats) {
    enriched.order_count = orderStats.order_count || 0;
    enriched.total_spent = orderStats.total_spent || 0;
    enriched.last_order_date = orderStats.last_order_date;
  }

  return enriched;
}

// POST /store/customers - Create a new customer
app.post('/', async (c) => {
  try {
    const body = await c.req.json();
    const { 
      email, 
      first_name, 
      last_name, 
      phone, 
      password_hash,
      has_account = false,
      metadata 
    } = body;

    if (!email) {
      return c.json({ success: false, error: 'Email is required' }, 400);
    }

    // Check if customer already exists
    const existingCustomer = await c.env.DB.prepare(`
      SELECT id FROM customers WHERE email = ? AND deleted_at IS NULL
    `).bind(email).first();

    if (existingCustomer) {
      return c.json({ success: false, error: 'Customer with this email already exists' }, 400);
    }

    // Create customer
    const customerId = crypto.randomUUID();
    const now = new Date().toISOString();
    
    const createQuery = `
      INSERT INTO customers (
        id, email, first_name, last_name, phone, password_hash, 
        has_account, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await c.env.DB.prepare(createQuery).bind(
      customerId,
      email,
      first_name || null,
      last_name || null,
      phone || null,
      password_hash || null,
      has_account ? 1 : 0,
      metadata ? JSON.stringify(metadata) : null,
      now,
      now
    ).run();

    // Get created customer
    const customer = await c.env.DB.prepare(`SELECT * FROM customers WHERE id = ?`).bind(customerId).first();
    
    // Enrich customer with related data
    const enrichedCustomer = await enrichCustomer(customer, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCustomer,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create customer');
  }
});

// GET /store/customers/:id - Get a specific customer
app.get('/:id', async (c) => {
  try {
    const customerId = c.req.param('id');

    // Get customer from database
    const customer = await c.env.DB.prepare(`SELECT * FROM customers WHERE id = ? AND deleted_at IS NULL`).bind(customerId).first();
    
    if (!customer) {
      return c.json({
        success: false,
        error: 'Customer not found',
      }, 404);
    }

    const enrichedCustomer = await enrichCustomer(customer, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCustomer,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch customer');
  }
});

// GET /store/customers/by-email/:email - Get customer by email
app.get('/by-email/:email', async (c) => {
  try {
    const email = c.req.param('email');

    // Get customer from database
    const customer = await c.env.DB.prepare(`SELECT * FROM customers WHERE email = ? AND deleted_at IS NULL`).bind(email).first();
    
    if (!customer) {
      return c.json({
        success: false,
        error: 'Customer not found',
      }, 404);
    }

    const enrichedCustomer = await enrichCustomer(customer, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCustomer,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch customer');
  }
});

// POST /store/customers/:id - Update customer
app.post('/:id', async (c) => {
  try {
    const customerId = c.req.param('id');
    const body = await c.req.json();
    const { 
      first_name, 
      last_name, 
      phone, 
      metadata 
    } = body;

    // Verify customer exists
    const customer = await c.env.DB.prepare(`SELECT * FROM customers WHERE id = ? AND deleted_at IS NULL`).bind(customerId).first();
    if (!customer) {
      return c.json({ success: false, error: 'Customer not found' }, 404);
    }

    const now = new Date().toISOString();
    const updateData: any = { updated_at: now };

    // Update fields
    if (first_name !== undefined) updateData.first_name = first_name;
    if (last_name !== undefined) updateData.last_name = last_name;
    if (phone !== undefined) updateData.phone = phone;
    if (metadata !== undefined) updateData.metadata = JSON.stringify(metadata);

    // Update customer
    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const updateValues = Object.values(updateData);
    
    const updateQuery = `UPDATE customers SET ${updateFields} WHERE id = ?`;
    await c.env.DB.prepare(updateQuery).bind(...updateValues, customerId).run();

    // Get updated customer
    const updatedCustomer = await c.env.DB.prepare(`SELECT * FROM customers WHERE id = ?`).bind(customerId).first();
    const enrichedCustomer = await enrichCustomer(updatedCustomer, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCustomer,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update customer');
  }
});

// POST /store/customers/:id/addresses - Add address to customer
app.post('/:id/addresses', async (c) => {
  try {
    const customerId = c.req.param('id');
    const body = await c.req.json();
    const {
      first_name,
      last_name,
      address_1,
      address_2,
      city,
      country_code,
      province,
      postal_code,
      phone,
      company,
      is_default = false,
      metadata
    } = body;

    if (!address_1 || !city || !country_code) {
      return c.json({ success: false, error: 'Address line 1, city, and country code are required' }, 400);
    }

    // Verify customer exists
    const customer = await c.env.DB.prepare(`SELECT * FROM customers WHERE id = ? AND deleted_at IS NULL`).bind(customerId).first();
    if (!customer) {
      return c.json({ success: false, error: 'Customer not found' }, 404);
    }

    const now = new Date().toISOString();
    const addressId = crypto.randomUUID();

    // If this is set as default, unset other default addresses
    if (is_default) {
      await c.env.DB.prepare(`
        UPDATE customer_addresses SET is_default = 0, updated_at = ?
        WHERE customer_id = ? AND deleted_at IS NULL
      `).bind(now, customerId).run();
    }

    // Create address
    const createQuery = `
      INSERT INTO customer_addresses (
        id, customer_id, first_name, last_name, address_1, address_2,
        city, country_code, province, postal_code, phone, company,
        is_default, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await c.env.DB.prepare(createQuery).bind(
      addressId,
      customerId,
      first_name || null,
      last_name || null,
      address_1,
      address_2 || null,
      city,
      country_code,
      province || null,
      postal_code || null,
      phone || null,
      company || null,
      is_default ? 1 : 0,
      metadata ? JSON.stringify(metadata) : null,
      now,
      now
    ).run();

    // Get created address
    const address = await c.env.DB.prepare(`SELECT * FROM customer_addresses WHERE id = ?`).bind(addressId).first();

    return c.json({
      success: true,
      data: address,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create address');
  }
});

// POST /store/customers/:id/addresses/:address_id - Update customer address
app.post('/:id/addresses/:address_id', async (c) => {
  try {
    const customerId = c.req.param('id');
    const addressId = c.req.param('address_id');
    const body = await c.req.json();
    const {
      first_name,
      last_name,
      address_1,
      address_2,
      city,
      country_code,
      province,
      postal_code,
      phone,
      company,
      is_default,
      metadata
    } = body;

    // Verify customer and address exist
    const address = await c.env.DB.prepare(`
      SELECT * FROM customer_addresses
      WHERE id = ? AND customer_id = ? AND deleted_at IS NULL
    `).bind(addressId, customerId).first();

    if (!address) {
      return c.json({ success: false, error: 'Address not found' }, 404);
    }

    const now = new Date().toISOString();
    const updateData: any = { updated_at: now };

    // Update fields
    if (first_name !== undefined) updateData.first_name = first_name;
    if (last_name !== undefined) updateData.last_name = last_name;
    if (address_1 !== undefined) updateData.address_1 = address_1;
    if (address_2 !== undefined) updateData.address_2 = address_2;
    if (city !== undefined) updateData.city = city;
    if (country_code !== undefined) updateData.country_code = country_code;
    if (province !== undefined) updateData.province = province;
    if (postal_code !== undefined) updateData.postal_code = postal_code;
    if (phone !== undefined) updateData.phone = phone;
    if (company !== undefined) updateData.company = company;
    if (metadata !== undefined) updateData.metadata = JSON.stringify(metadata);

    // Handle default address
    if (is_default !== undefined) {
      updateData.is_default = is_default ? 1 : 0;

      if (is_default) {
        // Unset other default addresses
        await c.env.DB.prepare(`
          UPDATE customer_addresses SET is_default = 0, updated_at = ?
          WHERE customer_id = ? AND id != ? AND deleted_at IS NULL
        `).bind(now, customerId, addressId).run();
      }
    }

    // Update address
    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const updateValues = Object.values(updateData);

    const updateQuery = `UPDATE customer_addresses SET ${updateFields} WHERE id = ?`;
    await c.env.DB.prepare(updateQuery).bind(...updateValues, addressId).run();

    // Get updated address
    const updatedAddress = await c.env.DB.prepare(`SELECT * FROM customer_addresses WHERE id = ?`).bind(addressId).first();

    return c.json({
      success: true,
      data: updatedAddress,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update address');
  }
});

// DELETE /store/customers/:id/addresses/:address_id - Delete customer address
app.delete('/:id/addresses/:address_id', async (c) => {
  try {
    const customerId = c.req.param('id');
    const addressId = c.req.param('address_id');

    // Verify customer and address exist
    const address = await c.env.DB.prepare(`
      SELECT * FROM customer_addresses
      WHERE id = ? AND customer_id = ? AND deleted_at IS NULL
    `).bind(addressId, customerId).first();

    if (!address) {
      return c.json({ success: false, error: 'Address not found' }, 404);
    }

    const now = new Date().toISOString();

    // Soft delete the address
    await c.env.DB.prepare(`
      UPDATE customer_addresses SET deleted_at = ? WHERE id = ?
    `).bind(now, addressId).run();

    return c.json({
      success: true,
      message: 'Address deleted successfully',
    });

  } catch (error) {
    return handleError(c, error, 'Failed to delete address');
  }
});

export const customersSimplifiedRoutes = app;
