import { Hono } from 'hono';
import { WorkerEnv } from 'handmadein-shared';
import { NewsletterService } from '../services/newsletter';
import { z } from 'zod';

const newsletter = new Hono<{ Bindings: WorkerEnv }>();

// Validation schemas
const subscribeSchema = z.object({
  email: z.string().email('Invalid email address'),
  metadata: z.record(z.any()).optional()
});

const unsubscribeSchema = z.object({
  email: z.string().email('Invalid email address')
});

/**
 * Subscribe to newsletter
 * POST /newsletter/subscribe
 */
newsletter.post('/subscribe', async (c) => {
  try {
    const body = await c.req.json();
    const { email, metadata } = subscribeSchema.parse(body);
    
    const newsletterService = new NewsletterService(c.env);
    const result = await newsletterService.subscribe(email, metadata);
    
    if (result.success) {
      return c.json({
        success: true,
        message: result.message
      });
    } else {
      return c.json({
        success: false,
        error: result.message
      }, result.message.includes('already subscribed') ? 409 : 400);
    }
  } catch (error) {
    console.error('Newsletter subscribe error:', error);
    
    if (error instanceof z.ZodError) {
      return c.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      }, 400);
    }
    
    return c.json({
      success: false,
      error: 'Failed to subscribe to newsletter'
    }, 500);
  }
});

/**
 * Unsubscribe from newsletter
 * POST /newsletter/unsubscribe
 */
newsletter.post('/unsubscribe', async (c) => {
  try {
    const body = await c.req.json();
    const { email } = unsubscribeSchema.parse(body);
    
    const newsletterService = new NewsletterService(c.env);
    const result = await newsletterService.unsubscribe(email);
    
    if (result.success) {
      return c.json({
        success: true,
        message: result.message
      });
    } else {
      return c.json({
        success: false,
        error: result.message
      }, 400);
    }
  } catch (error) {
    console.error('Newsletter unsubscribe error:', error);
    
    if (error instanceof z.ZodError) {
      return c.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      }, 400);
    }
    
    return c.json({
      success: false,
      error: 'Failed to unsubscribe from newsletter'
    }, 500);
  }
});

/**
 * Unsubscribe via GET link (for email links)
 * GET /newsletter/unsubscribe?email=...
 */
newsletter.get('/unsubscribe', async (c) => {
  try {
    const email = c.req.query('email');
    
    if (!email) {
      return c.html(`
        <html>
          <head><title>Unsubscribe - HandmadeIn.ro</title></head>
          <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px;">
            <h1>Invalid Unsubscribe Link</h1>
            <p>The unsubscribe link is invalid or missing email parameter.</p>
            <a href="${c.env.FRONTEND_URL}">Return to HandmadeIn.ro</a>
          </body>
        </html>
      `, 400);
    }
    
    const newsletterService = new NewsletterService(c.env);
    const result = await newsletterService.unsubscribe(email);
    
    if (result.success) {
      return c.html(`
        <html>
          <head><title>Unsubscribed - HandmadeIn.ro</title></head>
          <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center;">
            <h1 style="color: #2c5aa0;">Successfully Unsubscribed</h1>
            <p>You have been successfully unsubscribed from the HandmadeIn.ro newsletter.</p>
            <p>We're sorry to see you go! If you change your mind, you can always subscribe again on our website.</p>
            <div style="margin: 30px 0;">
              <a href="${c.env.FRONTEND_URL}" style="background: #2c5aa0; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Visit HandmadeIn.ro
              </a>
            </div>
          </body>
        </html>
      `);
    } else {
      return c.html(`
        <html>
          <head><title>Unsubscribe Error - HandmadeIn.ro</title></head>
          <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px;">
            <h1>Unsubscribe Error</h1>
            <p>${result.message}</p>
            <a href="${c.env.FRONTEND_URL}">Return to HandmadeIn.ro</a>
          </body>
        </html>
      `, 400);
    }
  } catch (error) {
    console.error('Newsletter unsubscribe error:', error);
    
    return c.html(`
      <html>
        <head><title>Unsubscribe Error - HandmadeIn.ro</title></head>
        <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px;">
          <h1>Unsubscribe Error</h1>
          <p>An error occurred while processing your unsubscribe request. Please try again later.</p>
          <a href="${c.env.FRONTEND_URL}">Return to HandmadeIn.ro</a>
        </body>
      </html>
    `, 500);
  }
});

/**
 * Get newsletter statistics (admin only)
 * GET /newsletter/stats
 */
newsletter.get('/stats', async (c) => {
  try {
    // TODO: Add admin authentication middleware
    
    const newsletterService = new NewsletterService(c.env);
    const stats = await newsletterService.getSubscriberStats();
    
    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Newsletter stats error:', error);
    
    return c.json({
      success: false,
      error: 'Failed to get newsletter statistics'
    }, 500);
  }
});

/**
 * Get all subscribers (admin only)
 * GET /newsletter/subscribers
 */
newsletter.get('/subscribers', async (c) => {
  try {
    // TODO: Add admin authentication middleware
    
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '50');
    const status = c.req.query('status') as 'subscribed' | 'unsubscribed' | 'pending' | undefined;
    
    const newsletterService = new NewsletterService(c.env);
    const result = await newsletterService.getAllSubscribers({ page, limit, status });
    
    return c.json({
      success: true,
      data: result.subscribers,
      pagination: {
        page,
        limit,
        total: result.count,
        pages: Math.ceil(result.count / limit)
      }
    });
  } catch (error) {
    console.error('Newsletter subscribers error:', error);
    
    return c.json({
      success: false,
      error: 'Failed to get newsletter subscribers'
    }, 500);
  }
});

export { newsletter }; 