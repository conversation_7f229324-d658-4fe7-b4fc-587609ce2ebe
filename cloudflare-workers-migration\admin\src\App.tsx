import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';

// Layout components
import Layout from './components/Layout';
import Login from './pages/Login';

// Page components
import Dashboard from './pages/Dashboard';
import Products from './pages/Products';
import ProductForm from './pages/ProductForm';
// import ProductDetail from './pages/ProductDetail';
import Orders from './pages/Orders';
import OrderDetail from './pages/OrderDetail';
import Customers from './pages/Customers';
import CustomerDetail from './pages/CustomerDetail';
import CustomerGroups from './pages/CustomerGroups';
import Collections from './pages/Collections';
import CollectionForm from './pages/CollectionForm';
import Categories from './pages/Categories';
import CategoryForm from './pages/CategoryForm';
import ProductTypes from './pages/ProductTypes';
import Tags from './pages/Tags';
import Inventory from './pages/Inventory';
import StockLocations from './pages/StockLocations';
import Reservations from './pages/Reservations';
import InventoryItems from './pages/InventoryItems';
import InventoryLevels from './pages/InventoryLevels';
import InventoryReservations from './pages/InventoryReservations';
import Discounts from './pages/Discounts';
import DiscountForm from './pages/DiscountForm';
import GiftCards from './pages/GiftCards';
import GiftCardForm from './pages/GiftCardForm';
import Campaigns from './pages/Campaigns';
import Analytics from './pages/Analytics';
import AnalyticsSales from './pages/AnalyticsSales';
import AnalyticsProducts from './pages/AnalyticsProducts';
import AnalyticsCustomers from './pages/AnalyticsCustomers';
import AnalyticsReports from './pages/AnalyticsReports';
import Returns from './pages/Returns';
import Exchanges from './pages/Exchanges';
import DraftOrders from './pages/DraftOrders';
import Journal from './pages/Journal';
import Settings from './pages/Settings';
import SettingsStore from './pages/SettingsStore';
import SettingsRegions from './pages/SettingsRegions';
import SettingsCurrencies from './pages/SettingsCurrencies';
import SettingsShipping from './pages/SettingsShipping';
import ShippingPricing from './pages/ShippingPricing';
import SettingsPayments from './pages/SettingsPayments';
import SettingsTaxes from './pages/SettingsTaxes';
import SettingsUsers from './pages/SettingsUsers';
import SettingsApiKeys from './pages/SettingsApiKeys';
import Files from './pages/Files';

// Auth context
import { AuthProvider, useAuth } from './contexts/AuthContext';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Protected Route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  console.log('🔍 ProtectedRoute: isLoading =', isLoading, ', isAuthenticated =', isAuthenticated);

  if (isLoading) {
    console.log('🔍 ProtectedRoute: Showing loading spinner');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    console.log('🔍 ProtectedRoute: Not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  console.log('🔍 ProtectedRoute: Authenticated, rendering children');
  return <Layout>{children}</Layout>;
};

// Public Route component (for login)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  console.log('🔍 PublicRoute: isLoading =', isLoading, ', isAuthenticated =', isAuthenticated);

  if (isLoading) {
    console.log('🔍 PublicRoute: Showing loading spinner');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (isAuthenticated) {
    console.log('🔍 PublicRoute: Authenticated, redirecting to dashboard');
    return <Navigate to="/" replace />;
  }

  console.log('🔍 PublicRoute: Not authenticated, rendering children');
  return <>{children}</>;
};

// Simple test component
const TestPage: React.FC = () => {
  console.log('🔍 TestPage: Rendering test page');
  return (
    <div className="p-8 bg-white rounded-lg shadow-lg">
      <h1 className="text-4xl font-bold text-green-600 mb-4">🎉 TEST PAGE WORKING!</h1>
      <p className="text-lg text-gray-700 mb-4">If you see this, React routing and rendering is working!</p>
      <div className="mt-4 p-4 bg-blue-100 rounded border-l-4 border-blue-500">
        <h2 className="text-xl font-semibold text-blue-800 mb-2">Layout Test</h2>
        <p className="text-blue-700">This means the issue was with missing page components and layout is now fixed.</p>
      </div>
      <div className="mt-4 p-4 bg-green-100 rounded border-l-4 border-green-500">
        <h2 className="text-xl font-semibold text-green-800 mb-2">Next Steps</h2>
        <ul className="list-disc list-inside text-green-700 space-y-1">
          <li>Try navigating to /products to see the products page</li>
          <li>Check the sidebar navigation on the left</li>
          <li>On mobile, tap the hamburger menu (☰) to see the sidebar</li>
          <li>Visit the dashboard at / to see analytics</li>
        </ul>
      </div>
    </div>
  );
};

function App() {
  console.log('🔍 App: Component is rendering!');
  
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <div className="App">
            <Routes>
              {/* Public routes */}
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <Login />
                  </PublicRoute>
                }
              />

              {/* Protected routes */}
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              
              {/* Product Routes */}
              <Route
                path="/products"
                element={
                  <ProtectedRoute>
                    <Products />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/products/new"
                element={
                  <ProtectedRoute>
                    <ProductForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/products/:id"
                element={
                  <ProtectedRoute>
                    <ProductForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/products/:id/edit"
                element={
                  <ProtectedRoute>
                    <ProductForm />
                  </ProtectedRoute>
                }
              />
              
              {/* Collection Routes */}
              <Route
                path="/collections"
                element={
                  <ProtectedRoute>
                    <Collections />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/collections/new"
                element={
                  <ProtectedRoute>
                    <CollectionForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/collections/:id"
                element={
                  <ProtectedRoute>
                    <CollectionForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/collections/:id/edit"
                element={
                  <ProtectedRoute>
                    <CollectionForm />
                  </ProtectedRoute>
                }
              />

              {/* Category Routes */}
              <Route
                path="/categories"
                element={
                  <ProtectedRoute>
                    <Categories />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/categories/new"
                element={
                  <ProtectedRoute>
                    <CategoryForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/categories/:id/edit"
                element={
                  <ProtectedRoute>
                    <CategoryForm />
                  </ProtectedRoute>
                }
              />

              {/* Product Types & Tags */}
              <Route
                path="/product-types"
                element={
                  <ProtectedRoute>
                    <ProductTypes />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/tags"
                element={
                  <ProtectedRoute>
                    <Tags />
                  </ProtectedRoute>
                }
              />
              
              {/* Order Routes */}
              <Route
                path="/orders"
                element={
                  <ProtectedRoute>
                    <Orders />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/orders/drafts"
                element={
                  <ProtectedRoute>
                    <DraftOrders />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/orders/:id"
                element={
                  <ProtectedRoute>
                    <OrderDetail />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/returns"
                element={
                  <ProtectedRoute>
                    <Returns />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/exchanges"
                element={
                  <ProtectedRoute>
                    <Exchanges />
                  </ProtectedRoute>
                }
              />
              
              {/* Customer Routes */}
              <Route
                path="/customers"
                element={
                  <ProtectedRoute>
                    <Customers />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/customers/:id"
                element={
                  <ProtectedRoute>
                    <CustomerDetail />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/customer-groups"
                element={
                  <ProtectedRoute>
                    <CustomerGroups />
                  </ProtectedRoute>
                }
              />

              {/* Inventory Routes */}
              <Route
                path="/inventory"
                element={
                  <ProtectedRoute>
                    <Inventory />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/inventory/items"
                element={
                  <ProtectedRoute>
                    <InventoryItems />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/inventory/levels"
                element={
                  <ProtectedRoute>
                    <InventoryLevels />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/inventory/reservations"
                element={
                  <ProtectedRoute>
                    <InventoryReservations />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/stock-locations"
                element={
                  <ProtectedRoute>
                    <StockLocations />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/inventory/reservations"
                element={
                  <ProtectedRoute>
                    <Reservations />
                  </ProtectedRoute>
                }
              />

              {/* Promotion Routes */}
              <Route
                path="/discounts"
                element={
                  <ProtectedRoute>
                    <Discounts />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/discounts/new"
                element={
                  <ProtectedRoute>
                    <DiscountForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/discounts/:id/edit"
                element={
                  <ProtectedRoute>
                    <DiscountForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/gift-cards"
                element={
                  <ProtectedRoute>
                    <GiftCards />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/gift-cards/new"
                element={
                  <ProtectedRoute>
                    <GiftCardForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/gift-cards/:id/edit"
                element={
                  <ProtectedRoute>
                    <GiftCardForm />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/campaigns"
                element={
                  <ProtectedRoute>
                    <Campaigns />
                  </ProtectedRoute>
                }
              />

              {/* Analytics Routes */}
              <Route
                path="/analytics"
                element={
                  <ProtectedRoute>
                    <Analytics />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/analytics/sales"
                element={
                  <ProtectedRoute>
                    <AnalyticsSales />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/analytics/products"
                element={
                  <ProtectedRoute>
                    <AnalyticsProducts />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/analytics/customers"
                element={
                  <ProtectedRoute>
                    <AnalyticsCustomers />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/analytics/reports"
                element={
                  <ProtectedRoute>
                    <AnalyticsReports />
                  </ProtectedRoute>
                }
              />
              
              {/* Journal Routes */}
              <Route
                path="/journal"
                element={
                  <ProtectedRoute>
                    <Journal />
                  </ProtectedRoute>
                }
              />
              
              {/* File Manager */}
              <Route
                path="/files"
                element={
                  <ProtectedRoute>
                    <Files />
                  </ProtectedRoute>
                }
              />
              
              {/* Settings Routes */}
              <Route
                path="/settings"
                element={
                  <ProtectedRoute>
                    <Settings />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/store"
                element={
                  <ProtectedRoute>
                    <SettingsStore />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/regions"
                element={
                  <ProtectedRoute>
                    <SettingsRegions />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/currencies"
                element={
                  <ProtectedRoute>
                    <SettingsCurrencies />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/shipping"
                element={
                  <ProtectedRoute>
                    <SettingsShipping />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/shipping-pricing"
                element={
                  <ProtectedRoute>
                    <ShippingPricing />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/payments"
                element={
                  <ProtectedRoute>
                    <SettingsPayments />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/taxes"
                element={
                  <ProtectedRoute>
                    <SettingsTaxes />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/users"
                element={
                  <ProtectedRoute>
                    <SettingsUsers />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/api-keys"
                element={
                  <ProtectedRoute>
                    <SettingsApiKeys />
                  </ProtectedRoute>
                }
              />

              {/* Test route */}
              <Route path="/test" element={<TestPage />} />

              {/* Test protected route */}
              <Route 
                path="/test-protected" 
                element={
                  <ProtectedRoute>
                    <TestPage />
                  </ProtectedRoute>
                } 
              />

              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>

            {/* Global toast notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 3000,
                  iconTheme: {
                    primary: '#10B981',
                    secondary: '#fff',
                  },
                },
                error: {
                  duration: 5000,
                  iconTheme: {
                    primary: '#EF4444',
                    secondary: '#fff',
                  },
                },
              }}
            />
            
            {/* React Query Devtools */}
            <ReactQueryDevtools initialIsOpen={false} />
          </div>
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App; 