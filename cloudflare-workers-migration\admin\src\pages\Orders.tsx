import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  DocumentTextIcon,
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  FunnelIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';
import { Order, OrderItem, Customer, PaginatedResponse } from '../types/schema';

const ORDER_STATUSES = [
  { value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'confirmed', label: 'Confirmed', color: 'bg-blue-100 text-blue-800' },
  { value: 'processing', label: 'Processing', color: 'bg-indigo-100 text-indigo-800' },
  { value: 'shipped', label: 'Shipped', color: 'bg-purple-100 text-purple-800' },
  { value: 'delivered', label: 'Delivered', color: 'bg-green-100 text-green-800' },
  { value: 'cancelled', label: 'Cancelled', color: 'bg-red-100 text-red-800' },
  { value: 'refunded', label: 'Refunded', color: 'bg-gray-100 text-gray-800' },
];

const FINANCIAL_STATUSES = [
  { value: 'pending', label: 'Pending', icon: ClockIcon },
  { value: 'paid', label: 'Paid', icon: CheckCircleIcon },
  { value: 'partially_paid', label: 'Partially Paid', icon: ClockIcon },
  { value: 'refunded', label: 'Refunded', icon: XCircleIcon },
  { value: 'partially_refunded', label: 'Partially Refunded', icon: XCircleIcon },
];

const FULFILLMENT_STATUSES = [
  { value: 'unfulfilled', label: 'Unfulfilled', icon: ClockIcon },
  { value: 'partial', label: 'Partial', icon: TruckIcon },
  { value: 'fulfilled', label: 'Fulfilled', icon: CheckCircleIcon },
];

const Orders: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [financialStatusFilter, setFinancialStatusFilter] = useState('all');
  const [fulfillmentStatusFilter, setFulfillmentStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState('all');
  const [sortBy, setSortBy] = useState('created_at:desc');
  const [showFilters, setShowFilters] = useState(false);

  const queryClient = useQueryClient();

  // Fetch orders
  const { data: ordersData, isLoading, error } = useQuery({
    queryKey: ['orders', currentPage, searchTerm, statusFilter, financialStatusFilter, fulfillmentStatusFilter, dateRange, sortBy],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        sort: sortBy,
      });

      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (financialStatusFilter !== 'all') params.append('financial_status', financialStatusFilter);
      if (fulfillmentStatusFilter !== 'all') params.append('fulfillment_status', fulfillmentStatusFilter);
      if (dateRange !== 'all') params.append('date_range', dateRange);

      const response = await api.get(`/admin/api/orders?${params}`);
      return response.data;
    },
  });

  // Update order status mutation
  const updateOrderStatusMutation = useMutation({
    mutationFn: async ({ orderId, status }: { orderId: string; status: string }) => {
      return api.put(`/admin/api/orders/${orderId}/status`, { status });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      toast.success('Order status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update order status');
    },
  });

  const getStatusColor = (status: string) => {
    const statusObj = ORDER_STATUSES.find(s => s.value === status);
    return statusObj?.color || 'bg-gray-100 text-gray-800';
  };

  const getFinancialStatusIcon = (status: string) => {
    const statusObj = FINANCIAL_STATUSES.find(s => s.value === status);
    return statusObj?.icon || ClockIcon;
  };

  const getFulfillmentStatusIcon = (status: string) => {
    const statusObj = FULFILLMENT_STATUSES.find(s => s.value === status);
    return statusObj?.icon || ClockIcon;
  };

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const orders = ordersData?.data || [];
  const totalPages = Math.ceil((ordersData?.pagination?.total || 0) / 20);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load orders. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Orders</h1>
          <p className="text-gray-600">Manage customer orders and fulfillment</p>
        </div>
        <div className="flex items-center space-x-3">
          <Link
            to="/orders/export"
            className="flex items-center text-gray-700 border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors duration-200"
          >
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Export
          </Link>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search orders by number, email, or customer..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center text-gray-700 border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50"
          >
            <FunnelIcon className="h-5 w-5 mr-2" />
            Filters
            <ChevronDownIcon className={`h-4 w-4 ml-2 transform transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {showFilters && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Statuses</option>
                  {ORDER_STATUSES.map(status => (
                    <option key={status.value} value={status.value}>{status.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Status</label>
                <select
                  value={financialStatusFilter}
                  onChange={(e) => setFinancialStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Payment Statuses</option>
                  {FINANCIAL_STATUSES.map(status => (
                    <option key={status.value} value={status.value}>{status.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Fulfillment</label>
                <select
                  value={fulfillmentStatusFilter}
                  onChange={(e) => setFulfillmentStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Fulfillment Statuses</option>
                  {FULFILLMENT_STATUSES.map(status => (
                    <option key={status.value} value={status.value}>{status.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                <select
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="yesterday">Yesterday</option>
                  <option value="last_7_days">Last 7 days</option>
                  <option value="last_30_days">Last 30 days</option>
                  <option value="last_90_days">Last 90 days</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Orders List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Orders ({ordersData?.total || 0})
            </h3>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="created_at:desc">Newest first</option>
              <option value="created_at:asc">Oldest first</option>
              <option value="total_amount:desc">Highest value</option>
              <option value="total_amount:asc">Lowest value</option>
              <option value="status:asc">Status A-Z</option>
            </select>
          </div>
        </div>

        {orders.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || statusFilter !== 'all' ? 'No orders match your search criteria.' : 'No orders have been placed yet.'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {orders.map((order: Order) => {
              const FinancialIcon = getFinancialStatusIcon(order.financial_status);
              const FulfillmentIcon = getFulfillmentStatusIcon(order.fulfillment_status);
              
              return (
                <div key={order.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3">
                        <Link
                          to={`/orders/${order.id}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          #{order.number}
                        </Link>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                          {ORDER_STATUSES.find(s => s.value === order.status)?.label}
                        </span>
                      </div>
                      
                      <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <span className="font-medium text-gray-900">
                            {order.customer?.first_name && order.customer?.last_name 
                              ? `${order.customer.first_name} ${order.customer.last_name}`
                              : order.email
                            }
                          </span>
                        </div>
                        <div className="flex items-center">
                          <FinancialIcon className="h-4 w-4 mr-1" />
                          <span>{FINANCIAL_STATUSES.find(s => s.value === order.financial_status)?.label}</span>
                        </div>
                        <div className="flex items-center">
                          <FulfillmentIcon className="h-4 w-4 mr-1" />
                          <span>{FULFILLMENT_STATUSES.find(s => s.value === order.fulfillment_status)?.label}</span>
                        </div>
                        <span>{new Date(order.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="text-lg font-medium text-gray-900">
                          {formatCurrency(order.total_amount, order.currency_code)}
                        </div>
                        {order.items && (
                          <div className="text-sm text-gray-500">
                            {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/orders/${order.id}`}
                          className="text-gray-400 hover:text-gray-600"
                          title="View order"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </Link>
                        
                        <select
                          value={order.status}
                          onChange={(e) => updateOrderStatusMutation.mutate({ 
                            orderId: order.id, 
                            status: e.target.value 
                          })}
                          className="text-sm border-0 bg-transparent text-gray-600 hover:text-gray-900 focus:ring-0"
                          title="Update status"
                        >
                          {ORDER_STATUSES.map(status => (
                            <option key={status.value} value={status.value}>{status.label}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {(currentPage - 1) * 20 + 1} to {Math.min(currentPage * 20, ordersData?.total || 0)} of {ordersData?.total || 0} orders
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Orders; 