import { Context, Next } from 'hono';
import { verify } from 'hono/jwt';
import { WorkerEnv } from 'handmadein-shared';

export interface AuthUser {
  id: string;
  email: string;
  role: 'admin' | 'customer';
}

// Extend Hono context to include authenticated user
declare module 'hono' {
  interface ContextVariableMap {
    user: AuthUser;
  }
}

export async function authMiddleware(c: Context<{ Bindings: WorkerEnv }>, next: Next) {
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({
      success: false,
      error: 'Authentication required',
    }, 401);
  }

  const token = authHeader.substring(7);
  
  try {
    const payload = await verify(token, c.env.JWT_SECRET) as any;
    if (!payload) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    // Check if token is expired
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return c.json({
        success: false,
        error: 'Token expired',
      }, 401);
    }

    // Set user in context
    c.set('user', {
      id: payload.userId,
      email: payload.email,
      role: payload.role,
    });

    await next();
  } catch (error) {
    return c.json({
      success: false,
      error: 'Invalid token',
    }, 401);
  }
}

// Admin-only middleware
export async function adminAuthMiddleware(c: Context<{ Bindings: WorkerEnv }>, next: Next) {
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({
      success: false,
      error: 'Authentication required',
    }, 401);
  }

  const token = authHeader.substring(7);
  
  try {
    const payload = await verify(token, c.env.JWT_SECRET) as any;
    if (!payload) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return c.json({
        success: false,
        error: 'Token expired',
      }, 401);
    }

    if (payload.role !== 'admin') {
      return c.json({
        success: false,
        error: 'Admin access required',
      }, 403);
    }

    c.set('user', {
      id: payload.userId,
      email: payload.email,
      role: payload.role,
    });

    await next();
  } catch (error) {
    return c.json({
      success: false,
      error: 'Invalid token',
    }, 401);
  }
}

// Customer-only middleware
export async function customerAuthMiddleware(c: Context<{ Bindings: WorkerEnv }>, next: Next) {
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({
      success: false,
      error: 'Authentication required',
    }, 401);
  }

  const token = authHeader.substring(7);
  
  try {
    const payload = await verify(token, c.env.JWT_SECRET) as any;
    if (!payload) {
      return c.json({
        success: false,
        error: 'Invalid token',
      }, 401);
    }

    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return c.json({
        success: false,
        error: 'Token expired',
      }, 401);
    }

    if (payload.role !== 'customer') {
      return c.json({
        success: false,
        error: 'Customer access required',
      }, 403);
    }

    c.set('user', {
      id: payload.userId,
      email: payload.email,
      role: payload.role,
    });

    await next();
  } catch (error) {
    return c.json({
      success: false,
      error: 'Invalid token',
    }, 401);
  }
} 