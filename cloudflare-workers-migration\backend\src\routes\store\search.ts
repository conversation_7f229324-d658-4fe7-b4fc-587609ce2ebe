import { Hono } from 'hono';
import { WorkerEnv } from 'handmadein-shared';
import { DatabaseService } from '../../services/database';

export const searchRoutes = new Hono<{ Bindings: WorkerEnv }>();

// Search products
searchRoutes.get('/products', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const query = c.req.query('q') || '';
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;
    const collection_id = c.req.query('collection_id');
    const min_price = c.req.query('min_price');
    const max_price = c.req.query('max_price');
    const sort = c.req.query('sort') || 'created_at';
    const order = c.req.query('order') || 'desc';

    if (!query.trim()) {
      return c.json({
        success: false,
        error: 'Search query is required',
      }, 400);
    }

    // Basic search implementation
    // In a production environment, you'd want to use a proper search engine
    const products = await db.searchProducts(query, {
      limit,
      offset,
    });

    // Apply additional filters
    let filteredProducts = products;

    if (collection_id) {
      filteredProducts = filteredProducts.filter(p => p.collection_id === collection_id);
    }

    // Get variants for each product to check prices
    for (const product of filteredProducts) {
      product.variants = await db.findMany('productVariants', {
        where: { product_id: product.id },
      });
    }

    // Apply price filters (simplified - would need proper price table in production)
    if (min_price || max_price) {
      filteredProducts = filteredProducts.filter(product => {
        // This is a simplified price check
        // In production, you'd query the actual price table
        return true; // Placeholder
      });
    }

    return c.json({
      success: true,
      data: filteredProducts,
      pagination: {
        page,
        limit,
        total: filteredProducts.length,
        pages: Math.ceil(filteredProducts.length / limit),
        hasNext: page * limit < filteredProducts.length,
        hasPrev: page > 1,
      },
      search: {
        query,
        filters: {
          collection_id,
          min_price,
          max_price,
          sort,
          order,
        },
      },
    });
  } catch (error) {
    console.error('Error searching products:', error);
    return c.json({
      success: false,
      error: 'Failed to search products',
    }, 500);
  }
});

// Search suggestions/autocomplete
searchRoutes.get('/suggestions', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const query = c.req.query('q') || '';
    const limit = parseInt(c.req.query('limit') || '5');

    if (!query.trim() || query.length < 2) {
      return c.json({
        success: true,
        data: [],
      });
    }

    // Get product suggestions
    const products = await db.searchProducts(query, { limit });
    
    const suggestions = products.map(product => ({
      type: 'product',
      title: product.title,
      handle: product.handle,
      thumbnail: product.thumbnail,
    }));

    // Get collection suggestions
    const collections = await db.findMany('productCollections', {
      // This would need a proper search implementation
      limit: 3,
    });

    const collectionSuggestions = collections
      .filter(collection => 
        collection.title.toLowerCase().includes(query.toLowerCase())
      )
      .map(collection => ({
        type: 'collection',
        title: collection.title,
        handle: collection.handle,
      }));

    return c.json({
      success: true,
      data: [...suggestions, ...collectionSuggestions].slice(0, limit),
    });
  } catch (error) {
    console.error('Error getting search suggestions:', error);
    return c.json({
      success: false,
      error: 'Failed to get suggestions',
    }, 500);
  }
});

// Search filters/facets
searchRoutes.get('/filters', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const query = c.req.query('q') || '';

    // Get available collections
    const collections = await db.findMany('productCollections', {
      limit: 50,
    });

    // Get available tags (simplified)
    const tags = await db.findMany('productTags', {
      limit: 50,
    });

    // In a production environment, you'd calculate these based on the search results
    const filters = {
      collections: collections.map(c => ({
        id: c.id,
        title: c.title,
        handle: c.handle,
        count: 0, // Would need to calculate actual count
      })),
      tags: tags.map(t => ({
        id: t.id,
        value: t.value,
        count: 0, // Would need to calculate actual count
      })),
      price_ranges: [
        { min: 0, max: 50, count: 0 },
        { min: 50, max: 100, count: 0 },
        { min: 100, max: 200, count: 0 },
        { min: 200, max: 500, count: 0 },
        { min: 500, max: null, count: 0 },
      ],
    };

    return c.json({
      success: true,
      data: filters,
    });
  } catch (error) {
    console.error('Error getting search filters:', error);
    return c.json({
      success: false,
      error: 'Failed to get filters',
    }, 500);
  }
}); 