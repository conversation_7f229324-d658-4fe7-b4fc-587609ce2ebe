import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  CogIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
  TruckIcon,
  CreditCardIcon,
  ShieldCheckIcon,
  BellIcon,
  MagnifyingGlassIcon,
  Cog8ToothIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { settingsApi } from '../lib/api';
import CurrencyManager from '../components/settings/CurrencyManager';
import WebhookManager from '../components/settings/WebhookManager';

interface SettingField {
  type: string;
  label: string;
  required?: boolean;
  options?: any;
  min?: number;
  max?: number;
  rows?: number;
}

interface SettingCategory {
  title: string;
  description: string;
  fields: Record<string, SettingField>;
}

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [hasChanges, setHasChanges] = useState(false);
  const queryClient = useQueryClient();

  // Fetch settings schema
  const { data: schemaData } = useQuery({
    queryKey: ['settings-schema'],
    queryFn: () => settingsApi.getSchema().then(res => res.data)
  });

  // Fetch all settings
  const { data: settingsData, isLoading } = useQuery({
    queryKey: ['settings'],
    queryFn: () => settingsApi.getAll().then(res => res.data)
  });

  // Fetch additional data for select options
  const { data: currenciesData } = useQuery({
    queryKey: ['currencies'],
    queryFn: async () => {
      try {
        const response = await settingsApi.getCurrencies();
        // Ensure we always return a valid object with currencies array
        return {
          currencies: response.data?.currencies || response.data?.data || response.data || []
        };
      } catch (error) {
        console.error('Failed to fetch currencies:', error);
        // Return empty array instead of undefined to prevent React Query errors
        return { currencies: [] };
      }
    }
  });

  const { data: regionsData } = useQuery({
    queryKey: ['regions'],
    queryFn: async () => {
      try {
        const response = await settingsApi.getRegions();
        // Ensure we always return a valid object with regions array
        return {
          regions: response.data?.regions || response.data?.data || response.data || []
        };
      } catch (error) {
        console.error('Failed to fetch regions:', error);
        // Return empty array instead of undefined to prevent React Query errors
        return { regions: [] };
      }
    }
  });

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: (data: { settings: Record<string, any>; category: string }) =>
      settingsApi.update(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings'] });
      toast.success('Settings updated successfully');
      setHasChanges(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update settings');
    }
  });

  // Initialize form data when settings load
  useEffect(() => {
    if (settingsData?.settings) {
      setFormData(settingsData.settings);
    }
  }, [settingsData]);

  const handleFieldChange = (category: string, key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSave = (category: string) => {
    if (formData[category]) {
      updateSettingsMutation.mutate({
        settings: formData[category],
        category
      });
    }
  };

  const renderField = (category: string, key: string, field: SettingField) => {
    const value = formData[category]?.[key] || '';

    switch (field.type) {
      case 'text':
      case 'email':
      case 'tel':
        return (
          <input
            type={field.type}
            value={value}
            onChange={(e) => handleFieldChange(category, key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required={field.required}
          />
        );

      case 'number':
        return (
          <input
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(category, key, parseFloat(e.target.value) || 0)}
            min={field.min}
            max={field.max}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required={field.required}
          />
        );

      case 'textarea':
        return (
          <textarea
            value={value}
            onChange={(e) => handleFieldChange(category, key, e.target.value)}
            rows={field.rows || 3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required={field.required}
          />
        );

      case 'boolean':
        return (
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={!!value}
              onChange={(e) => handleFieldChange(category, key, e.target.checked)}
              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600">Enable</span>
          </div>
        );

      case 'select':
        let options: any[] = [];
        
        if (field.options === 'currencies' && currenciesData?.currencies) {
          options = currenciesData.currencies.map((c: any) => ({
            value: c.code,
            label: `${c.name} (${c.symbol})`
          }));
        } else if (Array.isArray(field.options)) {
          options = field.options;
        }

        return (
          <select
            value={value}
            onChange={(e) => handleFieldChange(category, key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required={field.required}
          >
            <option value="">Select...</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'multiselect':
        const selectedValues = Array.isArray(value) ? value : [];
        const multiOptions = Array.isArray(field.options) ? field.options : [];

        return (
          <div className="space-y-2">
            {multiOptions.map((option) => (
              <label key={option.value} className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedValues.includes(option.value)}
                  onChange={(e) => {
                    const newValues = e.target.checked
                      ? [...selectedValues, option.value]
                      : selectedValues.filter(v => v !== option.value);
                    handleFieldChange(category, key, newValues);
                  }}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        );

      default:
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => handleFieldChange(category, key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        );
    }
  };

  const tabs = [
    { id: 'general', name: 'General', icon: CogIcon },
    { id: 'localization', name: 'Localization', icon: GlobeAltIcon },
    { id: 'tax', name: 'Tax', icon: CurrencyDollarIcon },
    { id: 'inventory', name: 'Inventory', icon: TruckIcon },
    { id: 'shipping', name: 'Shipping', icon: TruckIcon },
    { id: 'payments', name: 'Payments', icon: CreditCardIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'seo', name: 'SEO', icon: MagnifyingGlassIcon },
    { id: 'advanced', name: 'Advanced', icon: Cog8ToothIcon },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const schema = schemaData?.schema || {};
  const activeCategory = schema[activeTab] as SettingCategory;

  // Special handling for certain tabs that need custom components
  const renderSpecialTab = () => {
    switch (activeTab) {
      case 'localization':
        return (
          <div className="space-y-8">
            {/* Regular settings fields */}
            {activeCategory && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {activeCategory.title}
                </h3>
                <div className="space-y-6">
                  {Object.entries(activeCategory.fields).map(([key, field]) => (
                    <div key={key}>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {field.label}
                        {field.required && <span className="text-red-500 ml-1">*</span>}
                      </label>
                      {renderField(activeTab, key, field)}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Currency Manager */}
            <div className="border-t pt-8">
              <CurrencyManager />
            </div>
          </div>
        );
        
      case 'notifications':
        return (
          <div className="space-y-8">
            {/* Regular settings fields */}
            {activeCategory && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {activeCategory.title}
                </h3>
                <div className="space-y-6">
                  {Object.entries(activeCategory.fields).map(([key, field]) => (
                    <div key={key}>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {field.label}
                        {field.required && <span className="text-red-500 ml-1">*</span>}
                      </label>
                      {renderField(activeTab, key, field)}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Webhook Manager */}
            <div className="border-t pt-8">
              <WebhookManager />
            </div>
          </div>
        );
        
      default:
        return null;
    }
  };

  const specialTab = renderSpecialTab();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>
          <p className="mt-1 text-sm text-gray-500">
            Configure your store settings and preferences
          </p>
        </div>
        
        {hasChanges && !specialTab && (
          <div className="flex items-center space-x-3">
            <span className="text-sm text-amber-600">You have unsaved changes</span>
            <button
              onClick={() => handleSave(activeTab)}
              disabled={updateSettingsMutation.isPending}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {updateSettingsMutation.isPending ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        )}
      </div>

      <div className="flex space-x-6">
        {/* Sidebar Navigation */}
        <div className="w-64 flex-shrink-0">
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-4 py-3 text-sm font-medium text-left transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <div className="bg-white rounded-lg border border-gray-200">
            {specialTab ? (
              <div className="p-6">
                {specialTab}
              </div>
            ) : activeCategory ? (
              <>
                {/* Category Header */}
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900">
                    {activeCategory.title}
                  </h2>
                  <p className="mt-1 text-sm text-gray-500">
                    {activeCategory.description}
                  </p>
                </div>

                {/* Category Fields */}
                <div className="px-6 py-6">
                  <div className="space-y-6">
                    {Object.entries(activeCategory.fields).map(([key, field]) => (
                      <div key={key}>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {field.label}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </label>
                        {renderField(activeTab, key, field)}
                      </div>
                    ))}
                  </div>

                  {/* Save Button */}
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <div className="flex justify-end">
                      <button
                        onClick={() => handleSave(activeTab)}
                        disabled={updateSettingsMutation.isPending || !hasChanges}
                        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {updateSettingsMutation.isPending ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Saving...
                          </div>
                        ) : (
                          'Save Settings'
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="px-6 py-12 text-center">
                <CogIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Settings Category Not Found
                </h3>
                <p className="text-gray-500">
                  The selected settings category could not be loaded.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings; 