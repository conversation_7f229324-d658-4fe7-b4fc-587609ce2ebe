import axios from 'axios';

// Create axios instance with base configuration
export const api = axios.create({
  baseURL: (import.meta as any).env?.VITE_API_URL || 'http://localhost:8787',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API endpoint helpers for the simplified schema
export const endpoints = {
  // Auth
  auth: {
    login: '/admin/auth/login',
    logout: '/admin/auth/logout',
    me: '/admin/auth/me',
    refresh: '/admin/auth/refresh',
  },

  // Products
  products: {
    list: '/admin/api/products',
    create: '/admin/api/products',
    get: (id: string) => `/admin/api/products/${id}`,
    update: (id: string) => `/admin/api/products/${id}`,
    delete: (id: string) => `/admin/api/products/${id}`,

    // Variants
    variants: (id: string) => `/admin/api/products/${id}/variants`,
    createVariant: (id: string) => `/admin/api/products/${id}/variants`,
    updateVariant: (productId: string, variantId: string) => `/admin/api/products/${productId}/variants/${variantId}`,
    deleteVariant: (productId: string, variantId: string) => `/admin/api/products/${productId}/variants/${variantId}`,

    // Variant Prices
    variantPrices: (productId: string, variantId: string) => `/admin/api/products/${productId}/variants/${variantId}/prices`,
    createVariantPrice: (productId: string, variantId: string) => `/admin/api/products/${productId}/variants/${variantId}/prices`,
    updateVariantPrice: (productId: string, variantId: string, priceId: string) => `/admin/api/products/${productId}/variants/${variantId}/prices/${priceId}`,
    deleteVariantPrice: (productId: string, variantId: string, priceId: string) => `/admin/api/products/${productId}/variants/${variantId}/prices/${priceId}`,

    // Images
    images: (id: string) => `/admin/api/products/${id}/images`,
    createImage: (id: string) => `/admin/api/products/${id}/images`,
    updateImage: (productId: string, imageId: string) => `/admin/api/products/${productId}/images/${imageId}`,
    deleteImage: (productId: string, imageId: string) => `/admin/api/products/${productId}/images/${imageId}`,

    // Options
    options: (id: string) => `/admin/api/products/${id}/options`,
    createOption: (id: string) => `/admin/api/products/${id}/options`,
    updateOption: (productId: string, optionId: string) => `/admin/api/products/${productId}/options/${optionId}`,
    deleteOption: (productId: string, optionId: string) => `/admin/api/products/${productId}/options/${optionId}`,

    // Option Values
    optionValues: (productId: string, optionId: string) => `/admin/api/products/${productId}/options/${optionId}/values`,
    createOptionValue: (productId: string, optionId: string) => `/admin/api/products/${productId}/options/${optionId}/values`,
    updateOptionValue: (productId: string, optionId: string, valueId: string) => `/admin/api/products/${productId}/options/${optionId}/values/${valueId}`,
    deleteOptionValue: (productId: string, optionId: string, valueId: string) => `/admin/api/products/${productId}/options/${optionId}/values/${valueId}`,

    // Translations
    translations: (id: string) => `/admin/api/products/${id}/translations`,
    createTranslation: (id: string) => `/admin/api/products/${id}/translations`,
    updateTranslation: (productId: string, languageCode: string) => `/admin/api/products/${productId}/translations/${languageCode}`,
    deleteTranslation: (productId: string, languageCode: string) => `/admin/api/products/${productId}/translations/${languageCode}`,

    // Bulk Operations
    bulkDelete: '/admin/api/products/bulk-delete',
    bulkUpdate: '/admin/api/products/bulk-update',
    export: '/admin/api/products/export',
  },
  
  // Collections
  collections: {
    list: '/admin/api/collections',
    create: '/admin/api/collections',
    get: (id: string) => `/admin/api/collections/${id}`,
    update: (id: string) => `/admin/api/collections/${id}`,
    delete: (id: string) => `/admin/api/collections/${id}`,
    bulkDelete: '/admin/api/collections/bulk-delete',
  },
  
  // Product Types
  productTypes: {
    list: '/admin/api/product-types',
    create: '/admin/api/product-types',
    get: (id: string) => `/admin/api/product-types/${id}`,
    update: (id: string) => `/admin/api/product-types/${id}`,
    delete: (id: string) => `/admin/api/product-types/${id}`,
  },
  
  // Categories
  categories: {
    list: '/admin/api/categories',
    create: '/admin/api/categories',
    get: (id: string) => `/admin/api/categories/${id}`,
    update: (id: string) => `/admin/api/categories/${id}`,
    delete: (id: string) => `/admin/api/categories/${id}`,
    bulkDelete: '/admin/api/categories/bulk-delete',
  },
  
  // Orders
  orders: {
    list: '/admin/api/orders',
    create: '/admin/api/orders',
    get: (id: string) => `/admin/api/orders/${id}`,
    update: (id: string) => `/admin/api/orders/${id}`,
    cancel: (id: string) => `/admin/api/orders/${id}/cancel`,
    fulfill: (id: string) => `/admin/api/orders/${id}/fulfillments`,
    updateStatus: (id: string) => `/admin/api/orders/${id}/status`,
    updateNotes: (id: string) => `/admin/api/orders/${id}/notes`,
    createFulfillment: (id: string) => `/admin/api/orders/${id}/fulfillments`,

    // Order Items
    items: (id: string) => `/admin/api/orders/${id}/items`,
    updateItem: (orderId: string, itemId: string) => `/admin/api/orders/${orderId}/items/${itemId}`,

    // Payments
    payments: (id: string) => `/admin/api/orders/${id}/payments`,
    createPayment: (id: string) => `/admin/api/orders/${id}/payments`,
    updatePayment: (orderId: string, paymentId: string) => `/admin/api/orders/${orderId}/payments/${paymentId}`,

    // Fulfillments
    fulfillments: (id: string) => `/admin/api/orders/${id}/fulfillments`,
    updateFulfillment: (orderId: string, fulfillmentId: string) => `/admin/api/orders/${orderId}/fulfillments/${fulfillmentId}`,
  },
  
  // Customers
  customers: {
    list: '/admin/api/customers',
    create: '/admin/api/customers',
    get: (id: string) => `/admin/api/customers/${id}`,
    update: (id: string) => `/admin/api/customers/${id}`,
    delete: (id: string) => `/admin/api/customers/${id}`,
    bulkDelete: '/admin/api/customers/bulk-delete',

    // Customer Addresses
    addresses: (id: string) => `/admin/api/customers/${id}/addresses`,
    createAddress: (id: string) => `/admin/api/customers/${id}/addresses`,
    updateAddress: (customerId: string, addressId: string) => `/admin/api/customers/${customerId}/addresses/${addressId}`,
    deleteAddress: (customerId: string, addressId: string) => `/admin/api/customers/${customerId}/addresses/${addressId}`,

    // Customer Orders
    orders: (id: string) => `/admin/api/customers/${id}/orders`,
  },

  // Customer Groups
  customerGroups: {
    list: '/admin/api/customer-groups',
    create: '/admin/api/customer-groups',
    get: (id: string) => `/admin/api/customer-groups/${id}`,
    update: (id: string) => `/admin/api/customer-groups/${id}`,
    delete: (id: string) => `/admin/api/customer-groups/${id}`,
    bulkDelete: '/admin/api/customer-groups/bulk-delete',
  },
  
  // Settings
  settings: {
    list: '/admin/api/settings',
    get: (key: string) => `/admin/api/settings/${key}`,
    update: '/admin/api/settings',
    updateSingle: (key: string) => `/admin/api/settings/${key}`,
    delete: (key: string) => `/admin/api/settings/${key}`,
    schema: '/admin/api/settings/schema',
  },

  // Currencies
  currencies: {
    list: '/admin/api/currencies',
    create: '/admin/api/currencies',
    get: (code: string) => `/admin/api/currencies/${code}`,
    update: (code: string) => `/admin/api/currencies/${code}`,
    delete: (code: string) => `/admin/api/currencies/${code}`,
    setDefault: (code: string) => `/admin/api/currencies/${code}/set-default`,
  },

  // Languages
  languages: {
    list: '/admin/api/languages',
    create: '/admin/api/languages',
    get: (code: string) => `/admin/api/languages/${code}`,
    update: (code: string) => `/admin/api/languages/${code}`,
    delete: (code: string) => `/admin/api/languages/${code}`,
    setDefault: (code: string) => `/admin/api/languages/${code}/set-default`,
  },

  // Regions
  regions: {
    list: '/admin/api/regions',
    create: '/admin/api/regions',
    get: (id: string) => `/admin/api/regions/${id}`,
    update: (id: string) => `/admin/api/regions/${id}`,
    delete: (id: string) => `/admin/api/regions/${id}`,
  },

  // Shipping
  shipping: {
    zones: {
      list: '/admin/api/shipping/zones',
      create: '/admin/api/shipping/zones',
      get: (id: string) => `/admin/api/shipping/zones/${id}`,
      update: (id: string) => `/admin/api/shipping/zones/${id}`,
      delete: (id: string) => `/admin/api/shipping/zones/${id}`,
    },
    methods: {
      list: (zoneId: string) => `/admin/api/shipping/zones/${zoneId}/methods`,
      create: (zoneId: string) => `/admin/api/shipping/zones/${zoneId}/methods`,
      get: (zoneId: string, methodId: string) => `/admin/api/shipping/zones/${zoneId}/methods/${methodId}`,
      update: (zoneId: string, methodId: string) => `/admin/api/shipping/zones/${zoneId}/methods/${methodId}`,
      delete: (zoneId: string, methodId: string) => `/admin/api/shipping/zones/${zoneId}/methods/${methodId}`,
    },
  },
  
  // Files (for future implementation)
  files: {
    upload: '/admin/api/files',
    list: '/admin/api/files',
    delete: (id: string) => `/admin/api/files/${id}`,
  },
  
  // Discounts
  discounts: {
    list: '/admin/api/discounts',
    create: '/admin/api/discounts',
    get: (id: string) => `/admin/api/discounts/${id}`,
    update: (id: string) => `/admin/api/discounts/${id}`,
    delete: (id: string) => `/admin/api/discounts/${id}`,
    bulkDelete: '/admin/api/discounts/bulk-delete',
    usage: (id: string) => `/admin/api/discounts/${id}/usage`,
  },

  // Returns
  returns: {
    list: '/admin/api/returns',
    create: '/admin/api/returns',
    get: (id: string) => `/admin/api/returns/${id}`,
    update: (id: string) => `/admin/api/returns/${id}`,
    approve: (id: string) => `/admin/api/returns/${id}/approve`,
    receive: (id: string) => `/admin/api/returns/${id}/receive`,
    process: (id: string) => `/admin/api/returns/${id}/process`,
    complete: (id: string) => `/admin/api/returns/${id}/complete`,
    cancel: (id: string) => `/admin/api/returns/${id}/cancel`,

    // Return Items
    items: (id: string) => `/admin/api/returns/${id}/items`,
    updateItem: (returnId: string, itemId: string) => `/admin/api/returns/${returnId}/items/${itemId}`,

    // Return Refunds
    refunds: (id: string) => `/admin/api/returns/${id}/refunds`,
    createRefund: (id: string) => `/admin/api/returns/${id}/refunds`,
  },

  // Exchanges
  exchanges: {
    list: '/admin/api/exchanges',
    create: '/admin/api/exchanges',
    get: (id: string) => `/admin/api/exchanges/${id}`,
    update: (id: string) => `/admin/api/exchanges/${id}`,
    approve: (id: string) => `/admin/api/exchanges/${id}/approve`,
    ship: (id: string) => `/admin/api/exchanges/${id}/ship`,
    complete: (id: string) => `/admin/api/exchanges/${id}/complete`,
    cancel: (id: string) => `/admin/api/exchanges/${id}/cancel`,

    // Exchange Items
    returnItems: (id: string) => `/admin/api/exchanges/${id}/return-items`,
    newItems: (id: string) => `/admin/api/exchanges/${id}/new-items`,
  },
  
  // Tags
  tags: {
    list: '/admin/api/tags',
    create: '/admin/api/tags',
    update: (id: string) => `/admin/api/tags/${id}`,
    delete: (id: string) => `/admin/api/tags/${id}`,
    bulkDelete: '/admin/api/tags/bulk-delete',
  },
  
  // Inventory
  inventory: {
    items: '/admin/api/inventory/items',
    createItem: '/admin/api/inventory/items',
    updateItem: (id: string) => `/admin/api/inventory/items/${id}`,
    deleteItem: (id: string) => `/admin/api/inventory/items/${id}`,
    bulkDeleteItems: '/admin/api/inventory/items/bulk-delete',
    
    locations: '/admin/api/inventory/locations',
    createLocation: '/admin/api/inventory/locations',
    updateLocation: (id: string) => `/admin/api/inventory/locations/${id}`,
    deleteLocation: (id: string) => `/admin/api/inventory/locations/${id}`,
    bulkDeleteLocations: '/admin/api/inventory/locations/bulk-delete',
    
    levels: '/admin/api/inventory/levels',
    createLevel: '/admin/api/inventory/levels',
    updateLevel: (id: string) => `/admin/api/inventory/levels/${id}`,
    deleteLevel: (id: string) => `/admin/api/inventory/levels/${id}`,
    
    adjustments: '/admin/api/inventory/adjustments',
    createAdjustment: '/admin/api/inventory/adjustments',
    
    reservations: '/admin/api/inventory/reservations',
    createReservation: '/admin/api/inventory/reservations',
    updateReservation: (id: string) => `/admin/api/inventory/reservations/${id}`,
    deleteReservation: (id: string) => `/admin/api/inventory/reservations/${id}`,
  },
  
  // Analytics
  analytics: {
    dashboard: '/admin/api/analytics/dashboard',
    sales: '/admin/api/analytics/sales',
    products: '/admin/api/analytics/products',
    customers: '/admin/api/analytics/customers',
    orders: '/admin/api/analytics/orders',
    events: '/admin/api/analytics/events',
    reports: '/admin/api/analytics/reports',
  },

  // Webhooks
  webhooks: {
    list: '/admin/api/webhooks',
    create: '/admin/api/webhooks',
    get: (id: string) => `/admin/api/webhooks/${id}`,
    update: (id: string) => `/admin/api/webhooks/${id}`,
    delete: (id: string) => `/admin/api/webhooks/${id}`,
    test: (id: string) => `/admin/api/webhooks/${id}/test`,
    deliveries: (id: string) => `/admin/api/webhooks/${id}/deliveries`,
  },

  // Posts (Journal/Blog)
  posts: {
    list: '/admin/api/posts',
    create: '/admin/api/posts',
    get: (id: string) => `/admin/api/posts/${id}`,
    update: (id: string) => `/admin/api/posts/${id}`,
    delete: (id: string) => `/admin/api/posts/${id}`,
    publish: (id: string) => `/admin/api/posts/${id}/publish`,
    unpublish: (id: string) => `/admin/api/posts/${id}/unpublish`,
    bulkDelete: '/admin/api/posts/bulk-delete',

    // Post Translations
    translations: (id: string) => `/admin/api/posts/${id}/translations`,
    createTranslation: (id: string) => `/admin/api/posts/${id}/translations`,
    updateTranslation: (postId: string, languageCode: string) => `/admin/api/posts/${postId}/translations/${languageCode}`,
    deleteTranslation: (postId: string, languageCode: string) => `/admin/api/posts/${postId}/translations/${languageCode}`,
  },

  // Pages
  pages: {
    list: '/admin/api/pages',
    create: '/admin/api/pages',
    get: (id: string) => `/admin/api/pages/${id}`,
    update: (id: string) => `/admin/api/pages/${id}`,
    delete: (id: string) => `/admin/api/pages/${id}`,
    bulkDelete: '/admin/api/pages/bulk-delete',

    // Page Translations
    translations: (id: string) => `/admin/api/pages/${id}/translations`,
    createTranslation: (id: string) => `/admin/api/pages/${id}/translations`,
    updateTranslation: (pageId: string, languageCode: string) => `/admin/api/pages/${pageId}/translations/${languageCode}`,
    deleteTranslation: (pageId: string, languageCode: string) => `/admin/api/pages/${pageId}/translations/${languageCode}`,
  },
  
  // Gift Cards
  giftCards: {
    list: '/admin/api/gift-cards',
    create: '/admin/api/gift-cards',
    get: (id: string) => `/admin/api/gift-cards/${id}`,
    update: (id: string) => `/admin/api/gift-cards/${id}`,
    delete: (id: string) => `/admin/api/gift-cards/${id}`,
  },
  
  // Journal
  journal: {
    list: '/admin/api/journal',
    create: '/admin/api/journal',
    get: (id: string) => `/admin/api/journal/${id}`,
    update: (id: string) => `/admin/api/journal/${id}`,
    delete: (id: string) => `/admin/api/journal/${id}`,
    publish: (id: string) => `/admin/api/journal/${id}/publish`,
    bulkDelete: '/admin/api/journal/bulk-delete',
    analytics: '/admin/api/journal/analytics',
  },
};

// Helper functions for API calls
export const authApi = {
  login: (email: string, password: string) =>
    api.post(endpoints.auth.login, { email, password }),
  
  logout: () =>
    api.post(endpoints.auth.logout),
  
  getMe: () =>
    api.get(endpoints.auth.me),
};

export const productsApi = {
  list: (params?: { page?: number; limit?: number; search?: string; status?: string; collection_id?: string }) =>
    api.get(endpoints.products.list, { params }),
  
  get: (id: string) =>
    api.get(endpoints.products.get(id)),
  
  create: (data: any) =>
    api.post(endpoints.products.create, data),
  
  update: (id: string, data: any) =>
    api.put(endpoints.products.update(id), data),
  
  delete: (id: string) =>
    api.delete(endpoints.products.delete(id)),
  
  bulkDelete: (data: { productIds: string[] }) =>
    api.post('/admin/api/products/bulk-delete', data),
  
  bulkUpdate: (data: { productIds: string[]; status: string }) =>
    api.post('/admin/api/products/bulk-update', data),
  
  createVariant: (productId: string, data: any) =>
    api.post(endpoints.products.createVariant(productId), data),
  
  createImage: (productId: string, data: any) =>
    api.post(endpoints.products.createImage(productId), data),
  
  createOption: (productId: string, data: any) =>
    api.post(endpoints.products.createOption(productId), data),
};

export const collectionsApi = {
  list: () =>
    api.get(endpoints.collections.list),
  
  get: (id: string) =>
    api.get(endpoints.collections.get(id)),
  
  create: (data: any) =>
    api.post(endpoints.collections.create, data),
  
  update: (id: string, data: any) =>
    api.put(endpoints.collections.update(id), data),
  
  delete: (id: string) =>
    api.delete(endpoints.collections.delete(id)),
  
  bulkDelete: (collectionIds: string[]) =>
    api.post('/admin/api/collections/bulk-delete', { collectionIds }),
};

export const customersApi = {
  list: (params?: { page?: number; limit?: number; search?: string }) =>
    api.get(endpoints.customers.list, { params }),
  
  get: (id: string) =>
    api.get(endpoints.customers.get(id)),
  
  create: (data: any) =>
    api.post(endpoints.customers.create, data),
  
  update: (id: string, data: any) =>
    api.put(endpoints.customers.update(id), data),
  
  delete: (id: string) =>
    api.delete(endpoints.customers.delete(id)),
};

export const ordersApi = {
  list: (params?: { 
    page?: number; 
    limit?: number; 
    search?: string; 
    status?: string; 
    financial_status?: string; 
    fulfillment_status?: string;
  }) =>
    api.get('/admin/api/orders', { params }),
  
  get: (id: string) =>
    api.get(`/admin/api/orders/${id}`),
  
  updateStatus: (id: string, status: string) =>
    api.put(`/admin/api/orders/${id}/status`, { status }),
  
  updateNotes: (id: string, notes: string) =>
    api.put(`/admin/api/orders/${id}/notes`, { notes }),
  
  createFulfillment: (id: string, data: any) =>
    api.post(`/admin/api/orders/${id}/fulfillments`, data),
};

export const settingsApi = {
  // Core settings
  getAll: (category?: string) => api.get('/admin/api/settings', { 
    params: category ? { category } : {} 
  }),
  get: (key: string) => api.get(`/admin/api/settings/${key}`),
  update: (data: { settings: Record<string, any>; category?: string }) => 
    api.put('/admin/api/settings', data),
  updateSingle: (key: string, data: { value: any; category?: string; is_public?: boolean }) =>
    api.put(`/admin/api/settings/${key}`, data),
  delete: (key: string) => api.delete(`/admin/api/settings/${key}`),
  
  // Schema
  getSchema: () => api.get('/admin/api/settings/schema'),
  
  // Currencies
  getCurrencies: () => api.get('/admin/api/currencies'),
  saveCurrency: (data: any) => api.post('/admin/api/currencies', data),
  
  // Regions
  getRegions: () => api.get('/admin/api/regions'),
  saveRegion: (data: any) => api.post('/admin/api/regions', data),
  
  // Tax rates
  getTaxRates: () => api.get('/admin/api/tax-rates'),
  createTaxRate: (data: any) => api.post('/admin/api/tax-rates', data),
  
  // Shipping zones
  getShippingZones: () => api.get('/admin/api/shipping-zones'),
  createShippingZone: (data: any) => api.post('/admin/api/shipping-zones', data),
  
  // Payment providers
  getPaymentProviders: () => api.get('/admin/api/payment-providers'),
  updatePaymentProvider: (id: string, data: any) => api.put(`/admin/api/payment-providers/${id}`, data),
  
  // Webhooks
  getWebhooks: () => api.get('/admin/api/webhooks'),
  createWebhook: (data: any) => api.post('/admin/api/webhooks', data),
  testWebhook: (id: string) => api.post(`/admin/api/webhooks/${id}/test`)
};

// Legacy exports for backward compatibility
export const getSettings = () => settingsApi.getAll();
export const getStoreSettings = () => settingsApi.getAll('general');
export const updateStoreSettings = (data: any) => settingsApi.update({ settings: data, category: 'general' });

// ===========================================
// GIFT CARDS API
// ===========================================

export const giftCardsApi = {
  list: (params?: any) => api.get('/admin/api/gift-cards', { params }),
  get: (id: string) => api.get(`/admin/api/gift-cards/${id}`),
  create: (data: any) => api.post('/admin/api/gift-cards', data),
  update: (id: string, data: any) => api.put(`/admin/api/gift-cards/${id}`, data),
  delete: (id: string) => api.delete(`/admin/api/gift-cards/${id}`),
  bulkDelete: (giftCardIds: string[]) => api.post('/admin/api/gift-cards/bulk-delete', { giftCardIds }),
};

// ===========================================
// CAMPAIGNS API
// ===========================================

export const campaignsApi = {
  list: (params?: any) => api.get('/admin/api/campaigns', { params }),
  get: (id: string) => api.get(`/admin/api/campaigns/${id}`),
  create: (data: any) => api.post('/admin/api/campaigns', data),
  update: (id: string, data: any) => api.put(`/admin/api/campaigns/${id}`, data),
  delete: (id: string) => api.delete(`/admin/api/campaigns/${id}`),
  bulkDelete: (campaignIds: string[]) => api.post('/admin/api/campaigns/bulk-delete', { campaignIds }),
};

// Legacy exports for backward compatibility
export const getGiftCards = (params?: any) => giftCardsApi.list(params);
export const getGiftCard = (id: string) => giftCardsApi.get(id);
export const createGiftCard = (data: any) => giftCardsApi.create(data);
export const updateGiftCard = (id: string, data: any) => giftCardsApi.update(id, data);
export const deleteGiftCard = (id: string) => giftCardsApi.delete(id);

export const getCampaigns = (params?: any) => campaignsApi.list(params);
export const getCampaign = (id: string) => campaignsApi.get(id);
export const createCampaign = (data: any) => campaignsApi.create(data);
export const updateCampaign = (id: string, data: any) => campaignsApi.update(id, data);
export const deleteCampaign = (id: string) => campaignsApi.delete(id);

// ===========================================
// FILES API
// ===========================================

export const filesApi = {
  // Files
  list: (params?: any) => api.get('/admin/api/files', { params }),
  upload: (formData: FormData) => api.post('/admin/api/files/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  update: (id: string, data: any) => api.put(`/admin/api/files/${id}`, data),
  delete: (id: string) => api.delete(`/admin/api/files/${id}`),
  bulkDelete: (fileIds: string[]) => api.post('/admin/api/files/bulk-delete', { fileIds }),
  move: (fileIds: string[], folder_id: string | null) => api.post('/admin/api/files/move', { fileIds, folder_id }),
  analytics: () => api.get('/admin/api/files/analytics'),

  // Folders
  folders: {
    list: (params?: any) => api.get('/admin/api/files/folders', { params }),
    create: (data: any) => api.post('/admin/api/files/folders', data),
    update: (id: string, data: any) => api.put(`/admin/api/files/folders/${id}`, data),
    delete: (id: string) => api.delete(`/admin/api/files/folders/${id}`),
  }
}; 