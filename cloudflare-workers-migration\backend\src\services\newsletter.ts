import { WorkerEnv } from 'handmadein-shared';
import { EmailService } from './email';

export interface NewsletterSubscriber {
  id: string;
  email: string;
  subscribed_at: Date;
  unsubscribed_at?: Date;
  status: 'subscribed' | 'unsubscribed' | 'pending';
  metadata?: Record<string, any>;
}

export class NewsletterService {
  private env: WorkerEnv;
  private emailService: EmailService;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.emailService = new EmailService(env);
  }

  /**
   * Subscribe an email to the newsletter
   */
  async subscribe(email: string, metadata?: Record<string, any>): Promise<{ success: boolean; message: string }> {
    try {
      // Validate email format
      if (!this.isValidEmail(email)) {
        return { success: false, message: 'Invalid email address' };
      }

      // Check if already subscribed
      const existing = await this.getSubscriber(email);
      if (existing && existing.status === 'subscribed') {
        return { success: false, message: '<PERSON><PERSON> already subscribed' };
      }

      // If previously unsubscribed, reactivate
      if (existing && existing.status === 'unsubscribed') {
        await this.reactivateSubscriber(email);
        return { success: true, message: 'Successfully resubscribed to newsletter' };
      }

      // Create new subscription
      const subscriber: Omit<NewsletterSubscriber, 'id'> = {
        email: email.toLowerCase(),
        subscribed_at: new Date(),
        status: 'subscribed',
        metadata: metadata || {}
      };

      await this.createSubscriber(subscriber);

      // Send welcome email
      try {
        await this.emailService.sendEmail({
          to: email,
          subject: 'Welcome to HandmadeIn.ro Newsletter!',
          html: this.getWelcomeEmailTemplate(email),
          text: this.getWelcomeEmailText(email)
        });
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
        // Don't fail the subscription if email fails
      }

      return { success: true, message: 'Successfully subscribed to newsletter' };
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      return { success: false, message: 'Failed to subscribe to newsletter' };
    }
  }

  /**
   * Unsubscribe an email from the newsletter
   */
  async unsubscribe(email: string): Promise<{ success: boolean; message: string }> {
    try {
      const subscriber = await this.getSubscriber(email);
      if (!subscriber) {
        return { success: false, message: 'Email not found in newsletter' };
      }

      if (subscriber.status === 'unsubscribed') {
        return { success: false, message: 'Email already unsubscribed' };
      }

      await this.updateSubscriber(email, {
        status: 'unsubscribed',
        unsubscribed_at: new Date()
      });

      return { success: true, message: 'Successfully unsubscribed from newsletter' };
    } catch (error) {
      console.error('Newsletter unsubscribe error:', error);
      return { success: false, message: 'Failed to unsubscribe from newsletter' };
    }
  }

  /**
   * Get subscriber by email
   */
  private async getSubscriber(email: string): Promise<NewsletterSubscriber | null> {
    try {
      const stmt = this.env.DB.prepare(`
        SELECT * FROM newsletter_subscribers 
        WHERE email = ? 
        LIMIT 1
      `);
      
      const result = await stmt.bind(email.toLowerCase()).first();
      
      if (!result) {
        return null;
      }
      
      return {
        id: result.id as string,
        email: result.email as string,
        subscribed_at: new Date(result.subscribed_at as string),
        unsubscribed_at: result.unsubscribed_at ? new Date(result.unsubscribed_at as string) : undefined,
        status: result.status as 'subscribed' | 'unsubscribed' | 'pending',
        metadata: result.metadata ? JSON.parse(result.metadata as string) : {}
      };
    } catch (error) {
      console.error('Error getting subscriber:', error);
      return null;
    }
  }

  /**
   * Create new subscriber
   */
  private async createSubscriber(subscriber: Omit<NewsletterSubscriber, 'id'>): Promise<void> {
    const id = crypto.randomUUID();
    
    const stmt = this.env.DB.prepare(`
      INSERT INTO newsletter_subscribers (id, email, subscribed_at, status, metadata)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    await stmt.bind(
      id,
      subscriber.email,
      subscriber.subscribed_at.toISOString(),
      subscriber.status,
      JSON.stringify(subscriber.metadata || {})
    ).run();
  }

  /**
   * Update subscriber
   */
  private async updateSubscriber(email: string, updates: Partial<NewsletterSubscriber>): Promise<void> {
    const setParts: string[] = [];
    const values: any[] = [];
    
    if (updates.status) {
      setParts.push('status = ?');
      values.push(updates.status);
    }
    
    if (updates.unsubscribed_at) {
      setParts.push('unsubscribed_at = ?');
      values.push(updates.unsubscribed_at.toISOString());
    }
    
    if (updates.metadata) {
      setParts.push('metadata = ?');
      values.push(JSON.stringify(updates.metadata));
    }
    
    if (setParts.length === 0) {
      return;
    }
    
    values.push(email.toLowerCase());
    
    const stmt = this.env.DB.prepare(`
      UPDATE newsletter_subscribers 
      SET ${setParts.join(', ')}, updated_at = ?
      WHERE email = ?
    `);
    
    await stmt.bind(...values, new Date().toISOString()).run();
  }

  /**
   * Reactivate unsubscribed subscriber
   */
  private async reactivateSubscriber(email: string): Promise<void> {
    const stmt = this.env.DB.prepare(`
      UPDATE newsletter_subscribers 
      SET status = 'subscribed', unsubscribed_at = NULL, updated_at = ?
      WHERE email = ?
    `);
    
    await stmt.bind(new Date().toISOString(), email.toLowerCase()).run();
  }

  /**
   * Get all subscribers (admin)
   */
  async getAllSubscribers(options: {
    page?: number;
    limit?: number;
    status?: 'subscribed' | 'unsubscribed' | 'pending';
  } = {}): Promise<{ subscribers: NewsletterSubscriber[]; count: number }> {
    const { page = 1, limit = 50, status } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = '';
    const params: any[] = [];
    
    if (status) {
      whereClause = 'WHERE status = ?';
      params.push(status);
    }
    
    // Get total count
    const countStmt = this.env.DB.prepare(`
      SELECT COUNT(*) as count FROM newsletter_subscribers ${whereClause}
    `);
    const countResult = await countStmt.bind(...params).first();
    const count = countResult?.count || 0;
    
    // Get subscribers
    const stmt = this.env.DB.prepare(`
      SELECT * FROM newsletter_subscribers 
      ${whereClause}
      ORDER BY subscribed_at DESC
      LIMIT ? OFFSET ?
    `);
    
    const results = await stmt.bind(...params, limit, offset).all();
    const subscribers = results.results.map(row => ({
      id: row.id as string,
      email: row.email as string,
      subscribed_at: new Date(row.subscribed_at as string),
      unsubscribed_at: row.unsubscribed_at ? new Date(row.unsubscribed_at as string) : undefined,
      status: row.status as 'subscribed' | 'unsubscribed' | 'pending',
      metadata: row.metadata ? JSON.parse(row.metadata as string) : {}
    }));
    
    return { subscribers, count };
  }

  /**
   * Get subscriber count by status
   */
  async getSubscriberStats(): Promise<{
    total: number;
    subscribed: number;
    unsubscribed: number;
    pending: number;
  }> {
    const stmt = this.env.DB.prepare(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'subscribed' THEN 1 ELSE 0 END) as subscribed,
        SUM(CASE WHEN status = 'unsubscribed' THEN 1 ELSE 0 END) as unsubscribed,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
      FROM newsletter_subscribers
    `);
    
    const result = await stmt.first();
    
    return {
      total: result?.total || 0,
      subscribed: result?.subscribed || 0,
      unsubscribed: result?.unsubscribed || 0,
      pending: result?.pending || 0
    };
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get welcome email HTML template
   */
  private getWelcomeEmailTemplate(email: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Welcome to HandmadeIn.ro Newsletter</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2c5aa0;">Welcome to HandmadeIn.ro Newsletter!</h1>
          
          <p>Dear Subscriber,</p>
          
          <p>Thank you for subscribing to our newsletter! We're excited to share with you:</p>
          
          <ul>
            <li>New handmade products from Romanian artisans</li>
            <li>Artisan stories and craft insights</li>
            <li>Special offers and exclusive deals</li>
            <li>Behind-the-scenes content</li>
          </ul>
          
          <p>You'll receive our newsletter periodically with the latest updates from the world of Romanian craftsmanship.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${this.env.FRONTEND_URL}" style="background: #2c5aa0; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Visit HandmadeIn.ro
            </a>
          </div>
          
          <p>If you ever want to unsubscribe, you can do so by clicking the unsubscribe link in any of our emails.</p>
          
          <p>Happy crafting!</p>
          <p>The HandmadeIn.ro Team</p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="font-size: 12px; color: #666;">
            HandmadeIn.ro<br>
            This email was sent to ${email}
          </p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Get welcome email text template
   */
  private getWelcomeEmailText(email: string): string {
    return `
Welcome to HandmadeIn.ro Newsletter!

Dear Subscriber,

Thank you for subscribing to our newsletter! We're excited to share with you:

- New handmade products from Romanian artisans
- Artisan stories and craft insights
- Special offers and exclusive deals
- Behind-the-scenes content

You'll receive our newsletter periodically with the latest updates from the world of Romanian craftsmanship.

Visit us at: ${this.env.FRONTEND_URL}

If you ever want to unsubscribe, you can do so by clicking the unsubscribe link in any of our emails.

Happy crafting!
The HandmadeIn.ro Team

This email was sent to ${email}
    `;
  }
} 