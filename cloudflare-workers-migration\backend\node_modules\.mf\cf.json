{"clientTcpRtt": 62, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "EU", "asn": 8708, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "RO", "isEUCountry": "1", "region": "Cluj County", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "ql6/QWfQtgzfCH5q/ab01gJWYEmOc44PsPNeCk6IBrM=", "tlsExportedAuthenticator": {"clientFinished": "97eda016af4b74778e5cd1c9d9ce85b1c1ca9fbdff59fe760bf619a0651834fd9532174cb44081f027f4975a32215490", "clientHandshake": "b064be4b5a3a4e41675a5aa319c0d67d466d2663efa6a83d84527e4d124f366dc0a398d22a487564cb07649d7e5728b0", "serverHandshake": "3ee93e46f6cbe08530bc0f8644cd6faf8318033012121ddb3405a8335d1c319033f9eaeb2ce6e1682a0525c33ba5d59a", "serverFinished": "8105cab1c2b520a58c2ab2937d453a16f66591797286aca3386fff51f38fec98f32caca7c12c690c2bc836febbac367a"}, "tlsClientHelloLength": "386", "colo": "OTP", "timezone": "Europe/Bucharest", "longitude": "23.48950", "latitude": "46.74950", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "407280", "city": "Floreşti", "tlsVersion": "TLSv1.3", "regionCode": "CJ", "asOrganization": "Digi Romania", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}