import { WorkerEnv } from 'handmadein-shared';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  metadata?: Record<string, any>;
}

export class CacheService {
  private env: WorkerEnv;
  private defaultTTL: number;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.defaultTTL = 3600; // 1 hour default
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.env.CACHE.get(key, { type: 'json' });
      return value as T;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<boolean> {
    try {
      const ttl = options.ttl || this.defaultTTL;
      
      await this.env.CACHE.put(key, JSON.stringify(value), {
        expirationTtl: ttl,
        metadata: options.metadata,
      });
      
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      await this.env.CACHE.delete(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const value = await this.env.CACHE.get(key);
      return value !== null;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  async invalidatePattern(pattern: string): Promise<number> {
    try {
      const list = await this.env.CACHE.list({ prefix: pattern });
      let deleted = 0;
      
      for (const key of list.keys) {
        await this.env.CACHE.delete(key.name);
        deleted++;
      }
      
      return deleted;
    } catch (error) {
      console.error('Cache invalidate pattern error:', error);
      return 0;
    }
  }

  // Product-specific cache methods
  async getProduct(productId: string): Promise<any | null> {
    return this.get(`product:${productId}`);
  }

  async setProduct(productId: string, product: any, ttl: number = 1800): Promise<boolean> {
    return this.set(`product:${productId}`, product, { ttl });
  }

  async invalidateProduct(productId: string): Promise<boolean> {
    const deleted = await this.invalidatePattern(`product:${productId}`);
    // Also invalidate related caches
    await this.invalidatePattern('products:list');
    await this.invalidatePattern('search:');
    return deleted > 0;
  }

  // Collection-specific cache methods
  async getCollection(collectionId: string): Promise<any | null> {
    return this.get(`collection:${collectionId}`);
  }

  async setCollection(collectionId: string, collection: any, ttl: number = 3600): Promise<boolean> {
    return this.set(`collection:${collectionId}`, collection, { ttl });
  }

  async invalidateCollection(collectionId: string): Promise<boolean> {
    const deleted = await this.invalidatePattern(`collection:${collectionId}`);
    await this.invalidatePattern('collections:list');
    return deleted > 0;
  }

  // Search cache methods
  async getSearchResults(query: string, filters: Record<string, any> = {}): Promise<any | null> {
    const cacheKey = this.generateSearchKey(query, filters);
    return this.get(cacheKey);
  }

  async setSearchResults(
    query: string, 
    filters: Record<string, any> = {}, 
    results: any, 
    ttl: number = 900
  ): Promise<boolean> {
    const cacheKey = this.generateSearchKey(query, filters);
    return this.set(cacheKey, results, { ttl });
  }

  private generateSearchKey(query: string, filters: Record<string, any>): string {
    const filterString = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    
    return `search:${encodeURIComponent(query)}:${encodeURIComponent(filterString)}`;
  }

  // Cart cache methods
  async getCart(cartId: string): Promise<any | null> {
    return this.get(`cart:${cartId}`);
  }

  async setCart(cartId: string, cart: any, ttl: number = 1800): Promise<boolean> {
    return this.set(`cart:${cartId}`, cart, { ttl });
  }

  async invalidateCart(cartId: string): Promise<boolean> {
    return this.delete(`cart:${cartId}`);
  }

  // Customer cache methods
  async getCustomer(customerId: string): Promise<any | null> {
    return this.get(`customer:${customerId}`);
  }

  async setCustomer(customerId: string, customer: any, ttl: number = 3600): Promise<boolean> {
    return this.set(`customer:${customerId}`, customer, { ttl });
  }

  async invalidateCustomer(customerId: string): Promise<boolean> {
    return this.delete(`customer:${customerId}`);
  }

  // Order cache methods
  async getOrder(orderId: string): Promise<any | null> {
    return this.get(`order:${orderId}`);
  }

  async setOrder(orderId: string, order: any, ttl: number = 7200): Promise<boolean> {
    return this.set(`order:${orderId}`, order, { ttl });
  }

  async invalidateOrder(orderId: string): Promise<boolean> {
    return this.delete(`order:${orderId}`);
  }

  // Journal cache methods
  async getJournalEntry(entryId: string): Promise<any | null> {
    return this.get(`journal:${entryId}`);
  }

  async setJournalEntry(entryId: string, entry: any, ttl: number = 7200): Promise<boolean> {
    return this.set(`journal:${entryId}`, entry, { ttl });
  }

  async getJournalBySlug(slug: string): Promise<any | null> {
    return this.get(`journal:slug:${slug}`);
  }

  async setJournalBySlug(slug: string, entry: any, ttl: number = 7200): Promise<boolean> {
    return this.set(`journal:slug:${slug}`, entry, { ttl });
  }

  async invalidateJournal(entryId: string, slug?: string): Promise<boolean> {
    let deleted = await this.delete(`journal:${entryId}`);
    if (slug) {
      deleted = (await this.delete(`journal:slug:${slug}`)) || deleted;
    }
    await this.invalidatePattern('journal:list');
    return deleted;
  }

  // List cache methods
  async getProductsList(page: number, limit: number, filters: Record<string, any> = {}): Promise<any | null> {
    const filterString = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    
    return this.get(`products:list:${page}:${limit}:${encodeURIComponent(filterString)}`);
  }

  async setProductsList(
    page: number, 
    limit: number, 
    filters: Record<string, any> = {}, 
    products: any, 
    ttl: number = 1800
  ): Promise<boolean> {
    const filterString = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    
    return this.set(`products:list:${page}:${limit}:${encodeURIComponent(filterString)}`, products, { ttl });
  }

  // Dashboard stats cache
  async getDashboardStats(): Promise<any | null> {
    return this.get('dashboard:stats');
  }

  async setDashboardStats(stats: any, ttl: number = 300): Promise<boolean> {
    return this.set('dashboard:stats', stats, { ttl });
  }

  async invalidateDashboardStats(): Promise<boolean> {
    return this.delete('dashboard:stats');
  }

  // Utility methods
  async warmupCache(): Promise<void> {
    // Pre-populate frequently accessed data
    console.log('Starting cache warmup...');
    
    // This would typically fetch and cache:
    // - Popular products
    // - Featured collections
    // - Recent journal entries
    // - Dashboard stats
    
    console.log('Cache warmup completed');
  }

  async getCacheStats(): Promise<{ keys: number; size: string }> {
    try {
      const list = await this.env.CACHE.list();
      return {
        keys: list.keys.length,
        size: 'Unknown', // KV doesn't provide size info
      };
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return { keys: 0, size: 'Unknown' };
    }
  }

  async clearAllCache(): Promise<number> {
    try {
      const list = await this.env.CACHE.list();
      let deleted = 0;
      
      for (const key of list.keys) {
        await this.env.CACHE.delete(key.name);
        deleted++;
      }
      
      return deleted;
    } catch (error) {
      console.error('Failed to clear cache:', error);
      return 0;
    }
  }
} 