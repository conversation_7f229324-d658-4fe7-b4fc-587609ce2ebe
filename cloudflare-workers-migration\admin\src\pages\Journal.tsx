import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  BookOpenIcon,
  CalendarIcon,
  UserIcon,
  TagIcon,
  PhotoIcon,
  ChatBubbleLeftEllipsisIcon,
  ChartBarIcon,
  GlobeAltIcon,
  LinkIcon,
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface JournalEntry {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  author?: string;
  image_url?: string;
  published: boolean;
  published_at?: string;
  tags: string[];
  metadata?: any;
  word_count: number;
  reading_time: number;
  created_at: string;
  updated_at: string;
}

interface JournalFormData {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  author?: string;
  image_url?: string;
  published: boolean;
  published_at?: string;
  tags: string[];
  metadata?: any;
}

const Journal: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [publishedFilter, setPublishedFilter] = useState<string>('all');
  const [authorFilter, setAuthorFilter] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingEntry, setEditingEntry] = useState<JournalEntry | null>(null);
  const [selectedEntries, setSelectedEntries] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [limit] = useState(20);

  const queryClient = useQueryClient();

  // Fetch journal entries
  const { data: journalResponse, isLoading, error } = useQuery({
    queryKey: ['journal', { page, limit, search: searchTerm, published: publishedFilter, author: authorFilter }],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      
      if (searchTerm) params.append('search', searchTerm);
      if (publishedFilter !== 'all') params.append('published', publishedFilter);
      if (authorFilter) params.append('author', authorFilter);
      
      const response = await api.get(`${endpoints.journal.list}?${params}`);
      return response.data;
    },
  });

  const entries = journalResponse?.data || [];
  const pagination = journalResponse?.pagination || { page: 1, limit: 20, total: 0, pages: 0 };

  // Fetch analytics
  const { data: analyticsResponse } = useQuery({
    queryKey: ['journal-analytics'],
    queryFn: async () => {
      const response = await api.get(`${endpoints.journal.analytics}`);
      return response.data;
    },
  });

  const analytics = analyticsResponse?.analytics || {
    total_entries: 0,
    published_entries: 0,
    draft_entries: 0,
    monthly_stats: []
  };

  // Create entry mutation
  const createEntryMutation = useMutation({
    mutationFn: async (data: JournalFormData) => {
      const response = await api.post(endpoints.journal.create, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['journal'] });
      queryClient.invalidateQueries({ queryKey: ['journal-analytics'] });
      toast.success('Journal entry created successfully');
      setShowCreateModal(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create journal entry');
    },
  });

  // Update entry mutation
  const updateEntryMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<JournalFormData> }) => {
      const response = await api.put(endpoints.journal.update(id), data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['journal'] });
      queryClient.invalidateQueries({ queryKey: ['journal-analytics'] });
      toast.success('Journal entry updated successfully');
      setEditingEntry(null);
      setShowCreateModal(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update journal entry');
    },
  });

  // Delete entry mutation
  const deleteEntryMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await api.delete(endpoints.journal.delete(id));
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['journal'] });
      queryClient.invalidateQueries({ queryKey: ['journal-analytics'] });
      toast.success('Journal entry deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete journal entry');
    },
  });

  // Publish/unpublish mutation
  const publishMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await api.post(endpoints.journal.publish(id));
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['journal'] });
      queryClient.invalidateQueries({ queryKey: ['journal-analytics'] });
      toast.success(data.message);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update entry status');
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: async (entryIds: string[]) => {
      const response = await api.post(endpoints.journal.bulkDelete, { entryIds });
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['journal'] });
      queryClient.invalidateQueries({ queryKey: ['journal-analytics'] });
      toast.success(data.message);
      setSelectedEntries([]);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete entries');
    },
  });

  const handleEdit = (entry: JournalEntry) => {
    setEditingEntry(entry);
    setShowCreateModal(true);
  };

  const handleDelete = (entry: JournalEntry) => {
    if (confirm(`Are you sure you want to delete "${entry.title}"?`)) {
      deleteEntryMutation.mutate(entry.id);
    }
  };

  const handlePublishToggle = (entry: JournalEntry) => {
    publishMutation.mutate(entry.id);
  };

  const handleBulkDelete = () => {
    if (selectedEntries.length === 0) return;
    
    if (confirm(`Are you sure you want to delete ${selectedEntries.length} entry(ies)?`)) {
      bulkDeleteMutation.mutate(selectedEntries);
    }
  };

  const handleSelectAll = () => {
    if (selectedEntries.length === entries.length) {
      setSelectedEntries([]);
    } else {
      setSelectedEntries(entries.map((e: JournalEntry) => e.id));
    }
  };

  const handleSelectEntry = (id: string) => {
    setSelectedEntries(prev => 
      prev.includes(id) ? prev.filter(e => e !== id) : [...prev, id]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load journal entries. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
            <BookOpenIcon className="h-7 w-7 mr-2 text-blue-600" />
            Journal & Blog
          </h1>
          <p className="text-gray-600">Create and manage blog posts, articles, and journal entries</p>
        </div>
        <button
          onClick={() => {
            setEditingEntry(null);
            setShowCreateModal(true);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          New Entry
        </button>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="h-8 w-8 text-blue-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Total Entries</dt>
                <dd className="text-lg font-medium text-gray-900">{analytics.total_entries}</dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-8 w-8 text-green-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Published</dt>
                <dd className="text-lg font-medium text-gray-900">{analytics.published_entries}</dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClockIcon className="h-8 w-8 text-yellow-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Drafts</dt>
                <dd className="text-lg font-medium text-gray-900">{analytics.draft_entries}</dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-purple-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">This Month</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {analytics.monthly_stats?.[0]?.entries || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search entries by title, content, or author..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={publishedFilter}
            onChange={(e) => setPublishedFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Status</option>
            <option value="true">Published</option>
            <option value="false">Drafts</option>
          </select>
          
          <input
            type="text"
            placeholder="Filter by author"
            value={authorFilter}
            onChange={(e) => setAuthorFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />

          {selectedEntries.length > 0 && (
            <button
              onClick={handleBulkDelete}
              disabled={bulkDeleteMutation.isPending}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 flex items-center"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete Selected ({selectedEntries.length})
            </button>
          )}
        </div>
      </div>

      {/* Entries List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Journal Entries ({pagination.total})
            </h3>
            {entries.length > 0 && (
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedEntries.length === entries.length}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-600">Select all</span>
              </label>
            )}
          </div>
        </div>

        {entries.length === 0 ? (
          <div className="text-center py-12">
            <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No entries found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || publishedFilter !== 'all' || authorFilter 
                ? 'No entries match your search criteria.' 
                : 'Get started by creating your first journal entry.'}
            </p>
            {!searchTerm && publishedFilter === 'all' && !authorFilter && (
              <div className="mt-6">
                <button
                  onClick={() => {
                    setEditingEntry(null);
                    setShowCreateModal(true);
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center mx-auto"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Create First Entry
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {entries.map((entry: JournalEntry) => (
              <div key={entry.id} className="px-6 py-6 hover:bg-gray-50">
                <div className="flex items-start space-x-4">
                  <input
                    type="checkbox"
                    checked={selectedEntries.includes(entry.id)}
                    onChange={() => handleSelectEntry(entry.id)}
                    className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  
                  {entry.image_url && (
                    <div className="flex-shrink-0">
                      <img
                        src={entry.image_url}
                        alt={entry.title}
                        className="h-16 w-16 rounded-lg object-cover"
                      />
                    </div>
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-medium text-gray-900">
                            {entry.title}
                          </h4>
                          
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            entry.published 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {entry.published ? 'Published' : 'Draft'}
                          </span>
                          
                          {entry.published && (
                            <span className="inline-flex items-center text-xs text-gray-500">
                              <GlobeAltIcon className="h-3 w-3 mr-1" />
                              Live
                            </span>
                          )}
                        </div>
                        
                        {entry.excerpt && (
                          <p className="text-sm text-gray-600 mb-2">{entry.excerpt}</p>
                        )}
                        
                        {!entry.excerpt && entry.content && (
                          <p className="text-sm text-gray-600 mb-2">
                            {truncateContent(entry.content.replace(/<[^>]*>/g, ''))}
                          </p>
                        )}
                        
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          {entry.author && (
                            <div className="flex items-center">
                              <UserIcon className="h-4 w-4 mr-1" />
                              {entry.author}
                            </div>
                          )}
                          
                          <div className="flex items-center">
                            <ClockIcon className="h-4 w-4 mr-1" />
                            {entry.reading_time} min read
                          </div>
                          
                          <div className="flex items-center">
                            <ChatBubbleLeftEllipsisIcon className="h-4 w-4 mr-1" />
                            {entry.word_count} words
                          </div>
                          
                          {entry.tags.length > 0 && (
                            <div className="flex items-center">
                              <TagIcon className="h-4 w-4 mr-1" />
                              <div className="flex space-x-1">
                                {entry.tags.slice(0, 3).map((tag, index) => (
                                  <span
                                    key={index}
                                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                  >
                                    {tag}
                                  </span>
                                ))}
                                {entry.tags.length > 3 && (
                                  <span className="text-xs text-gray-400">
                                    +{entry.tags.length - 3} more
                                  </span>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                        
                        <div className="text-xs text-gray-500 mt-2">
                          <div className="flex items-center space-x-4">
                            <span>Created: {formatDate(entry.created_at)}</span>
                            {entry.updated_at !== entry.created_at && (
                              <span>Updated: {formatDate(entry.updated_at)}</span>
                            )}
                            {entry.published_at && (
                              <span>Published: {formatDate(entry.published_at)}</span>
                            )}
                          </div>
                          <div className="mt-1">
                            <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                              /{entry.slug}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handlePublishToggle(entry)}
                          disabled={publishMutation.isPending}
                          className={`p-2 rounded-md ${
                            entry.published
                              ? 'text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50'
                              : 'text-green-600 hover:text-green-800 hover:bg-green-50'
                          }`}
                          title={entry.published ? 'Unpublish' : 'Publish'}
                        >
                          {entry.published ? (
                            <XCircleIcon className="h-4 w-4" />
                          ) : (
                            <CheckCircleIcon className="h-4 w-4" />
                          )}
                        </button>
                        
                        <button
                          onClick={() => handleEdit(entry)}
                          className="text-blue-600 hover:text-blue-800 p-2 rounded-md hover:bg-blue-50"
                          title="Edit entry"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        
                        <button
                          onClick={() => handleDelete(entry)}
                          className="text-red-600 hover:text-red-800 p-2 rounded-md hover:bg-red-50"
                          title="Delete entry"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {(page - 1) * limit + 1} to {Math.min(page * limit, pagination.total)} of {pagination.total} entries
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                  disabled={page === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm text-gray-700">
                  Page {page} of {pagination.pages}
                </span>
                <button
                  onClick={() => setPage(prev => Math.min(prev + 1, pagination.pages))}
                  disabled={page === pagination.pages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <JournalModal
          isOpen={showCreateModal}
          onClose={() => {
            setShowCreateModal(false);
            setEditingEntry(null);
          }}
          entry={editingEntry}
          onSubmit={(data) => {
            if (editingEntry) {
              updateEntryMutation.mutate({ id: editingEntry.id, data });
            } else {
              createEntryMutation.mutate(data);
            }
          }}
          isSubmitting={createEntryMutation.isPending || updateEntryMutation.isPending}
        />
      )}
    </div>
  );
};

// Journal Modal Component
interface JournalModalProps {
  isOpen: boolean;
  onClose: () => void;
  entry?: JournalEntry | null;
  onSubmit: (data: JournalFormData) => void;
  isSubmitting: boolean;
}

const JournalModal: React.FC<JournalModalProps> = ({
  isOpen,
  onClose,
  entry,
  onSubmit,
  isSubmitting
}) => {
  const [formData, setFormData] = useState<JournalFormData>({
    title: entry?.title || '',
    content: entry?.content || '',
    excerpt: entry?.excerpt || '',
    slug: entry?.slug || '',
    author: entry?.author || '',
    image_url: entry?.image_url || '',
    published: entry?.published || false,
    published_at: entry?.published_at || '',
    tags: entry?.tags || [],
  });

  const [newTag, setNewTag] = useState('');

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast.error('Title is required');
      return;
    }

    if (!formData.content.trim()) {
      toast.error('Content is required');
      return;
    }
    
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h3 className="text-lg font-medium mb-6">
            {entry ? 'Edit Journal Entry' : 'Create New Journal Entry'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Title and Slug */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter entry title"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  URL Slug
                </label>
                <input
                  type="text"
                  value={formData.slug}
                  onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="url-friendly-slug"
                />
              </div>
            </div>

            {/* Author and Image */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Author
                </label>
                <input
                  type="text"
                  value={formData.author}
                  onChange={(e) => setFormData({ ...formData, author: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Author name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Featured Image URL
                </label>
                <input
                  type="url"
                  value={formData.image_url}
                  onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://example.com/image.jpg"
                />
              </div>
            </div>

            {/* Excerpt */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Excerpt
              </label>
              <textarea
                value={formData.excerpt}
                onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Brief description or summary (optional)"
              />
            </div>

            {/* Content */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Content *
              </label>
              <textarea
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                rows={12}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Write your content here... (supports HTML)"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                You can use HTML tags for formatting. Word count: {formData.content.split(/\s+/).filter(word => word.length > 0).length}
              </p>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tags
              </label>
              <div className="flex items-center space-x-2 mb-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Add tag and press Enter"
                />
                <button
                  type="button"
                  onClick={addTag}
                  className="px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
                >
                  Add
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>

            {/* Publishing Options */}
            <div className="flex items-center space-x-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.published}
                  onChange={(e) => setFormData({ ...formData, published: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">
                  Publish immediately
                </span>
              </label>

              {formData.published && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Publish Date
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.published_at}
                    onChange={(e) => setFormData({ ...formData, published_at: e.target.value })}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              )}
            </div>

            {/* Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Saving...' : entry ? 'Update Entry' : 'Create Entry'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Journal; 