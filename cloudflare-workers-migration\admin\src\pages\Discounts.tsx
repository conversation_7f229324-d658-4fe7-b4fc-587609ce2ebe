import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  TagIcon,
  SparklesIcon,
  CurrencyDollarIcon,
  GiftIcon,
  ShoppingBagIcon,
  ArrowTrendingUpIcon,
  CalendarDaysIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface Promotion {
  id: string;
  code: string;
  type: string;
  status: 'active' | 'inactive' | 'scheduled' | 'expired';
  is_automatic: boolean;
  campaign_name?: string;
  campaign_description?: string;
  campaign_starts_at?: string;
  campaign_ends_at?: string;
  application_type?: string;
  discount_value?: number;
  currency_code?: string;
  target_type?: string;
  allocation?: string;
  rules_count?: number;
  created_at: string;
  updated_at: string;
}

interface PromotionFormData {
  code: string;
  type: 'percentage' | 'fixed' | 'free_shipping' | 'buy_x_get_y';
  status: 'active' | 'inactive' | 'scheduled';
  is_automatic: boolean;
  campaign_id?: string;
  application_method: {
    type: 'fixed' | 'percentage';
    target_type: 'order' | 'items' | 'shipping';
    value?: number;
    currency_code?: string;
    allocation?: 'across' | 'each';
    max_quantity?: number;
    apply_to_quantity?: number;
    buy_rules_min_quantity?: number;
  };
  rules: Array<{
    attribute: string;
    operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'not_in';
    values: string[];
    description?: string;
  }>;
}

const Promotions: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingPromotion, setEditingPromotion] = useState<Promotion | null>(null);
  const [selectedPromotions, setSelectedPromotions] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [limit] = useState(20);

  const queryClient = useQueryClient();

  // Fetch promotions
  const { data: promotionsResponse, isLoading, error } = useQuery({
    queryKey: ['promotions', { page, limit, search: searchTerm, status: statusFilter, type: typeFilter }],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (typeFilter !== 'all') params.append('type', typeFilter);
      
      const response = await api.get(`${endpoints.promotions.list}?${params}`);
      return response.data;
    },
  });

  const promotions = promotionsResponse?.data || [];
  const pagination = promotionsResponse?.pagination || { page: 1, limit: 20, total: 0, pages: 0 };

  // Create promotion mutation
  const createPromotionMutation = useMutation({
    mutationFn: async (data: PromotionFormData) => {
      const response = await api.post(endpoints.promotions.create, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast.success('Promotion created successfully');
      setShowCreateModal(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create promotion');
    },
  });

  // Update promotion mutation
  const updatePromotionMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<PromotionFormData> }) => {
      const response = await api.put(endpoints.promotions.update(id), data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast.success('Promotion updated successfully');
      setEditingPromotion(null);
      setShowCreateModal(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update promotion');
    },
  });

  // Delete promotion mutation
  const deletePromotionMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await api.delete(endpoints.promotions.delete(id));
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast.success('Promotion deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete promotion');
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: async (promotionIds: string[]) => {
      const response = await api.post(endpoints.promotions.bulkDelete, { promotionIds });
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast.success(data.message);
      setSelectedPromotions([]);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete promotions');
    },
  });

  const handleEdit = (promotion: Promotion) => {
    setEditingPromotion(promotion);
    setShowCreateModal(true);
  };

  const handleDelete = (promotion: Promotion) => {
    if (confirm(`Are you sure you want to delete promotion "${promotion.code}"?`)) {
      deletePromotionMutation.mutate(promotion.id);
    }
  };

  const handleBulkDelete = () => {
    if (selectedPromotions.length === 0) return;
    
    if (confirm(`Are you sure you want to delete ${selectedPromotions.length} promotion(s)?`)) {
      bulkDeleteMutation.mutate(selectedPromotions);
    }
  };

  const handleSelectAll = () => {
    if (selectedPromotions.length === promotions.length) {
      setSelectedPromotions([]);
    } else {
      setSelectedPromotions(promotions.map((p: Promotion) => p.id));
    }
  };

  const handleSelectPromotion = (id: string) => {
    setSelectedPromotions(prev => 
      prev.includes(id) ? prev.filter(p => p !== id) : [...prev, id]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'percentage':
        return '%';
      case 'fixed':
        return <CurrencyDollarIcon className="h-4 w-4" />;
      case 'free_shipping':
        return <GiftIcon className="h-4 w-4" />;
      case 'buy_x_get_y':
        return <ShoppingBagIcon className="h-4 w-4" />;
      default:
        return <TagIcon className="h-4 w-4" />;
    }
  };

  const formatDiscountValue = (promotion: Promotion) => {
    if (!promotion.discount_value) return 'N/A';
    
    if (promotion.application_type === 'percentage') {
      return `${promotion.discount_value}%`;
    } else if (promotion.application_type === 'fixed') {
      return `${promotion.discount_value} ${promotion.currency_code || 'RON'}`;
    }
    
    return promotion.discount_value.toString();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load promotions. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
            <SparklesIcon className="h-7 w-7 mr-2 text-purple-600" />
            Promotions & Discounts
          </h1>
          <p className="text-gray-600">Create and manage discount codes, campaigns, and promotional offers</p>
        </div>
        <button
          onClick={() => {
            setEditingPromotion(null);
            setShowCreateModal(true);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Create Promotion
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-8 w-8 text-green-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Active Promotions</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {promotions.filter((p: Promotion) => p.status === 'active').length}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClockIcon className="h-8 w-8 text-blue-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Scheduled</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {promotions.filter((p: Promotion) => p.status === 'scheduled').length}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-8 w-8 text-red-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Expired</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {promotions.filter((p: Promotion) => p.status === 'expired').length}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ArrowTrendingUpIcon className="h-8 w-8 text-purple-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Total Promotions</dt>
                <dd className="text-lg font-medium text-gray-900">{pagination.total}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search promotions by code or campaign name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="scheduled">Scheduled</option>
            <option value="expired">Expired</option>
          </select>
          
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Types</option>
            <option value="percentage">Percentage</option>
            <option value="fixed">Fixed Amount</option>
            <option value="free_shipping">Free Shipping</option>
            <option value="buy_x_get_y">Buy X Get Y</option>
          </select>

          {selectedPromotions.length > 0 && (
            <button
              onClick={handleBulkDelete}
              disabled={bulkDeleteMutation.isPending}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 flex items-center"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete Selected ({selectedPromotions.length})
            </button>
          )}
        </div>
      </div>

      {/* Promotions Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Promotions ({pagination.total})
            </h3>
            {promotions.length > 0 && (
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedPromotions.length === promotions.length}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-600">Select all</span>
              </label>
            )}
          </div>
        </div>

        {promotions.length === 0 ? (
          <div className="text-center py-12">
            <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No promotions found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || statusFilter !== 'all' || typeFilter !== 'all' 
                ? 'No promotions match your search criteria.' 
                : 'Get started by creating your first promotion.'}
            </p>
            {!searchTerm && statusFilter === 'all' && typeFilter === 'all' && (
              <div className="mt-6">
                <button
                  onClick={() => {
                    setEditingPromotion(null);
                    setShowCreateModal(true);
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center mx-auto"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Create First Promotion
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {promotions.map((promotion: Promotion) => (
              <div key={promotion.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-start space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedPromotions.includes(promotion.id)}
                      onChange={() => handleSelectPromotion(promotion.id)}
                      className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(promotion.type)}
                          <h4 className="font-medium text-gray-900 text-lg">
                            {promotion.code}
                          </h4>
                        </div>
                        
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(promotion.status)}`}>
                          {promotion.status}
                        </span>
                        
                        {promotion.is_automatic && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            Automatic
                          </span>
                        )}
                      </div>
                      
                      {promotion.campaign_name && (
                        <p className="text-sm text-gray-600 mb-1">
                          <CalendarDaysIcon className="h-4 w-4 inline mr-1" />
                          Campaign: {promotion.campaign_name}
                        </p>
                      )}
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Type:</span>
                          <span className="font-medium text-gray-900 ml-2 capitalize">
                            {promotion.type?.replace('_', ' ')}
                          </span>
                        </div>
                        
                        <div>
                          <span className="text-gray-500">Discount:</span>
                          <span className="font-medium text-gray-900 ml-2">
                            {formatDiscountValue(promotion)}
                          </span>
                        </div>
                        
                        <div>
                          <span className="text-gray-500">Target:</span>
                          <span className="font-medium text-gray-900 ml-2 capitalize">
                            {promotion.target_type || 'N/A'}
                          </span>
                        </div>
                        
                        <div>
                          <span className="text-gray-500">Rules:</span>
                          <span className="font-medium text-gray-900 ml-2">
                            {promotion.rules_count || 0}
                          </span>
                        </div>
                      </div>
                      
                      <div className="text-xs text-gray-500 mt-2">
                        Created: {formatDate(promotion.created_at)}
                        {promotion.updated_at !== promotion.created_at && (
                          <span> • Updated: {formatDate(promotion.updated_at)}</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEdit(promotion)}
                      className="text-blue-600 hover:text-blue-800 p-2 rounded-md hover:bg-blue-50"
                      title="Edit promotion"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => handleDelete(promotion)}
                      className="text-red-600 hover:text-red-800 p-2 rounded-md hover:bg-red-50"
                      title="Delete promotion"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {(page - 1) * limit + 1} to {Math.min(page * limit, pagination.total)} of {pagination.total} promotions
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                  disabled={page === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm text-gray-700">
                  Page {page} of {pagination.pages}
                </span>
                <button
                  onClick={() => setPage(prev => Math.min(prev + 1, pagination.pages))}
                  disabled={page === pagination.pages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <PromotionModal
          isOpen={showCreateModal}
          onClose={() => {
            setShowCreateModal(false);
            setEditingPromotion(null);
          }}
          promotion={editingPromotion}
          onSubmit={(data) => {
            if (editingPromotion) {
              updatePromotionMutation.mutate({ id: editingPromotion.id, data });
            } else {
              createPromotionMutation.mutate(data);
            }
          }}
          isSubmitting={createPromotionMutation.isPending || updatePromotionMutation.isPending}
        />
      )}
    </div>
  );
};

// Promotion Modal Component
interface PromotionModalProps {
  isOpen: boolean;
  onClose: () => void;
  promotion?: Promotion | null;
  onSubmit: (data: PromotionFormData) => void;
  isSubmitting: boolean;
}

const PromotionModal: React.FC<PromotionModalProps> = ({
  isOpen,
  onClose,
  promotion,
  onSubmit,
  isSubmitting
}) => {
  const [formData, setFormData] = useState<PromotionFormData>({
    code: promotion?.code || '',
    type: (promotion?.type as any) || 'percentage',
    status: (promotion?.status as any) || 'inactive',
    is_automatic: promotion?.is_automatic || false,
    application_method: {
      type: (promotion?.application_type as any) || 'percentage',
      target_type: (promotion?.target_type as any) || 'order',
      value: promotion?.discount_value || 0,
      currency_code: promotion?.currency_code || 'RON',
      allocation: (promotion?.allocation as any) || 'across',
    },
    rules: [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.code.trim()) {
      toast.error('Promotion code is required');
      return;
    }
    
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">
            {promotion ? 'Edit Promotion' : 'Create New Promotion'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Promotion Code *
                </label>
                <input
                  type="text"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., SAVE20, WELCOME10"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="inactive">Inactive</option>
                  <option value="active">Active</option>
                  <option value="scheduled">Scheduled</option>
                </select>
              </div>
            </div>

            {/* Discount Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Discount Type
                </label>
                <select
                  value={formData.application_method.type}
                  onChange={(e) => setFormData({
                    ...formData,
                    application_method: {
                      ...formData.application_method,
                      type: e.target.value as any
                    }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="percentage">Percentage</option>
                  <option value="fixed">Fixed Amount</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Discount Value
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.application_method.value || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    application_method: {
                      ...formData.application_method,
                      value: parseFloat(e.target.value) || 0
                    }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={formData.application_method.type === 'percentage' ? '20' : '100'}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Target
                </label>
                <select
                  value={formData.application_method.target_type}
                  onChange={(e) => setFormData({
                    ...formData,
                    application_method: {
                      ...formData.application_method,
                      target_type: e.target.value as any
                    }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="order">Entire Order</option>
                  <option value="items">Specific Items</option>
                  <option value="shipping">Shipping</option>
                </select>
              </div>
            </div>

            {/* Options */}
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_automatic}
                  onChange={(e) => setFormData({ ...formData, is_automatic: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">
                  Automatic (apply without code)
                </span>
              </label>
            </div>

            {/* Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Saving...' : promotion ? 'Update Promotion' : 'Create Promotion'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Promotions; 