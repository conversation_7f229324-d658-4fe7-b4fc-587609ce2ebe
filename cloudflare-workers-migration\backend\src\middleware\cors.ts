import { Context, Next } from 'hono';

export interface CorsOptions {
  origin?: string | string[] | ((origin: string) => boolean);
  methods?: string[];
  allowedHeaders?: string[];
  exposedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
}

export function cors(options: CorsOptions = {}) {
  const {
    origin = '*',
    methods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders = ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders = [],
    credentials = false,
    maxAge = 86400, // 24 hours
  } = options;

  return async (c: Context, next: Next) => {
    const requestOrigin = c.req.header('Origin');
    
    // Handle preflight requests
    if (c.req.method === 'OPTIONS') {
      const headers = new Headers();
      
      // Set allowed origin
      if (typeof origin === 'string') {
        headers.set('Access-Control-Allow-Origin', origin);
      } else if (Array.isArray(origin)) {
        if (requestOrigin && origin.includes(requestOrigin)) {
          headers.set('Access-Control-Allow-Origin', requestOrigin);
        }
      } else if (typeof origin === 'function') {
        if (requestOrigin && origin(requestOrigin)) {
          headers.set('Access-Control-Allow-Origin', requestOrigin);
        }
      }
      
      // Set other CORS headers
      headers.set('Access-Control-Allow-Methods', methods.join(', '));
      headers.set('Access-Control-Allow-Headers', allowedHeaders.join(', '));
      
      if (exposedHeaders.length > 0) {
        headers.set('Access-Control-Expose-Headers', exposedHeaders.join(', '));
      }
      
      if (credentials) {
        headers.set('Access-Control-Allow-Credentials', 'true');
      }
      
      headers.set('Access-Control-Max-Age', maxAge.toString());
      
      return new Response(null, {
        status: 204,
        headers,
      });
    }
    
    // Handle actual requests
    await next();
    
    // Set CORS headers on response
    if (typeof origin === 'string') {
      c.res.headers.set('Access-Control-Allow-Origin', origin);
    } else if (Array.isArray(origin)) {
      if (requestOrigin && origin.includes(requestOrigin)) {
        c.res.headers.set('Access-Control-Allow-Origin', requestOrigin);
      }
    } else if (typeof origin === 'function') {
      if (requestOrigin && origin(requestOrigin)) {
        c.res.headers.set('Access-Control-Allow-Origin', requestOrigin);
      }
    }
    
    if (credentials) {
      c.res.headers.set('Access-Control-Allow-Credentials', 'true');
    }
    
    if (exposedHeaders.length > 0) {
      c.res.headers.set('Access-Control-Expose-Headers', exposedHeaders.join(', '));
    }
  };
}

// Predefined CORS configurations
export const corsConfig = {
  // Development - allow all origins
  development: cors({
    origin: '*',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-API-Key'],
  }),
  
  // Production - restrict to specific domains
  production: cors({
    origin: [
      'https://handmadein.ro',
      'https://www.handmadein.ro',
      'https://admin.handmadein.ro',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-API-Key'],
    exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  }),
  
  // API only - for external integrations
  api: cors({
    origin: (origin: string) => {
      // Allow specific partner domains or API consumers
      const allowedDomains = [
        'handmadein.ro',
        'admin.handmadein.ro',
        // Add partner domains here
      ];
      
      return allowedDomains.some(domain => origin.endsWith(domain));
    },
    credentials: false,
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'X-API-Key'],
  }),
}; 