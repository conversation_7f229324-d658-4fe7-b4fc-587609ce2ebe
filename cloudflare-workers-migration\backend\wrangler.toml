name = "handmadein-backend"
main = "src/index.ts"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]

[vars]
ENVIRONMENT = "development"
FRONTEND_URL = "http://localhost:3000"
ADMIN_URL = "http://localhost:3001"
JWT_SECRET = "dev-jwt-secret-change-in-production-please-make-this-very-long-and-secure-2024"
STRIPE_SECRET_KEY = "sk_test_dev_key_placeholder"
STRIPE_WEBHOOK_SECRET = "whsec_dev_webhook_placeholder"
RESEND_API_KEY = "re_dev_api_key_placeholder"
TYPESENSE_API_KEY = "dev_typesense_key_placeholder"
TRUSTED_SHOP_API_KEY = "dev_trusted_shop_key_placeholder"

[[d1_databases]]
binding = "DB"
database_name = "ecommerce-v2"
database_id = "14bc2a37-ceea-4317-881d-48edf1a61de5"
migrations_dir = "database/migrations"

[[d1_databases]]
binding = "SIMPLIFIED_DB"
database_name = "simplified-ecommerce-db"
database_id = "0a1740de-7a29-4091-9a23-e6323a31070c"

[[kv_namespaces]]
binding = "CACHE"
id = "daefe275557e44dfab942293b0553a28"
preview_id = "d94bbd954d864b519cb0efc345c50f13"

[[kv_namespaces]]
binding = "SESSIONS"
id = "ad71f88f9dc74a8ca384258b06c29f6f"
preview_id = "a90252a220f04be188d30de4048dca6a"

[[r2_buckets]]
binding = "ASSETS"
bucket_name = "handmadein-assets"

[[r2_buckets]]
binding = "UPLOADS"
bucket_name = "handmadein-uploads"

[env.staging]
name = "handmadein-backend-staging"
vars = { ENVIRONMENT = "staging", FRONTEND_URL = "https://staging.handmadein.ro", ADMIN_URL = "https://admin-staging.handmadein.ro" }

[[env.staging.d1_databases]]
binding = "DB"
database_name = "handmadein-db-staging"
database_id = "your-staging-d1-database-id"

[[env.staging.kv_namespaces]]
binding = "CACHE"
id = "your-staging-cache-kv-id"

[[env.staging.kv_namespaces]]
binding = "SESSIONS"
id = "your-staging-sessions-kv-id"

[[env.staging.r2_buckets]]
binding = "ASSETS"
bucket_name = "handmadein-assets-staging"

[[env.staging.r2_buckets]]
binding = "UPLOADS"
bucket_name = "handmadein-uploads-staging"

[env.production]
name = "handmadein-backend"
vars = { ENVIRONMENT = "production", FRONTEND_URL = "https://handmadein.ro", ADMIN_URL = "https://admin.handmadein.ro" }

[[env.production.d1_databases]]
binding = "DB"
database_name = "handmadein-db-production"
database_id = "your-production-d1-database-id"

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "your-production-cache-kv-id"

[[env.production.kv_namespaces]]
binding = "SESSIONS"
id = "your-production-sessions-kv-id"

[[env.production.r2_buckets]]
binding = "ASSETS"
bucket_name = "handmadein-assets"

[[env.production.r2_buckets]]
binding = "UPLOADS"
bucket_name = "handmadein-uploads" 
