import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  CubeIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  BuildingStorefrontIcon,
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface InventoryLevel {
  id: string;
  inventory_item_id: string;
  location_id: string;
  stocked_quantity: number;
  reserved_quantity: number;
  incoming_quantity: number;
  available_quantity: number;
  metadata?: any;
  item?: {
    id: string;
    sku: string;
    title: string;
    thumbnail?: string;
  };
  location?: {
    id: string;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

interface StockAdjustment {
  inventory_level_id: string;
  adjustment_type: 'increase' | 'decrease' | 'set';
  quantity: number;
  reason: string;
  reference?: string;
}

const InventoryLevels: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState('all');
  const [lowStockFilter, setLowStockFilter] = useState(false);
  const [isAdjustmentModalOpen, setIsAdjustmentModalOpen] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<InventoryLevel | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [adjustmentData, setAdjustmentData] = useState<StockAdjustment>({
    inventory_level_id: '',
    adjustment_type: 'increase',
    quantity: 0,
    reason: '',
    reference: '',
  });

  const queryClient = useQueryClient();

  // Fetch inventory levels
  const { data: levelsData, isLoading, error } = useQuery({
    queryKey: ['inventory-levels', currentPage, searchTerm, locationFilter, lowStockFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
      });
      
      if (searchTerm) params.append('search', searchTerm);
      if (locationFilter !== 'all') params.append('location_id', locationFilter);
      if (lowStockFilter) params.append('low_stock', 'true');

      const response = await api.get(`${endpoints.inventory.levels}?${params}`);
      return response.data;
    },
  });

  // Fetch locations for filter
  const { data: locationsData } = useQuery({
    queryKey: ['stock-locations-filter'],
    queryFn: async () => {
      const response = await api.get(`${endpoints.inventory.locations}?limit=100`);
      return response.data;
    },
  });

  // Create stock adjustment mutation
  const createAdjustmentMutation = useMutation({
    mutationFn: async (data: StockAdjustment) => {
      return api.post(endpoints.inventory.createAdjustment, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory-levels'] });
      toast.success('Stock adjustment created successfully');
      setIsAdjustmentModalOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create stock adjustment');
    },
  });

  const resetForm = () => {
    setAdjustmentData({
      inventory_level_id: '',
      adjustment_type: 'increase',
      quantity: 0,
      reason: '',
      reference: '',
    });
  };

  const handleCreateAdjustment = (e: React.FormEvent) => {
    e.preventDefault();
    if (!adjustmentData.quantity || adjustmentData.quantity <= 0) {
      toast.error('Quantity must be greater than 0');
      return;
    }
    if (!adjustmentData.reason.trim()) {
      toast.error('Reason is required');
      return;
    }
    createAdjustmentMutation.mutate(adjustmentData);
  };

  const handleAdjustStock = (level: InventoryLevel, type: 'increase' | 'decrease') => {
    setSelectedLevel(level);
    setAdjustmentData({
      inventory_level_id: level.id,
      adjustment_type: type,
      quantity: 1,
      reason: type === 'increase' ? 'Stock replenishment' : 'Stock correction',
      reference: '',
    });
    setIsAdjustmentModalOpen(true);
  };

  const getStockStatus = (level: InventoryLevel) => {
    const available = level.available_quantity || 0;
    if (available === 0) return { status: 'out-of-stock', color: 'text-red-600', bg: 'bg-red-100' };
    if (available < 10) return { status: 'low-stock', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    return { status: 'in-stock', color: 'text-green-600', bg: 'bg-green-100' };
  };

  const levels = levelsData?.levels || [];
  const locations = locationsData?.locations || [];
  const totalPages = Math.ceil((levelsData?.total || 0) / 20);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load inventory levels. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Inventory Levels</h1>
          <p className="text-gray-600">Monitor and adjust stock levels across all locations</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search by SKU or item title..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <select
          value={locationFilter}
          onChange={(e) => setLocationFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Locations</option>
          {locations.map((location: any) => (
            <option key={location.id} value={location.id}>{location.name}</option>
          ))}
        </select>
        
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={lowStockFilter}
            onChange={(e) => setLowStockFilter(e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700">Low Stock Only</span>
        </label>
      </div>

      {/* Inventory Levels List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Inventory Levels ({levelsData?.total || 0})
          </h3>
        </div>

        {levels.length === 0 ? (
          <div className="text-center py-12">
            <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No inventory levels found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || locationFilter !== 'all' ? 'No levels match your search criteria.' : 'No inventory levels have been set up yet.'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {levels.map((level: InventoryLevel) => {
              const stockStatus = getStockStatus(level);
              
              return (
                <div key={level.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-start space-x-4">
                      {level.item?.thumbnail ? (
                        <img
                          src={level.item.thumbnail}
                          alt={level.item.title}
                          className="w-12 h-12 object-cover rounded-md"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                          <CubeIcon className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="font-medium text-gray-900">{level.item?.title}</h4>
                          <span className="text-sm text-gray-500">SKU: {level.item?.sku}</span>
                        </div>
                        
                        <div className="flex items-center space-x-2 mt-1">
                          <BuildingStorefrontIcon className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-500">{level.location?.name}</span>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3 text-sm">
                          <div>
                            <span className="text-gray-500">Available:</span>
                            <div className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ml-2 ${stockStatus.bg} ${stockStatus.color}`}>
                              {stockStatus.status === 'out-of-stock' && <ExclamationTriangleIcon className="h-3 w-3 mr-1" />}
                              {stockStatus.status === 'low-stock' && <ClockIcon className="h-3 w-3 mr-1" />}
                              {stockStatus.status === 'in-stock' && <CheckCircleIcon className="h-3 w-3 mr-1" />}
                              {level.available_quantity}
                            </div>
                          </div>
                          
                          <div>
                            <span className="text-gray-500">Stocked:</span>
                            <span className="font-medium text-gray-900 ml-2">{level.stocked_quantity}</span>
                          </div>
                          
                          <div>
                            <span className="text-gray-500">Reserved:</span>
                            <span className="font-medium text-gray-900 ml-2">{level.reserved_quantity || 0}</span>
                          </div>
                          
                          <div>
                            <span className="text-gray-500">Incoming:</span>
                            <span className="font-medium text-gray-900 ml-2">{level.incoming_quantity || 0}</span>
                          </div>
                        </div>
                        
                        <div className="text-xs text-gray-400 mt-2">
                          Last updated {new Date(level.updated_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleAdjustStock(level, 'increase')}
                        className="flex items-center text-green-600 hover:text-green-800 text-sm bg-green-50 px-3 py-1 rounded-md"
                        title="Increase stock"
                      >
                        <ArrowUpIcon className="h-4 w-4 mr-1" />
                        Add
                      </button>
                      <button
                        onClick={() => handleAdjustStock(level, 'decrease')}
                        className="flex items-center text-red-600 hover:text-red-800 text-sm bg-red-50 px-3 py-1 rounded-md"
                        title="Decrease stock"
                      >
                        <ArrowDownIcon className="h-4 w-4 mr-1" />
                        Remove
                      </button>
                      <button
                        onClick={() => {
                          setSelectedLevel(level);
                          setAdjustmentData({
                            inventory_level_id: level.id,
                            adjustment_type: 'set',
                            quantity: level.stocked_quantity,
                            reason: 'Stock count correction',
                            reference: '',
                          });
                          setIsAdjustmentModalOpen(true);
                        }}
                        className="text-gray-400 hover:text-gray-600"
                        title="Set exact stock level"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {(currentPage - 1) * 20 + 1} to {Math.min(currentPage * 20, levelsData?.total || 0)} of {levelsData?.total || 0} levels
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Stock Adjustment Modal */}
      {isAdjustmentModalOpen && selectedLevel && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Adjust Stock Level
            </h3>
            
            <div className="mb-4 p-4 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-3">
                {selectedLevel.item?.thumbnail ? (
                  <img
                    src={selectedLevel.item.thumbnail}
                    alt={selectedLevel.item.title}
                    className="w-10 h-10 object-cover rounded-md"
                  />
                ) : (
                  <div className="w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center">
                    <CubeIcon className="h-5 w-5 text-gray-400" />
                  </div>
                )}
                <div>
                  <div className="font-medium text-gray-900">{selectedLevel.item?.title}</div>
                  <div className="text-sm text-gray-500">
                    {selectedLevel.item?.sku} • {selectedLevel.location?.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    Current stock: {selectedLevel.stocked_quantity}
                  </div>
                </div>
              </div>
            </div>
            
            <form onSubmit={handleCreateAdjustment}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Adjustment Type
                  </label>
                  <select
                    value={adjustmentData.adjustment_type}
                    onChange={(e) => setAdjustmentData({ 
                      ...adjustmentData, 
                      adjustment_type: e.target.value as 'increase' | 'decrease' | 'set'
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="increase">Increase Stock</option>
                    <option value="decrease">Decrease Stock</option>
                    <option value="set">Set Exact Amount</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {adjustmentData.adjustment_type === 'set' ? 'New Stock Level' : 'Quantity'}
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="1"
                    value={adjustmentData.quantity}
                    onChange={(e) => setAdjustmentData({ 
                      ...adjustmentData, 
                      quantity: parseInt(e.target.value) || 0
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter quantity"
                    required
                  />
                  {adjustmentData.adjustment_type !== 'set' && (
                    <p className="text-xs text-gray-500 mt-1">
                      New level will be: {
                        adjustmentData.adjustment_type === 'increase' 
                          ? selectedLevel.stocked_quantity + adjustmentData.quantity
                          : Math.max(0, selectedLevel.stocked_quantity - adjustmentData.quantity)
                      }
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reason *
                  </label>
                  <select
                    value={adjustmentData.reason}
                    onChange={(e) => setAdjustmentData({ ...adjustmentData, reason: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">Select a reason</option>
                    <option value="Stock replenishment">Stock replenishment</option>
                    <option value="Stock correction">Stock correction</option>
                    <option value="Physical count">Physical count</option>
                    <option value="Damaged goods">Damaged goods</option>
                    <option value="Theft/Loss">Theft/Loss</option>
                    <option value="Return to supplier">Return to supplier</option>
                    <option value="Transfer">Transfer</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                {adjustmentData.reason === 'Other' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Custom Reason
                    </label>
                    <input
                      type="text"
                      value={adjustmentData.reference || ''}
                      onChange={(e) => setAdjustmentData({ ...adjustmentData, reference: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Describe the reason"
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reference (Optional)
                  </label>
                  <input
                    type="text"
                    value={adjustmentData.reference || ''}
                    onChange={(e) => setAdjustmentData({ ...adjustmentData, reference: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="PO number, invoice, etc."
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setIsAdjustmentModalOpen(false);
                    setSelectedLevel(null);
                    resetForm();
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createAdjustmentMutation.isPending}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {createAdjustmentMutation.isPending ? 'Adjusting...' : 'Apply Adjustment'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryLevels; 