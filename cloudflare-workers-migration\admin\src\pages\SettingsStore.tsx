import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { BuildingStorefrontIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';
import { api } from '../lib/api';

interface StoreData {
  id: string;
  name: string;
  default_sales_channel_id?: string;
  default_region_id?: string;
  default_location_id?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

const SettingsStore: React.FC = () => {
  const [formData, setFormData] = useState<Partial<StoreData>>({});
  const queryClient = useQueryClient();

  // Fetch store settings
  const { data: store, isLoading } = useQuery({
    queryKey: ['store-settings'],
    queryFn: async () => {
      const response = await api.get('/admin/api/settings/store');
      return response.data.data;
    },
  });

  // Fetch related data for dropdowns
  const { data: regionsData } = useQuery({
    queryKey: ['regions'],
    queryFn: async () => {
      const response = await api.get('/admin/api/regions');
      return response.data.data?.regions || [];
    },
  });

  const { data: salesChannelsData } = useQuery({
    queryKey: ['sales-channels'],
    queryFn: async () => {
      const response = await api.get('/admin/api/sales-channels');
      return response.data.data || [];
    },
  });

  const { data: locationsData } = useQuery({
    queryKey: ['stock-locations'],
    queryFn: async () => {
      const response = await api.get('/admin/api/stock-locations');
      return response.data.data || [];
    },
  });

  // Update store mutation
  const updateStoreMutation = useMutation({
    mutationFn: (updatedData: Partial<StoreData>) =>
      api.put('/admin/api/settings/store', updatedData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['store-settings'] });
      queryClient.invalidateQueries({ queryKey: ['settings-overview'] });
      toast.success('Store settings updated successfully');
    },
    onError: () => {
      toast.error('Failed to update store settings');
    },
  });

  React.useEffect(() => {
    if (store) {
      setFormData(store);
    }
  }, [store]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleMetadataChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        [key]: value,
      },
    }));
  };

  const handleSave = () => {
    updateStoreMutation.mutate(formData);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          to="/settings"
          className="flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-1" />
          Back to Settings
        </Link>
      </div>

      <div className="border-b border-gray-200 pb-4">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <BuildingStorefrontIcon className="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Store Settings</h1>
            <p className="text-sm text-gray-500">
              Manage your store's basic information and configuration
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Store Information</h2>
          <p className="text-sm text-gray-500">
            Basic information about your store
          </p>
        </div>

        <div className="p-6 space-y-6">
          {/* Store Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Store Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Enter store name"
              required
            />
          </div>

          {/* Default Settings */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Default Sales Channel
              </label>
              <select
                value={formData.default_sales_channel_id || ''}
                onChange={(e) => handleInputChange('default_sales_channel_id', e.target.value || null)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="">Select sales channel</option>
                {(salesChannelsData || []).map((channel: any) => (
                  <option key={channel.id} value={channel.id}>
                    {channel.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Default Region
              </label>
              <select
                value={formData.default_region_id || ''}
                onChange={(e) => handleInputChange('default_region_id', e.target.value || null)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="">Select region</option>
                {(regionsData || []).map((region: any) => (
                  <option key={region.id} value={region.id}>
                    {region.name} ({region.currency_code})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Default Location
              </label>
              <select
                value={formData.default_location_id || ''}
                onChange={(e) => handleInputChange('default_location_id', e.target.value || null)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="">Select location</option>
                {(locationsData || []).map((location: any) => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Additional Store Details */}
          <div className="border-t pt-6">
            <h3 className="text-base font-medium text-gray-900 mb-4">Additional Details</h3>
            
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Store Description
                </label>
                <textarea
                  rows={3}
                  value={formData.metadata?.description || ''}
                  onChange={(e) => handleMetadataChange('description', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Brief description of your store"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Contact Email
                </label>
                <input
                  type="email"
                  value={formData.metadata?.email || ''}
                  onChange={(e) => handleMetadataChange('email', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.metadata?.phone || ''}
                  onChange={(e) => handleMetadataChange('phone', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="+40 123 456 789"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Website URL
                </label>
                <input
                  type="url"
                  value={formData.metadata?.website || ''}
                  onChange={(e) => handleMetadataChange('website', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="https://yourstore.com"
                />
              </div>
            </div>
          </div>

          {/* Store Timestamps */}
          {store && (
            <div className="border-t pt-6">
              <h3 className="text-base font-medium text-gray-900 mb-4">Store Information</h3>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Store ID</dt>
                  <dd className="mt-1 text-sm text-gray-900 font-mono">{store.id}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(store.created_at).toLocaleDateString()}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(store.updated_at).toLocaleDateString()}
                  </dd>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end">
          <button
            onClick={handleSave}
            disabled={updateStoreMutation.isPending || !formData.name}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {updateStoreMutation.isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <BuildingStorefrontIcon className="-ml-1 mr-2 h-4 w-4" />
                Save Store Settings
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsStore; 