#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update customer passwords to "maxell95" for the provider_identity table
 * Excludes the admin account (<EMAIL>)
 * 
 * Note: The actual database uses provider_identity table with scrypt hashed passwords
 * stored in provider_metadata JSON field, not bcrypt in a customer table.
 */

const bcrypt = require('bcryptjs');

// List of customer emails to update (excluding admin)
const customerEmails = [
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>',
  '<EMAIL>'
];

const newPassword = 'maxell95';

async function generatePasswordHash(password) {
  try {
    // Using bcrypt for the new Cloudflare backend system
    const hash = await bcrypt.hash(password, 12);
    return hash;
  } catch (error) {
    console.error('Error generating password hash:', error);
    throw error;
  }
}

async function generateSQLStatements() {
  try {
    console.log('Analyzing current database structure...\n');
    console.log('DATABASE STRUCTURE DETECTED:');
    console.log('- Table: provider_identity');
    console.log('- Password storage: provider_metadata JSON field');
    console.log('- Current format: scrypt (c2NyeXB0...)');
    console.log('- Target format: bcrypt for new Cloudflare backend\n');
    
    const passwordHash = await generatePasswordHash(newPassword);
    console.log('Generated bcrypt hash for "maxell95":', passwordHash);
    console.log('\n=== IMPORTANT NOTE ===');
    console.log('The current database uses provider_identity table with scrypt passwords.');
    console.log('For your new Cloudflare backend, you\'ll need to either:');
    console.log('1. Update the backend to read from provider_identity table, OR');
    console.log('2. Migrate data to a customer table with bcrypt passwords\n');
    
    console.log('=== Option 1: Update provider_identity table (if keeping current structure) ===\n');
    
    // Generate UPDATE statements for provider_identity table
    for (const email of customerEmails) {
      // Create new provider_metadata JSON with bcrypt hash
      const providerMetadata = JSON.stringify({
        password: passwordHash
      });
      
      const updateSQL = `UPDATE provider_identity SET provider_metadata = '${providerMetadata}', updated_at = '${new Date().toISOString()}' WHERE entity_id = '${email}' AND provider = 'emailpass';`;
      console.log(updateSQL);
    }
    
    console.log('\n=== Option 2: Create/Update customer table (recommended for new backend) ===\n');
    
    // Generate customer table updates/inserts
    for (const email of customerEmails) {
      console.log(`-- For ${email}:`);
      console.log(`INSERT OR REPLACE INTO customer (id, email, password_hash, has_account, created_at, updated_at) VALUES ('${crypto.randomUUID()}', '${email}', '${passwordHash}', 1, '${new Date().toISOString()}', '${new Date().toISOString()}');`);
    }
    
    console.log('\n=== Verification Queries ===\n');
    console.log('-- Check current provider_identity data:');
    for (const email of customerEmails) {
      console.log(`SELECT entity_id, provider_metadata FROM provider_identity WHERE entity_id = '${email}';`);
    }
    
    console.log('\n-- Check customer table (if using Option 2):');
    console.log(`SELECT email, has_account, created_at, updated_at FROM customer WHERE email IN ('${customerEmails.join("', '")}');`);
    
    console.log('\n=== Recommendations ===');
    console.log('1. If you want to keep the current auth system, use Option 1');
    console.log('2. If migrating to the new Cloudflare backend, use Option 2');
    console.log('3. Update your backend authentication code to match the chosen table structure');
    console.log('4. Test login with password "maxell95" after updates');
    console.log('\nAdmin account (<EMAIL>) will remain unchanged.');
    
  } catch (error) {
    console.error('Error generating SQL statements:', error);
    process.exit(1);
  }
}

// Run the script
generateSQLStatements(); 