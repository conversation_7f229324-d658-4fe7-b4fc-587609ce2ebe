import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  ArrowLeftIcon,
  PlusIcon,
  TrashIcon,
  PhotoIcon,
  CogIcon,
  CurrencyDollarIcon,
  TagIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface ProductFormData {
  title: string;
  subtitle?: string;
  description?: string;
  handle: string;
  status: 'draft' | 'published' | 'archived';
  thumbnail?: string;
  collection_id?: string;
  type_id?: string;
  category_id?: string;
  tags: string[];
  metadata: Record<string, any>;
}

interface ProductVariant {
  id?: string;
  title: string;
  sku?: string;
  barcode?: string;
  ean?: string;
  upc?: string;
  price: number;
  compare_at_price?: number;
  cost_price?: number;
  currency_code?: string;
  inventory_quantity?: number;
  manage_inventory: boolean;
  allow_backorder: boolean;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  variant_rank?: number;
  options: VariantOption[];
  metadata?: Record<string, any>;
}

interface VariantOption {
  option_id: string;
  option_value_id: string;
  value: string;
  option_title?: string;
}

interface ProductImage {
  id?: string;
  url: string;
  rank: number;
  metadata?: Record<string, any>;
}

interface ProductOption {
  id?: string;
  title: string;
  values: ProductOptionValue[];
  metadata?: Record<string, any>;
}

interface ProductOptionValue {
  id?: string;
  value: string;
  metadata?: Record<string, any>;
}

const ProductForm: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const isEditing = !!id;

  const [activeTab, setActiveTab] = useState<'general' | 'variants' | 'images' | 'options' | 'seo'>('general');
  const [formData, setFormData] = useState<ProductFormData>({
    title: '',
    subtitle: '',
    description: '',
    handle: '',
    status: 'draft',
    thumbnail: '',
    collection_id: '',
    type_id: '',
    category_id: '',
    tags: [],
    metadata: {},
  });

  const [variants, setVariants] = useState<ProductVariant[]>([{
    title: 'Default Variant',
    sku: '',
    price: 0,
    currency_code: 'eur',
    region_id: null,
    inventory_quantity: 0,
    manage_inventory: true,
    allow_backorder: false,
    options: [],
  }]);

  const [images, setImages] = useState<ProductImage[]>([]);
  const [options, setOptions] = useState<ProductOption[]>([]);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    pricing: true,
    inventory: true,
    shipping: false,
  });

  // Fetch product data if editing
  const { data: product, isLoading: productLoading, error: productError } = useQuery({
    queryKey: ['product', id],
    queryFn: async () => {
      if (!id) return null;
      try {
        const response = await api.get(endpoints.products.get(id));
        return response.data.data;
      } catch (error: any) {
        console.error('Failed to fetch product:', error);
        if (error.response?.status === 404) {
          toast.error('Product not found');
        } else if (error.response?.status >= 500) {
          toast.error('Server error while fetching product');
        }
        throw error;
      }
    },
    enabled: isEditing,
    retry: (failureCount, error: any) => {
      // Don't retry on 404 (not found) or auth errors
      if (error?.response?.status === 404 || error?.response?.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
  });

  // Fetch variants
  const { data: variantsData } = useQuery({
    queryKey: ['product-variants', id],
    queryFn: async () => {
      if (!id) return [];
      try {
        const response = await api.get(endpoints.products.variants(id));
        return response.data.data || [];
      } catch (error: any) {
        console.error('Failed to fetch variants:', error);
        // Don't show error for variants if endpoint doesn't exist yet
        return [];
      }
    },
    enabled: isEditing,
    retry: false, // Don't retry for sub-resources that might not be implemented
  });

  // Fetch images
  const { data: imagesData } = useQuery({
    queryKey: ['product-images', id],
    queryFn: async () => {
      if (!id) return [];
      try {
        const response = await api.get(endpoints.products.images(id));
        return response.data.data || [];
      } catch (error: any) {
        console.error('Failed to fetch images:', error);
        // Don't show error for images if endpoint doesn't exist yet
        return [];
      }
    },
    enabled: isEditing,
    retry: false, // Don't retry for sub-resources that might not be implemented
  });

  // Fetch options
  const { data: optionsData } = useQuery({
    queryKey: ['product-options', id],
    queryFn: async () => {
      if (!id) return [];
      try {
        const response = await api.get(endpoints.products.options(id));
        return response.data.data || [];
      } catch (error: any) {
        console.error('Failed to fetch options:', error);
        // Don't show error for options if endpoint doesn't exist yet
        return [];
      }
    },
    enabled: isEditing,
    retry: false, // Don't retry for sub-resources that might not be implemented
  });

  // Fetch collections
  const { data: collectionsData } = useQuery({
    queryKey: ['collections'],
    queryFn: async () => {
      try {
        const response = await api.get(endpoints.collections.list);
        return response.data.data || response.data.collections || [];
      } catch (error: any) {
        console.error('Failed to fetch collections:', error);
        return [];
      }
    },
    retry: false,
  });

  // Fetch product types
  const { data: productTypes } = useQuery({
    queryKey: ['product-types'],
    queryFn: async () => {
      try {
        const response = await api.get(endpoints.productTypes.list);
        return response.data.data || response.data.product_types || [];
      } catch (error: any) {
        console.error('Failed to fetch product types:', error);
        return [];
      }
    },
    retry: false,
  });

  // Fetch categories
  const { data: categories } = useQuery({
    queryKey: ['product-categories'],
    queryFn: async () => {
      try {
        const response = await api.get(endpoints.categories.list);
        return response.data.data || response.data.categories || [];
      } catch (error: any) {
        console.error('Failed to fetch categories:', error);
        return [];
      }
    },
    retry: false,
  });

  // Fetch currencies
  const { data: currencies } = useQuery({
    queryKey: ['currencies'],
    queryFn: async () => {
      try {
        const response = await api.get(endpoints.currencies.list);
        return response.data.data || response.data.currencies || [];
      } catch (error: any) {
        console.error('Failed to fetch currencies:', error);
        return [];
      }
    },
    retry: false,
  });

  // Fetch regions
  const { data: regions } = useQuery({
    queryKey: ['regions'],
    queryFn: async () => {
      try {
        const response = await api.get(endpoints.regions.list);
        return response.data.data || response.data.regions || [];
      } catch (error: any) {
        console.error('Failed to fetch regions:', error);
        return [];
      }
    },
    retry: false,
  });

  // Save product mutation
  const saveProductMutation = useMutation({
    mutationFn: async (data: ProductFormData) => {
      if (isEditing) {
        return api.put(endpoints.products.update(id!), data);
      } else {
        return api.post(endpoints.products.create, data);
      }
    },
    onSuccess: (response) => {
      const productId = response.data.data?.id || id;
      queryClient.invalidateQueries({ queryKey: ['products'] });

      // Always save variants, images, and options for both create and edit
      saveAdditionalData(productId);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to save product');
    },
  });

  // Save variants mutation
  const saveVariantsMutation = useMutation({
    mutationFn: async ({ productId, variants: variantsToSave }: { productId: string; variants: ProductVariant[] }) => {
      const promises = variantsToSave.map(async (variant) => {
        // Prepare variant data without pricing fields
        const variantData = {
          title: variant.title,
          sku: variant.sku || null,
          barcode: variant.barcode || null,
          weight: variant.weight || null,
          dimensions: variant.dimensions || {},
          sort_order: variant.sort_order || 0,
          option_values: variant.options || [],
          manage_inventory: variant.manage_inventory !== false,
          allow_backorder: variant.allow_backorder || false,
          inventory_quantity: variant.inventory_quantity || 0,
          metadata: variant.metadata || {}
        };

        let variantResponse;
        if (variant.id) {
          variantResponse = await api.put(endpoints.products.updateVariant(productId, variant.id), variantData);
        } else {
          variantResponse = await api.post(endpoints.products.createVariant(productId), variantData);
        }

        const variantId = variantResponse.data.data?.id || variantResponse.data.id || variant.id;

        // Save pricing data separately if price is provided
        if (variant.price !== undefined && variant.price !== null) {
          const priceData = {
            currency_code: (variant.currency_code || 'eur').toLowerCase(),
            price: variant.price,
            compare_at_price: variant.compare_at_price || null,
            min_quantity: 1,
            region_id: variant.region_id || null
          };

          try {
            await api.post(endpoints.products.createVariantPrice(productId, variantId), priceData);
          } catch (priceError) {
            console.error('Failed to save variant price:', priceError);
            // Don't fail the entire operation if pricing fails
          }
        }

        return variantResponse;
      });
      return Promise.all(promises);
    },
  });

  // Save images mutation
  const saveImagesMutation = useMutation({
    mutationFn: async ({ productId, images: imagesToSave }: { productId: string; images: ProductImage[] }) => {
      const promises = imagesToSave.map(image => {
        if (image.id) {
          return api.put(endpoints.products.updateImage(productId, image.id), image);
        } else {
          return api.post(endpoints.products.createImage(productId), image);
        }
      });
      return Promise.all(promises);
    },
  });

  // Save options mutation
  const saveOptionsMutation = useMutation({
    mutationFn: async ({ productId, options: optionsToSave }: { productId: string; options: ProductOption[] }) => {
      const promises = optionsToSave.map(async (option) => {
        let optionResponse;
        if (option.id) {
          optionResponse = await api.put(endpoints.products.updateOption(productId, option.id), {
            title: option.title,
            name: option.title // Backend expects 'name' field
          });
        } else {
          optionResponse = await api.post(endpoints.products.createOption(productId), {
            title: option.title,
            name: option.title // Backend expects 'name' field
          });
        }

        const optionId = optionResponse.data.data?.id || option.id;

        // Save option values
        if (option.values && option.values.length > 0) {
          const valuePromises = option.values.map(value => {
            if (value.id) {
              return api.put(endpoints.products.updateOptionValue(productId, optionId, value.id), value);
            } else {
              return api.post(endpoints.products.createOptionValue(productId, optionId), value);
            }
          });
          await Promise.all(valuePromises);
        }

        return optionResponse;
      });
      return Promise.all(promises);
    },
  });

  // Initialize form data when editing
  useEffect(() => {
    if (product && isEditing) {
      setFormData({
        title: product.title || '',
        subtitle: product.subtitle || '',
        description: product.description || '',
        handle: product.handle || '',
        status: product.status || 'draft',
        thumbnail: product.thumbnail || '',
        collection_id: product.collection_id || '',
        type_id: product.type_id || '',
        category_id: product.category_id || '',
        tags: product.tags || [],
        metadata: product.metadata || {},
      });
    }
  }, [product, isEditing]);

  useEffect(() => {
    if (variantsData && Array.isArray(variantsData)) {
      // Ensure each variant has required fields
      const processedVariants = variantsData.map(variant => ({
        ...variant,
        title: variant.title || 'Default Variant',
        price: variant.price || 0,
        currency_code: variant.currency_code || 'eur',
        region_id: variant.region_id || null,
        inventory_quantity: variant.inventory_quantity || 0,
        manage_inventory: variant.manage_inventory !== undefined ? variant.manage_inventory : true,
        allow_backorder: variant.allow_backorder || false,
        options: variant.options || []
      }));
      setVariants(processedVariants);
    }
  }, [variantsData]);

  useEffect(() => {
    if (imagesData && Array.isArray(imagesData)) {
      // Ensure each image has required fields
      const processedImages = imagesData.map((image, index) => ({
        ...image,
        url: image.url || '',
        rank: image.rank !== undefined ? image.rank : image.sort_order || index,
        metadata: image.metadata || {}
      }));
      setImages(processedImages);
    }
  }, [imagesData]);

  useEffect(() => {
    const fetchOptionsWithValues = async () => {
      if (optionsData && Array.isArray(optionsData) && id) {
        try {
          // Fetch option values for each option
          const processedOptions = await Promise.all(
            optionsData.map(async (option) => {
              try {
                const valuesResponse = await api.get(endpoints.products.optionValues(id, option.id));
                const values = valuesResponse.data.data || [];
                return {
                  ...option,
                  title: option.name || option.title || '',
                  values: values.length > 0 ? values : [{ value: '' }] // Ensure at least one empty value
                };
              } catch (error) {
                console.error(`Failed to fetch values for option ${option.id}:`, error);
                return {
                  ...option,
                  title: option.name || option.title || '',
                  values: [{ value: '' }] // Default empty value
                };
              }
            })
          );
          setOptions(processedOptions);
        } catch (error) {
          console.error('Failed to process options:', error);
          // Fallback to options without values
          const processedOptions = optionsData.map(option => ({
            ...option,
            title: option.name || option.title || '',
            values: [{ value: '' }]
          }));
          setOptions(processedOptions);
        }
      } else if (optionsData && Array.isArray(optionsData)) {
        // For new products or when no ID is available
        const processedOptions = optionsData.map(option => ({
          ...option,
          title: option.name || option.title || '',
          values: option.values || [{ value: '' }]
        }));
        setOptions(processedOptions);
      }
    };

    fetchOptionsWithValues();
  }, [optionsData, id]);

  const saveAdditionalData = async (productId: string) => {
    try {
      const promises = [];

      // Save variants if there are any
      if (variants.length > 0) {
        promises.push(saveVariantsMutation.mutateAsync({ productId, variants }));
      }

      // Save images if there are any
      if (images.length > 0) {
        promises.push(saveImagesMutation.mutateAsync({ productId, images }));
      }

      // Save options if there are any
      if (options.length > 0) {
        promises.push(saveOptionsMutation.mutateAsync({ productId, options }));
      }

      // Wait for all additional data to be saved
      await Promise.all(promises);

      toast.success(isEditing ? 'Product updated successfully' : 'Product created successfully');
      navigate('/products');
    } catch (error) {
      console.error('Failed to save additional data:', error);
      toast.error(isEditing ? 'Product updated but failed to save additional data' : 'Product created but failed to save additional data');
      navigate('/products');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast.error('Product title is required');
      return;
    }

    saveProductMutation.mutate(formData);
  };

  const addVariant = () => {
    setVariants([...variants, {
      title: `Variant ${variants.length + 1}`,
      sku: '',
      price: 0,
      currency_code: 'eur',
      region_id: null,
      inventory_quantity: 0,
      manage_inventory: true,
      allow_backorder: false,
      options: [],
    }]);
  };

  const removeVariant = (index: number) => {
    if (variants.length > 1) {
      setVariants(variants.filter((_, i) => i !== index));
    }
  };

  const updateVariant = (index: number, field: keyof ProductVariant, value: any) => {
    const newVariants = [...variants];
    newVariants[index] = { ...newVariants[index], [field]: value };
    setVariants(newVariants);
  };

  const addImage = () => {
    const newImage: ProductImage = {
      url: '',
      rank: images.length,
    };
    setImages([...images, newImage]);
  };

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  const updateImage = (index: number, field: keyof ProductImage, value: any) => {
    const newImages = [...images];
    newImages[index] = { ...newImages[index], [field]: value };
    setImages(newImages);
  };

  const addOption = () => {
    const newOption: ProductOption = {
      title: '',
      values: [{ value: '' }],
    };
    setOptions([...options, newOption]);
  };

  const removeOption = (index: number) => {
    setOptions(options.filter((_, i) => i !== index));
  };

  const updateOption = (index: number, field: keyof ProductOption, value: any) => {
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], [field]: value };
    setOptions(newOptions);
  };

  const addOptionValue = (optionIndex: number) => {
    const newOptions = [...options];
    if (!newOptions[optionIndex].values) {
      newOptions[optionIndex].values = [];
    }
    newOptions[optionIndex].values.push({ value: '' });
    setOptions(newOptions);
  };

  const removeOptionValue = (optionIndex: number, valueIndex: number) => {
    const newOptions = [...options];
    if (!newOptions[optionIndex].values) {
      newOptions[optionIndex].values = [];
      return;
    }
    if (newOptions[optionIndex].values.length > 1) {
      newOptions[optionIndex].values = newOptions[optionIndex].values.filter((_, i) => i !== valueIndex);
      setOptions(newOptions);
    }
  };

  const updateOptionValue = (optionIndex: number, valueIndex: number, value: string) => {
    const newOptions = [...options];
    if (!newOptions[optionIndex].values) {
      newOptions[optionIndex].values = [];
    }
    if (!newOptions[optionIndex].values[valueIndex]) {
      newOptions[optionIndex].values[valueIndex] = { value: '' };
    }
    newOptions[optionIndex].values[valueIndex].value = value;
    setOptions(newOptions);
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (productLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Handle product load error for editing mode
  if (isEditing && productError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/products')}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-1" />
              Products
            </button>
            <span className="text-gray-400">/</span>
            <h1 className="text-2xl font-semibold text-gray-900">Edit Product</h1>
          </div>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Failed to load product
              </h3>
              <p className="mt-2 text-sm text-red-700">
                {productError?.response?.status === 404 
                  ? 'The product you are trying to edit was not found.' 
                  : 'There was an error loading the product. Please try again later.'}
              </p>
              <div className="mt-4">
                <button
                  onClick={() => navigate('/products')}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 transition-colors duration-200"
                >
                  Back to Products
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/products')}
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-1" />
            Products
          </button>
          <span className="text-gray-400">/</span>
          <h1 className="text-2xl font-semibold text-gray-900">
            {isEditing ? 'Edit Product' : 'Create Product'}
          </h1>
        </div>
        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={() => navigate('/products')}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={saveProductMutation.isPending}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200"
          >
            {saveProductMutation.isPending ? 'Saving...' : (isEditing ? 'Update Product' : 'Create Product')}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'general', label: 'General', icon: CogIcon },
            { id: 'variants', label: 'Variants', icon: CurrencyDollarIcon },
            { id: 'images', label: 'Images', icon: PhotoIcon },
            { id: 'options', label: 'Options', icon: TagIcon },
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* General Tab */}
        {activeTab === 'general' && (
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Product Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => {
                      setFormData({ ...formData, title: e.target.value });
                      if (!isEditing && !formData.handle) {
                        setFormData(prev => ({
                          ...prev,
                          title: e.target.value,
                          handle: e.target.value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')
                        }));
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter product title"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subtitle
                  </label>
                  <input
                    type="text"
                    value={formData.subtitle || ''}
                    onChange={(e) => setFormData({ ...formData, subtitle: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Product subtitle"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Handle *
                  </label>
                  <input
                    type="text"
                    value={formData.handle}
                    onChange={(e) => setFormData({ ...formData, handle: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="product-handle"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">URL-friendly version of the product name</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description || ''}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Product description"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                    <option value="archived">Archived</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Thumbnail URL
                  </label>
                  <input
                    type="url"
                    value={formData.thumbnail || ''}
                    onChange={(e) => setFormData({ ...formData, thumbnail: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
              </div>
            </div>

            {/* Organization */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Organization</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Collection
                  </label>
                  <select
                    value={formData.collection_id || ''}
                    onChange={(e) => setFormData({ ...formData, collection_id: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">No collection</option>
                    {Array.isArray(collectionsData) && collectionsData.map((collection: any) => (
                      <option key={collection.id} value={collection.id}>
                        {collection.title}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Product Type
                  </label>
                  <select
                    value={formData.type_id || ''}
                    onChange={(e) => setFormData({ ...formData, type_id: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">No type</option>
                    {Array.isArray(productTypes) && productTypes.map((type: any) => (
                      <option key={type.id} value={type.id}>
                        {type.name || type.value}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={formData.category_id || ''}
                    onChange={(e) => setFormData({ ...formData, category_id: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">No category</option>
                    {Array.isArray(categories) && categories.map((category: any) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Variants Tab */}
        {activeTab === 'variants' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Product Variants</h3>
                <button
                  type="button"
                  onClick={addVariant}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Variant
                </button>
              </div>

              <div className="divide-y divide-gray-200">
                {variants.map((variant, index) => (
                  <div key={index} className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-md font-medium text-gray-900">
                        Variant {index + 1}
                      </h4>
                      {variants.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeVariant(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Title
                        </label>
                        <input
                          type="text"
                          value={variant.title}
                          onChange={(e) => updateVariant(index, 'title', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Variant title"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          SKU
                        </label>
                        <input
                          type="text"
                          value={variant.sku || ''}
                          onChange={(e) => updateVariant(index, 'sku', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="SKU"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Barcode
                        </label>
                        <input
                          type="text"
                          value={variant.barcode || ''}
                          onChange={(e) => updateVariant(index, 'barcode', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Barcode"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Price ({(variant.currency_code || 'eur').toUpperCase()})
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          value={variant.price === 0 ? '' : variant.price || ''}
                          onChange={(e) => {
                            const value = e.target.value;
                            updateVariant(index, 'price', value === '' ? 0 : parseFloat(value) || 0);
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Compare at Price
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          value={variant.compare_at_price || ''}
                          onChange={(e) => updateVariant(index, 'compare_at_price', parseFloat(e.target.value) || undefined)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Currency
                        </label>
                        <select
                          value={variant.currency_code || 'eur'}
                          onChange={(e) => updateVariant(index, 'currency_code', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          {Array.isArray(currencies) && currencies.map((currency: any) => (
                            <option key={currency.code} value={currency.code}>
                              {currency.code.toUpperCase()} - {currency.name} ({currency.symbol})
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Region (Optional)
                        </label>
                        <select
                          value={variant.region_id || ''}
                          onChange={(e) => updateVariant(index, 'region_id', e.target.value || null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">All regions</option>
                          {Array.isArray(regions) && regions.map((region: any) => (
                            <option key={region.id} value={region.id}>
                              {region.name} ({region.currency_code?.toUpperCase()})
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Inventory Quantity
                        </label>
                        <input
                          type="number"
                          min="0"
                          value={variant.inventory_quantity === 0 ? '' : variant.inventory_quantity || ''}
                          onChange={(e) => {
                            const value = e.target.value;
                            updateVariant(index, 'inventory_quantity', value === '' ? 0 : parseInt(value) || 0);
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Weight (g)
                        </label>
                        <input
                          type="number"
                          min="0"
                          value={variant.weight || ''}
                          onChange={(e) => updateVariant(index, 'weight', parseInt(e.target.value) || undefined)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0"
                        />
                      </div>

                      <div className="flex items-center space-x-4">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={variant.manage_inventory}
                            onChange={(e) => updateVariant(index, 'manage_inventory', e.target.checked)}
                            className="mr-2"
                          />
                          <span className="text-sm text-gray-700">Track inventory</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={variant.allow_backorder}
                            onChange={(e) => updateVariant(index, 'allow_backorder', e.target.checked)}
                            className="mr-2"
                          />
                          <span className="text-sm text-gray-700">Allow backorder</span>
                        </label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Images Tab */}
        {activeTab === 'images' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Product Images</h3>
                <button
                  type="button"
                  onClick={addImage}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Image
                </button>
              </div>

              <div className="p-6">
                {images.length === 0 ? (
                  <div className="text-center py-8">
                    <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No images</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Add product images to showcase your product.
                    </p>
                    <button
                      type="button"
                      onClick={addImage}
                      className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
                    >
                      Add Image
                    </button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {images.map((image, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">Image {index + 1}</span>
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                        <div className="space-y-2">
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Image URL
                            </label>
                            <input
                              type="url"
                              value={image.url}
                              onChange={(e) => updateImage(index, 'url', e.target.value)}
                              className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="https://example.com/image.jpg"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Sort Order
                            </label>
                            <input
                              type="number"
                              min="0"
                              value={image.rank === 0 ? '' : image.rank || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                updateImage(index, 'rank', value === '' ? 0 : parseInt(value) || 0);
                              }}
                              className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          {image.url && (
                            <div className="mt-2">
                              <img
                                src={image.url}
                                alt={`Product image ${index + 1}`}
                                className="w-full h-32 object-cover rounded"
                                onError={(e) => {
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Options Tab */}
        {activeTab === 'options' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Product Options</h3>
                <button
                  type="button"
                  onClick={addOption}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Option
                </button>
              </div>

              <div className="p-6">
                {options.length === 0 ? (
                  <div className="text-center py-8">
                    <TagIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No options</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Add product options like size, color, or material.
                    </p>
                    <button
                      type="button"
                      onClick={addOption}
                      className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
                    >
                      Add Option
                    </button>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {options.map((option, optionIndex) => (
                      <div key={optionIndex} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <input
                            type="text"
                            value={option.title || ''}
                            onChange={(e) => updateOption(optionIndex, 'title', e.target.value)}
                            className="text-lg font-medium bg-transparent border-none p-0 focus:ring-0 focus:outline-none"
                            placeholder="Option name (e.g., Size, Color)"
                          />
                          <button
                            type="button"
                            onClick={() => removeOption(optionIndex)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">Option Values</span>
                            <button
                              type="button"
                              onClick={() => addOptionValue(optionIndex)}
                              className="text-sm text-blue-600 hover:text-blue-800"
                            >
                              <PlusIcon className="h-4 w-4 inline mr-1" />
                              Add Value
                            </button>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                            {(option.values || []).map((value, valueIndex) => (
                              <div key={valueIndex} className="flex items-center space-x-2">
                                <input
                                  type="text"
                                  value={value.value || ''}
                                  onChange={(e) => updateOptionValue(optionIndex, valueIndex, e.target.value)}
                                  className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Value (e.g., Small, Red)"
                                />
                                {(option.values || []).length > 1 && (
                                  <button
                                    type="button"
                                    onClick={() => removeOptionValue(optionIndex, valueIndex)}
                                    className="text-red-600 hover:text-red-800"
                                  >
                                    <TrashIcon className="h-3 w-3" />
                                  </button>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default ProductForm; 