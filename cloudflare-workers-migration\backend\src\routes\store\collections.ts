import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { Context } from 'hono';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Utility functions
function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error(error);
  return c.json({ error: message, details: error.message }, 500);
}

function safeParseJson(value: any): any {
  if (!value) return null;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }
  return value;
}

// GET /store/collections - Get all collections
app.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;
    const handle = c.req.query('handle');

    // Build collections query
    let query = `
      SELECT 
        c.id, c.handle, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
        ct.title, ct.description, ct.seo_title, ct.seo_description
      FROM collections c
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'en'
      WHERE c.deleted_at IS NULL AND c.is_active = 1
    `;

    const params: any[] = [];

    // Add handle filter if provided
    if (handle) {
      query += ` AND c.handle = ?`;
      params.push(handle);
    }

    // Add sorting and pagination
    query += ` ORDER BY c.sort_order ASC, c.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const collections = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM collections c 
      WHERE c.deleted_at IS NULL AND c.is_active = 1
    `;
    const countParams: any[] = [];

    if (handle) {
      countQuery += ` AND c.handle = ?`;
      countParams.push(handle);
    }

    const countResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();
    const total = (countResult as any)?.total || 0;

    // Process collections
    const processedCollections = (collections.results || []).map((collection: any) => {
      collection.metadata = safeParseJson(collection.metadata) || {};
      return collection;
    });

    return c.json({
      success: true,
      data: processedCollections,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch collections');
  }
});

// GET /store/collections/:identifier - Get collection by ID or handle
app.get('/:identifier', async (c) => {
  try {
    const identifier = c.req.param('identifier');
    const currency_code = c.req.query('currency_code') || 'USD';
    const region_id = c.req.query('region_id') || '';
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;

    // Get collection by handle or ID
    const collectionQuery = `
      SELECT 
        c.id, c.handle, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
        ct.title, ct.description, ct.seo_title, ct.seo_description
      FROM collections c
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'en'
      WHERE c.deleted_at IS NULL AND c.is_active = 1
      AND (c.handle = ? OR c.id = ?)
    `;

    const collection = await c.env.DB.prepare(collectionQuery).bind(identifier, identifier).first();

    if (!collection) {
      return c.json({ success: false, error: 'Collection not found' }, 404);
    }

    const collectionData = collection as any;
    collectionData.metadata = safeParseJson(collectionData.metadata) || {};

    // Get products in this collection
    const productsQuery = `
      SELECT 
        p.id, p.handle, p.status, p.collection_id, p.type_id, p.thumbnail,
        p.weight, p.dimensions, p.requires_shipping, p.is_giftcard, p.is_discountable,
        p.tags, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description,
        ptype.name as type_name,
        COUNT(pv.id) as variant_count
      FROM products p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      LEFT JOIN product_types ptype ON p.type_id = ptype.id
      LEFT JOIN product_variants pv ON p.id = pv.product_id AND pv.deleted_at IS NULL
      WHERE p.deleted_at IS NULL AND p.status = 'published' AND p.collection_id = ?
      GROUP BY p.id, pt.title, pt.subtitle, pt.description, ptype.name
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const products = await c.env.DB.prepare(productsQuery).bind(collectionData.id, limit, offset).all();

    // Get total product count
    const productCountQuery = `
      SELECT COUNT(*) as total
      FROM products p
      WHERE p.deleted_at IS NULL AND p.status = 'published' AND p.collection_id = ?
    `;
    const productCountResult = await c.env.DB.prepare(productCountQuery).bind(collectionData.id).first();
    const productTotal = (productCountResult as any)?.total || 0;

    // Enrich products with variants, images, and pricing
    const enrichedProducts = [];
    for (const product of products.results || []) {
      const productData = product as any;

      // Get variants with pricing
      const variantsQuery = `
        SELECT 
          pv.id, pv.title, pv.sku, pv.barcode, pv.weight, pv.dimensions,
          pv.sort_order, pv.option_values, pv.manage_inventory, pv.allow_backorder,
          pv.inventory_quantity, pv.metadata, pv.created_at, pv.updated_at,
          vp.price, vp.compare_at_price, vp.cost_price, vp.currency_code
        FROM product_variants pv
        LEFT JOIN variant_prices vp ON pv.id = vp.variant_id 
          AND vp.currency_code = ? 
          AND (vp.region_id = ? OR vp.region_id IS NULL)
        WHERE pv.product_id = ? AND pv.deleted_at IS NULL
        ORDER BY vp.region_id DESC, pv.sort_order ASC, pv.created_at ASC
      `;
      
      const variants = await c.env.DB.prepare(variantsQuery)
        .bind(currency_code, region_id || null, productData.id)
        .all();

      // Get images
      const imagesQuery = `
        SELECT id, url, alt_text, sort_order, metadata
        FROM product_images
        WHERE product_id = ?
        ORDER BY sort_order ASC, created_at ASC
      `;
      const images = await c.env.DB.prepare(imagesQuery).bind(productData.id).all();

      // Parse JSON fields
      productData.dimensions = safeParseJson(productData.dimensions);
      productData.tags = safeParseJson(productData.tags) || [];
      productData.metadata = safeParseJson(productData.metadata) || {};

      // Process variants
      const processedVariants = (variants.results || []).map((variant: any) => {
        variant.dimensions = safeParseJson(variant.dimensions);
        variant.option_values = safeParseJson(variant.option_values) || [];
        variant.metadata = safeParseJson(variant.metadata) || {};
        
        // Ensure price is a number
        variant.price = variant.price ? parseInt(variant.price) : 0;
        variant.compare_at_price = variant.compare_at_price ? parseInt(variant.compare_at_price) : null;
        variant.cost_price = variant.cost_price ? parseInt(variant.cost_price) : null;
        
        // Add calculated_price structure for frontend compatibility
        variant.calculated_price = {
          calculated_amount: variant.price,
          original_amount: variant.price,
          currency_code: variant.currency_code || currency_code,
          compare_at_price: variant.compare_at_price
        };

        return variant;
      });

      // Process images
      const processedImages = (images.results || []).map((image: any) => {
        image.metadata = safeParseJson(image.metadata) || {};
        return image;
      });

      // Add product-level pricing from first variant
      const firstVariant = processedVariants[0];
      if (firstVariant) {
        productData.price = firstVariant.price;
        productData.currency_code = firstVariant.currency_code || currency_code;
        productData.compare_at_price = firstVariant.compare_at_price;
      }

      enrichedProducts.push({
        ...productData,
        variants: processedVariants,
        images: processedImages
      });
    }

    return c.json({
      success: true,
      data: {
        ...collectionData,
        products: enrichedProducts,
        product_count: productTotal,
        pagination: {
          page,
          limit,
          total: productTotal,
          pages: Math.ceil(productTotal / limit),
          hasNext: page * limit < productTotal,
          hasPrev: page > 1,
        }
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch collection');
  }
});

// GET /store/collections/:identifier/products - Get products in a collection
app.get('/:identifier/products', async (c) => {
  try {
    const identifier = c.req.param('identifier');
    const currency_code = c.req.query('currency_code') || 'RON';
    const region_id = c.req.query('region_id') || '';
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;
    const sort = c.req.query('sort') || 'created_at';
    const order = c.req.query('order') || 'desc';

    // Get collection by handle or ID
    const collectionQuery = `
      SELECT id, handle, sort_order, is_active, metadata, created_at, updated_at
      FROM collections
      WHERE deleted_at IS NULL AND is_active = 1
      AND (handle = ? OR id = ?)
    `;

    const collection = await c.env.DB.prepare(collectionQuery).bind(identifier, identifier).first();

    if (!collection) {
      return c.json({ success: false, error: 'Collection not found' }, 404);
    }

    // Get products in this collection with enhanced filtering
    let productsQuery = `
      SELECT
        p.id, p.handle, p.status, p.collection_id, p.type_id, p.thumbnail,
        p.weight, p.dimensions, p.origin_country, p.hs_code, p.requires_shipping,
        p.is_giftcard, p.is_discountable, p.tags, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description, pt.seo_title, pt.seo_description,
        ptype.name as type_name,
        COUNT(DISTINCT pv.id) as variant_count
      FROM products p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      LEFT JOIN product_types ptype ON p.type_id = ptype.id
      LEFT JOIN product_variants pv ON p.id = pv.product_id AND pv.deleted_at IS NULL
      WHERE p.deleted_at IS NULL AND p.status = 'published' AND p.collection_id = ?
      GROUP BY p.id, pt.title, pt.subtitle, pt.description, pt.seo_title, pt.seo_description, ptype.name
    `;

    const params = [collection.id];

    // Add sorting
    const validSorts = ['created_at', 'updated_at', 'title', 'price'];
    const validOrders = ['asc', 'desc'];
    const sortField = validSorts.includes(sort) ? sort : 'created_at';
    const sortOrder = validOrders.includes(order) ? order : 'desc';

    if (sortField === 'title') {
      productsQuery += ` ORDER BY pt.title ${sortOrder.toUpperCase()}`;
    } else if (sortField === 'price') {
      productsQuery += ` ORDER BY (
        SELECT MIN(vp.price)
        FROM product_variants pv2
        LEFT JOIN variant_prices vp ON pv2.id = vp.variant_id AND vp.currency_code = ?
        WHERE pv2.product_id = p.id AND pv2.deleted_at IS NULL
      ) ${sortOrder.toUpperCase()}`;
      params.push(currency_code);
    } else {
      productsQuery += ` ORDER BY p.${sortField} ${sortOrder.toUpperCase()}`;
    }

    productsQuery += ` LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const products = await c.env.DB.prepare(productsQuery).bind(...params).all();

    // Get total product count
    const productCountQuery = `
      SELECT COUNT(*) as total
      FROM products p
      WHERE p.deleted_at IS NULL AND p.status = 'published' AND p.collection_id = ?
    `;
    const productCountResult = await c.env.DB.prepare(productCountQuery).bind(collection.id).first();
    const productTotal = (productCountResult as any)?.total || 0;

    // Enrich products with variants, images, and pricing
    const enrichedProducts = [];
    for (const product of products.results || []) {
      const productData = product as any;

      // Get variants with pricing
      const variantsQuery = `
        SELECT
          pv.id, pv.title, pv.sku, pv.barcode, pv.weight, pv.dimensions,
          pv.sort_order, pv.option_values, pv.manage_inventory, pv.allow_backorder,
          pv.inventory_quantity, pv.metadata, pv.created_at, pv.updated_at,
          vp.price, vp.compare_at_price, vp.cost_price, vp.currency_code
        FROM product_variants pv
        LEFT JOIN variant_prices vp ON pv.id = vp.variant_id
          AND vp.currency_code = ?
          AND (vp.region_id = ? OR vp.region_id IS NULL)
        WHERE pv.product_id = ? AND pv.deleted_at IS NULL
        ORDER BY vp.region_id DESC, pv.sort_order ASC, pv.created_at ASC
      `;

      const variants = await c.env.DB.prepare(variantsQuery)
        .bind(currency_code, region_id || null, productData.id)
        .all();

      // Get images
      const imagesQuery = `
        SELECT id, url, alt_text, sort_order, metadata
        FROM product_images
        WHERE product_id = ?
        ORDER BY sort_order ASC, created_at ASC
      `;
      const images = await c.env.DB.prepare(imagesQuery).bind(productData.id).all();

      // Parse JSON fields
      productData.dimensions = safeParseJson(productData.dimensions);
      productData.tags = safeParseJson(productData.tags) || [];
      productData.metadata = safeParseJson(productData.metadata) || {};

      // Process variants
      const processedVariants = (variants.results || []).map((variant: any) => {
        variant.dimensions = safeParseJson(variant.dimensions);
        variant.option_values = safeParseJson(variant.option_values) || [];
        variant.metadata = safeParseJson(variant.metadata) || {};

        // Ensure price is a number
        variant.price = variant.price ? parseInt(variant.price) : 0;
        variant.compare_at_price = variant.compare_at_price ? parseInt(variant.compare_at_price) : null;
        variant.cost_price = variant.cost_price ? parseInt(variant.cost_price) : null;

        // Add calculated_price structure for frontend compatibility
        variant.calculated_price = {
          calculated_amount: variant.price,
          original_amount: variant.price,
          currency_code: variant.currency_code || currency_code,
          compare_at_price: variant.compare_at_price
        };

        return variant;
      });

      // Process images
      const processedImages = (images.results || []).map((image: any) => {
        image.metadata = safeParseJson(image.metadata) || {};
        return image;
      });

      // Add product-level pricing from first variant
      const firstVariant = processedVariants[0];
      if (firstVariant) {
        productData.price = firstVariant.price;
        productData.currency_code = firstVariant.currency_code || currency_code;
        productData.compare_at_price = firstVariant.compare_at_price;
      }

      enrichedProducts.push({
        ...productData,
        variants: processedVariants,
        images: processedImages
      });
    }

    return c.json({
      success: true,
      data: enrichedProducts,
      pagination: {
        page,
        limit,
        total: productTotal,
        pages: Math.ceil(productTotal / limit),
        hasNext: page * limit < productTotal,
        hasPrev: page > 1,
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch collection products');
  }
});

export const collectionRoutes = app;