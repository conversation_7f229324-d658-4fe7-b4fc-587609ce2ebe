import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { CurrencyDollarIcon, ArrowLeftIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';
import { api } from '../lib/api';

interface Currency {
  code: string;
  symbol: string;
  symbol_native: string;
  decimal_digits: number;
  rounding: number;
  name: string;
  created_at: string;
  updated_at: string;
}

interface StoreCurrency {
  id: string;
  currency_code: string;
  is_default: boolean;
  store_id: string;
  created_at: string;
  updated_at: string;
}

const SettingsCurrencies: React.FC = () => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    code: '',
    symbol: '',
    symbol_native: '',
    decimal_digits: 2,
    rounding: 1.0,
    name: '',
  });
  const queryClient = useQueryClient();

  // Fetch currencies
  const { data: currencyData, isLoading } = useQuery({
    queryKey: ['currencies'],
    queryFn: async () => {
      try {
        const response = await api.get('/admin/api/currencies');
        // Ensure we always return a valid object with currencies array
        return {
          currencies: response.data?.data?.currencies || response.data?.currencies || response.data?.data || [],
          storeCurrencies: response.data?.data?.storeCurrencies || response.data?.storeCurrencies || []
        };
      } catch (error) {
        console.error('Failed to fetch currencies:', error);
        // Return empty arrays instead of undefined to prevent React Query errors
        return { 
          currencies: [],
          storeCurrencies: []
        };
      }
    },
  });

  // Create currency mutation
  const createCurrencyMutation = useMutation({
    mutationFn: (newCurrency: any) =>
      api.post('/admin/api/currencies', newCurrency),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['currencies'] });
      queryClient.invalidateQueries({ queryKey: ['settings-overview'] });
      toast.success('Currency created successfully');
      setShowAddForm(false);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create currency');
    },
  });

  const resetForm = () => {
    setFormData({
      code: '',
      symbol: '',
      symbol_native: '',
      decimal_digits: 2,
      rounding: 1.0,
      name: '',
    });
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createCurrencyMutation.mutate(formData);
  };

  const currencies = currencyData?.currencies || [];
  const storeCurrencies = currencyData?.storeCurrencies || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          to="/settings"
          className="flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-1" />
          Back to Settings
        </Link>
      </div>

      <div className="border-b border-gray-200 pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <CurrencyDollarIcon className="w-5 h-5 text-green-600" />
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Currencies</h1>
              <p className="text-sm text-gray-500">
                Manage supported currencies for your store
              </p>
            </div>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <PlusIcon className="-ml-1 mr-2 h-4 w-4" />
            Add Currency
          </button>
        </div>
      </div>

      {/* Add Currency Form */}
      {showAddForm && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Add New Currency</h2>
          </div>
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Currency Code <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.code}
                  onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="USD"
                  maxLength={3}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Currency Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="US Dollar"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Symbol <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.symbol}
                  onChange={(e) => handleInputChange('symbol', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="$"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Native Symbol <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.symbol_native}
                  onChange={(e) => handleInputChange('symbol_native', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="$"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Decimal Digits
                </label>
                <input
                  type="number"
                  value={formData.decimal_digits}
                  onChange={(e) => handleInputChange('decimal_digits', parseInt(e.target.value))}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  min="0"
                  max="4"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Rounding
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.rounding}
                  onChange={(e) => handleInputChange('rounding', parseFloat(e.target.value))}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  min="0.01"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  resetForm();
                }}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createCurrencyMutation.isPending}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {createCurrencyMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  'Create Currency'
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Currencies List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">Available Currencies</h2>
            <span className="text-sm text-gray-500">
              {currencies.length} currencies configured
            </span>
          </div>
        </div>

        {currencies.length === 0 ? (
          <div className="p-6 text-center">
            <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No currencies</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by adding a currency for your store.
            </p>
          </div>
        ) : (
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Currency
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Symbol
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Decimal Digits
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currencies.map((currency: Currency) => {
                  const isStoreCurrency = storeCurrencies.some((sc: StoreCurrency) => sc.currency_code === currency.code);
                  const isDefault = storeCurrencies.find((sc: StoreCurrency) => sc.currency_code === currency.code && sc.is_default);
                  
                  return (
                    <tr key={currency.code} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-900">
                              {currency.symbol}
                            </span>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">
                              {currency.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              Native: {currency.symbol_native}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm font-mono text-gray-900">
                          {currency.code}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">
                          {currency.symbol}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">
                          {currency.decimal_digits}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-2">
                          {isDefault && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Default
                            </span>
                          )}
                          {isStoreCurrency && !isDefault && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Active
                            </span>
                          )}
                          {!isStoreCurrency && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              Available
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(currency.created_at).toLocaleDateString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Store Currencies */}
      {storeCurrencies.length > 0 && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Store Currencies</h2>
            <p className="text-sm text-gray-500">
              Currencies currently enabled for your store
            </p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {storeCurrencies.map((storeCurrency: StoreCurrency) => {
                const currency = currencies.find((c: Currency) => c.code === storeCurrency.currency_code);
                return (
                  <div key={storeCurrency.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-900">
                            {currency?.symbol || '?'}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {currency?.name || storeCurrency.currency_code}
                          </div>
                          <div className="text-sm text-gray-500">
                            {storeCurrency.currency_code}
                          </div>
                        </div>
                      </div>
                      {storeCurrency.is_default && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Default
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsCurrencies; 