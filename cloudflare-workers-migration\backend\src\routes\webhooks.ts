import { Hono } from 'hono';
import { WorkerEnv } from 'handmadein-shared';
import { DatabaseService } from '../services/database';

export const webhookRoutes = new Hono<{ Bindings: WorkerEnv }>();

// Stripe webhook handler
webhookRoutes.post('/stripe', async (c) => {
  try {
    const body = await c.req.text();
    const signature = c.req.header('stripe-signature');

    if (!signature) {
      return c.json({
        success: false,
        error: 'Missing Stripe signature',
      }, 400);
    }

    // In a real implementation, you'd verify the webhook signature
    // const stripe = new Stripe(c.env.STRIPE_SECRET_KEY);
    // const event = stripe.webhooks.constructEvent(body, signature, c.env.STRIPE_WEBHOOK_SECRET);

    // For now, we'll parse the body as JSON
    const event = JSON.parse(body);

    const db = new DatabaseService(c.env);

    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(db, event.data.object);
        break;
      
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(db, event.data.object);
        break;
      
      case 'charge.dispute.created':
        await handleChargeDispute(db, event.data.object);
        break;
      
      default:
        console.log(`Unhandled Stripe event type: ${event.type}`);
    }

    return c.json({ received: true });
  } catch (error) {
    console.error('Error processing Stripe webhook:', error);
    return c.json({
      success: false,
      error: 'Failed to process webhook',
    }, 500);
  }
});

// Handle successful payment
async function handlePaymentSucceeded(db: DatabaseService, paymentIntent: any) {
  try {
    // Find the order associated with this payment
    const payments = await db.findMany('payments', {
      where: { 
        provider_id: 'stripe',
        // You'd need to store the payment_intent_id in the data field
      },
      limit: 1,
    });

    if (payments.length === 0) {
      console.error('No payment found for payment intent:', paymentIntent.id);
      return;
    }

    const payment = payments[0];

    // Update payment status
    await db.update('payments', payment.id, {
      captured_at: new Date().toISOString(),
      data: JSON.stringify({
        ...JSON.parse(payment.data),
        payment_intent: paymentIntent,
      }),
    });

    // Update order status
    if (payment.order_id) {
      await db.update('orders', payment.order_id, {
        payment_status: 'captured',
        status: 'completed',
      });

      // Send order confirmation email
      // This would integrate with your email service
      console.log('Order payment confirmed:', payment.order_id);
    }
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

// Handle failed payment
async function handlePaymentFailed(db: DatabaseService, paymentIntent: any) {
  try {
    // Find the order associated with this payment
    const payments = await db.findMany('payments', {
      where: { 
        provider_id: 'stripe',
      },
      limit: 1,
    });

    if (payments.length === 0) {
      console.error('No payment found for payment intent:', paymentIntent.id);
      return;
    }

    const payment = payments[0];

    // Update payment status
    await db.update('payments', payment.id, {
      canceled_at: new Date().toISOString(),
      data: JSON.stringify({
        ...JSON.parse(payment.data),
        payment_intent: paymentIntent,
        failure_reason: paymentIntent.last_payment_error?.message,
      }),
    });

    // Update order status
    if (payment.order_id) {
      await db.update('orders', payment.order_id, {
        payment_status: 'requires_action',
      });

      // Send payment failed notification
      console.log('Order payment failed:', payment.order_id);
    }
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

// Handle charge dispute
async function handleChargeDispute(db: DatabaseService, dispute: any) {
  try {
    // Log the dispute for manual review
    console.log('Charge dispute created:', dispute.id);
    
    // You might want to create a dispute record in your database
    // and notify administrators
  } catch (error) {
    console.error('Error handling charge dispute:', error);
  }
}

// Generic webhook endpoint for other services
webhookRoutes.post('/generic', async (c) => {
  try {
    const body = await c.req.json();
    const source = c.req.header('x-webhook-source');

    console.log('Generic webhook received:', { source, body });

    // Handle different webhook sources
    switch (source) {
      case 'trusted-shop':
        await handleTrustedShopWebhook(body);
        break;
      
      case 'sameday':
        await handleSamedayWebhook(body);
        break;
      
      default:
        console.log('Unknown webhook source:', source);
    }

    return c.json({ received: true });
  } catch (error) {
    console.error('Error processing generic webhook:', error);
    return c.json({
      success: false,
      error: 'Failed to process webhook',
    }, 500);
  }
});

// Handle Trusted Shop webhooks
async function handleTrustedShopWebhook(data: any) {
  try {
    // Handle trusted shop review notifications
    console.log('Trusted Shop webhook:', data);
  } catch (error) {
    console.error('Error handling Trusted Shop webhook:', error);
  }
}

// Handle Sameday courier webhooks
async function handleSamedayWebhook(data: any) {
  try {
    // Handle delivery status updates
    console.log('Sameday webhook:', data);
  } catch (error) {
    console.error('Error handling Sameday webhook:', error);
  }
} 