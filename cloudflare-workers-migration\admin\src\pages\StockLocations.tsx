import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  BuildingStorefrontIcon,
  MapPinIcon,
  CubeIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface StockLocation {
  id: string;
  name: string;
  address?: LocationAddress;
  metadata?: any;
  item_count?: number;
  total_inventory?: number;
  low_stock_items?: number;
  created_at: string;
  updated_at: string;
}

interface LocationAddress {
  id: string;
  address_1: string;
  address_2?: string;
  company?: string;
  city: string;
  country_code: string;
  phone?: string;
  province?: string;
  postal_code: string;
  metadata?: any;
}

interface CreateLocationData {
  name: string;
  address?: {
    address_1: string;
    address_2?: string;
    company?: string;
    city: string;
    country_code: string;
    phone?: string;
    province?: string;
    postal_code: string;
  };
  metadata?: any;
}

const StockLocations: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState<StockLocation | null>(null);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [formData, setFormData] = useState<CreateLocationData>({
    name: '',
    address: {
      address_1: '',
      address_2: '',
      company: '',
      city: '',
      country_code: '',
      phone: '',
      province: '',
      postal_code: '',
    },
  });

  const queryClient = useQueryClient();

  // Fetch stock locations
  const { data: locationsData, isLoading, error } = useQuery({
    queryKey: ['stock-locations', currentPage, searchTerm],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
      });
      
      if (searchTerm) params.append('search', searchTerm);

      const response = await api.get(`${endpoints.inventory.locations}?${params}`);
      return response.data;
    },
  });

  // Create location mutation
  const createLocationMutation = useMutation({
    mutationFn: async (data: CreateLocationData) => {
      return api.post(endpoints.inventory.createLocation, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-locations'] });
      toast.success('Stock location created successfully');
      setIsCreateModalOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create stock location');
    },
  });

  // Update location mutation
  const updateLocationMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: CreateLocationData }) => {
      return api.put(endpoints.inventory.updateLocation(id), data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-locations'] });
      toast.success('Stock location updated successfully');
      setEditingLocation(null);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update stock location');
    },
  });

  // Delete location mutation
  const deleteLocationMutation = useMutation({
    mutationFn: async (id: string) => {
      return api.delete(endpoints.inventory.deleteLocation(id));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-locations'] });
      toast.success('Stock location deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete stock location');
    },
  });

  const resetForm = () => {
    setFormData({
      name: '',
      address: {
        address_1: '',
        address_2: '',
        company: '',
        city: '',
        country_code: '',
        phone: '',
        province: '',
        postal_code: '',
      },
    });
  };

  const handleCreateLocation = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      toast.error('Location name is required');
      return;
    }
    createLocationMutation.mutate(formData);
  };

  const handleUpdateLocation = (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingLocation || !formData.name.trim()) {
      toast.error('Location name is required');
      return;
    }
    updateLocationMutation.mutate({ id: editingLocation.id, data: formData });
  };

  const handleDeleteLocation = (id: string) => {
    if (confirm('Are you sure you want to delete this stock location? This action cannot be undone.')) {
      deleteLocationMutation.mutate(id);
    }
  };

  const handleEdit = (location: StockLocation) => {
    setEditingLocation(location);
    setFormData({
      name: location.name,
      address: {
        address_1: location.address?.address_1 || '',
        address_2: location.address?.address_2 || '',
        company: location.address?.company || '',
        city: location.address?.city || '',
        country_code: location.address?.country_code || '',
        phone: location.address?.phone || '',
        province: location.address?.province || '',
        postal_code: location.address?.postal_code || '',
      },
      metadata: location.metadata,
    });
    setIsCreateModalOpen(true);
  };

  const formatAddress = (address?: LocationAddress) => {
    if (!address) return 'No address provided';
    
    const parts = [];
    if (address.company) parts.push(address.company);
    parts.push(address.address_1);
    if (address.address_2) parts.push(address.address_2);
    parts.push(`${address.city}, ${address.province || ''} ${address.postal_code}`.trim());
    parts.push(address.country_code.toUpperCase());
    
    return parts.filter(Boolean).join('\n');
  };

  const toggleLocationSelection = (locationId: string) => {
    setSelectedLocations(prev =>
      prev.includes(locationId)
        ? prev.filter(id => id !== locationId)
        : [...prev, locationId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedLocations.length === locationsData?.locations?.length) {
      setSelectedLocations([]);
    } else {
      setSelectedLocations(locationsData?.locations?.map((location: StockLocation) => location.id) || []);
    }
  };

  const locations = locationsData?.locations || [];
  const totalPages = Math.ceil((locationsData?.total || 0) / 20);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load stock locations. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Stock Locations</h1>
          <p className="text-gray-600">Manage your warehouse and stock locations</p>
        </div>
        <button
          onClick={() => {
            setEditingLocation(null);
            resetForm();
            setIsCreateModalOpen(true);
          }}
          className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Location
        </button>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search locations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Locations List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedLocations.length === locations.length && locations.length > 0}
                onChange={toggleSelectAll}
                className="mr-3"
              />
              <h3 className="text-lg font-medium text-gray-900">
                Stock Locations ({locationsData?.total || 0})
              </h3>
            </div>
            {selectedLocations.length > 0 && (
              <span className="text-sm text-gray-600">
                {selectedLocations.length} selected
              </span>
            )}
          </div>
        </div>

        {locations.length === 0 ? (
          <div className="text-center py-12">
            <BuildingStorefrontIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No stock locations found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'No locations match your search criteria.' : 'Get started by adding your first stock location.'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => {
                  setEditingLocation(null);
                  resetForm();
                  setIsCreateModalOpen(true);
                }}
                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
              >
                Add Location
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {locations.map((location: StockLocation) => (
              <div key={location.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedLocations.includes(location.id)}
                      onChange={() => toggleLocationSelection(location.id)}
                      className="mr-3"
                    />
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                        <BuildingStorefrontIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="font-medium text-gray-900">{location.name}</h4>
                        </div>
                        
                        {location.address && (
                          <div className="flex items-start space-x-1 mt-2">
                            <MapPinIcon className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                            <div className="text-sm text-gray-500 whitespace-pre-line">
                              {formatAddress(location.address)}
                            </div>
                          </div>
                        )}
                        
                        <div className="flex items-center space-x-6 mt-3 text-sm">
                          {location.item_count !== undefined && (
                            <div className="flex items-center">
                              <CubeIcon className="h-4 w-4 mr-1 text-gray-400" />
                              <span className="text-gray-600">{location.item_count} items</span>
                            </div>
                          )}
                          
                          {location.total_inventory !== undefined && (
                            <div className="flex items-center">
                              <ChartBarIcon className="h-4 w-4 mr-1 text-gray-400" />
                              <span className="text-gray-600">{location.total_inventory} total stock</span>
                            </div>
                          )}
                          
                          {location.low_stock_items !== undefined && location.low_stock_items > 0 && (
                            <div className="flex items-center">
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                {location.low_stock_items} low stock
                              </span>
                            </div>
                          )}
                          
                          <span className="text-gray-400">
                            Created {new Date(location.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEdit(location)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteLocation(location.id)}
                      disabled={deleteLocationMutation.isPending}
                      className="text-gray-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {(currentPage - 1) * 20 + 1} to {Math.min(currentPage * 20, locationsData?.total || 0)} of {locationsData?.total || 0} locations
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create/Edit Location Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-96 overflow-y-auto">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {editingLocation ? 'Edit Stock Location' : 'Create New Stock Location'}
            </h3>
            <form onSubmit={editingLocation ? handleUpdateLocation : handleCreateLocation}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter location name"
                    required
                  />
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Address Information (Optional)</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Company
                      </label>
                      <input
                        type="text"
                        value={formData.address?.company || ''}
                        onChange={(e) => setFormData({
                          ...formData,
                          address: { ...formData.address!, company: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Company name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone
                      </label>
                      <input
                        type="tel"
                        value={formData.address?.phone || ''}
                        onChange={(e) => setFormData({
                          ...formData,
                          address: { ...formData.address!, phone: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Phone number"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Address Line 1
                      </label>
                      <input
                        type="text"
                        value={formData.address?.address_1 || ''}
                        onChange={(e) => setFormData({
                          ...formData,
                          address: { ...formData.address!, address_1: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Street address"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Address Line 2
                      </label>
                      <input
                        type="text"
                        value={formData.address?.address_2 || ''}
                        onChange={(e) => setFormData({
                          ...formData,
                          address: { ...formData.address!, address_2: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Apartment, suite, unit, etc."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        City
                      </label>
                      <input
                        type="text"
                        value={formData.address?.city || ''}
                        onChange={(e) => setFormData({
                          ...formData,
                          address: { ...formData.address!, city: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="City"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        State/Province
                      </label>
                      <input
                        type="text"
                        value={formData.address?.province || ''}
                        onChange={(e) => setFormData({
                          ...formData,
                          address: { ...formData.address!, province: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="State or Province"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Postal Code
                      </label>
                      <input
                        type="text"
                        value={formData.address?.postal_code || ''}
                        onChange={(e) => setFormData({
                          ...formData,
                          address: { ...formData.address!, postal_code: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Postal/ZIP code"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Country Code
                      </label>
                      <input
                        type="text"
                        value={formData.address?.country_code || ''}
                        onChange={(e) => setFormData({
                          ...formData,
                          address: { ...formData.address!, country_code: e.target.value.toUpperCase() }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="US, CA, GB, etc."
                        maxLength={2}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setIsCreateModalOpen(false);
                    setEditingLocation(null);
                    resetForm();
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createLocationMutation.isPending || updateLocationMutation.isPending}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {createLocationMutation.isPending || updateLocationMutation.isPending 
                    ? 'Saving...' 
                    : editingLocation 
                      ? 'Update Location' 
                      : 'Create Location'
                  }
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockLocations; 