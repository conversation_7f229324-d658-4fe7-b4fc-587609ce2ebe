@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles for admin interface */
body {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  margin: 0;
  padding: 0;
  background-color: #f3f4f6;
  color: #111827;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  text-align: left;
  display: block;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

/* Custom scrollbar for file manager and content areas */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animation for file upload */
@keyframes uploadPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.upload-animation {
  animation: uploadPulse 1.5s ease-in-out infinite;
}

/* Preview modal background blur */
.modal-backdrop {
  backdrop-filter: blur(4px);
}

/* Focus styles for accessibility */
.focus-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading skeleton animation */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.skeleton {
  animation: shimmer 1.2s ease-in-out infinite;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
}

/* Toast notification custom styles */
.toast-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.toast-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* File type specific colors */
.file-icon-image {
  color: #8b5cf6;
}

.file-icon-video {
  color: #ef4444;
}

.file-icon-audio {
  color: #f59e0b;
}

.file-icon-document {
  color: #3b82f6;
}

.file-icon-archive {
  color: #6b7280;
}

.file-icon-code {
  color: #10b981;
}

/* Journal editor enhancements */
.journal-editor textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  tab-size: 2;
}

/* Status badge improvements */
.status-published {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-draft {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border: 1px solid #fde68a;
}

/* Responsive table improvements */
@media (max-width: 768px) {
  .responsive-table {
    font-size: 0.875rem;
  }
  
  .responsive-table th,
  .responsive-table td {
    padding: 0.5rem;
  }
}

/* Drag and drop styles */
.drag-over {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.drag-active {
  border-color: #1d4ed8;
  background-color: #dbeafe;
} 