import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import { 
  ChevronLeftIcon,
  CreditCardIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CogIcon,
  CurrencyDollarIcon,
  BuildingStorefrontIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';

interface PaymentProvider {
  id: string;
  is_enabled: boolean;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

const SettingsPayments: React.FC = () => {
  const queryClient = useQueryClient();
  const [isAddingProvider, setIsAddingProvider] = useState(false);
  const [editingProvider, setEditingProvider] = useState<PaymentProvider | null>(null);
  const [newProvider, setNewProvider] = useState({
    id: '',
    is_enabled: true,
    description: '',
    webhook_url: '',
    api_key: '',
    secret_key: '',
  });

  // Fetch payment providers data
  const { data: paymentData, isLoading } = useQuery({
    queryKey: ['admin-payment-providers'],
    queryFn: () => api.get('/admin/api/payment-providers').then(res => res.data),
  });

  // Create payment provider mutation
  const createProviderMutation = useMutation({
    mutationFn: (data: any) => api.post('/admin/api/payment-providers', data),
    onSuccess: () => {
      toast.success('Payment provider created successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-payment-providers'] });
      setIsAddingProvider(false);
      setNewProvider({ id: '', is_enabled: true, description: '', webhook_url: '', api_key: '', secret_key: '' });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create payment provider');
    },
  });

  // Update payment provider mutation
  const updateProviderMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      api.put(`/admin/api/payment-providers/${id}`, data),
    onSuccess: () => {
      toast.success('Payment provider updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-payment-providers'] });
      setEditingProvider(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update payment provider');
    },
  });

  // Delete payment provider mutation
  const deleteProviderMutation = useMutation({
    mutationFn: (providerId: string) => api.delete(`/admin/api/payment-providers/${providerId}`),
    onSuccess: () => {
      toast.success('Payment provider deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-payment-providers'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete payment provider');
    },
  });

  const handleCreateProvider = () => {
    if (!newProvider.id.trim()) {
      toast.error('Provider ID is required');
      return;
    }

    createProviderMutation.mutate({
      id: newProvider.id,
      is_enabled: newProvider.is_enabled ? 1 : 0,
      metadata: { 
        description: newProvider.description,
        webhook_url: newProvider.webhook_url,
        api_key: newProvider.api_key,
        secret_key: newProvider.secret_key,
      },
    });
  };

  const handleUpdateProvider = (provider: PaymentProvider) => {
    updateProviderMutation.mutate({
      id: provider.id,
      data: {
        is_enabled: provider.is_enabled ? 1 : 0,
        metadata: provider.metadata,
      },
    });
  };

  const handleDeleteProvider = (providerId: string) => {
    if (window.confirm('Are you sure you want to delete this payment provider?')) {
      deleteProviderMutation.mutate(providerId);
    }
  };

  const getProviderIcon = (providerId: string) => {
    const icons: Record<string, React.ReactNode> = {
      stripe: <CreditCardIcon className="h-6 w-6 text-blue-600" />,
      paypal: <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" />,
      square: <BuildingStorefrontIcon className="h-6 w-6 text-green-600" />,
      manual: <CogIcon className="h-6 w-6 text-gray-600" />,
    };
    return icons[providerId.toLowerCase()] || <CreditCardIcon className="h-6 w-6 text-gray-600" />;
  };

  const getProviderName = (providerId: string) => {
    const names: Record<string, string> = {
      stripe: 'Stripe',
      paypal: 'PayPal',
      square: 'Square',
      manual: 'Manual Payment',
    };
    return names[providerId.toLowerCase()] || providerId;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const paymentProviders = paymentData?.data?.paymentProviders || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/admin/settings"
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Settings
          </Link>
          <span className="text-gray-400">/</span>
          <div className="flex items-center space-x-2">
            <CreditCardIcon className="h-5 w-5 text-blue-600" />
            <h1 className="text-2xl font-semibold text-gray-900">Payment Providers</h1>
          </div>
        </div>
        <button
          onClick={() => setIsAddingProvider(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Provider</span>
        </button>
      </div>

      {/* Description */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <p className="text-blue-800 text-sm">
          Configure payment providers to accept payments from your customers. Each provider can be enabled or disabled as needed.
        </p>
      </div>

      {/* Add Provider Form */}
      {isAddingProvider && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Payment Provider</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Provider ID *
              </label>
              <select
                value={newProvider.id}
                onChange={(e) => setNewProvider({ ...newProvider, id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a provider</option>
                <option value="stripe">Stripe</option>
                <option value="paypal">PayPal</option>
                <option value="square">Square</option>
                <option value="manual">Manual Payment</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API Key
              </label>
              <input
                type="password"
                value={newProvider.api_key}
                onChange={(e) => setNewProvider({ ...newProvider, api_key: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Provider API key"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Secret Key
              </label>
              <input
                type="password"
                value={newProvider.secret_key}
                onChange={(e) => setNewProvider({ ...newProvider, secret_key: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Provider secret key"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Webhook URL
              </label>
              <input
                type="url"
                value={newProvider.webhook_url}
                onChange={(e) => setNewProvider({ ...newProvider, webhook_url: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://your-domain.com/webhooks/payment"
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={newProvider.description}
                onChange={(e) => setNewProvider({ ...newProvider, description: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Optional description for this payment provider"
              />
            </div>
            <div className="md:col-span-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={newProvider.is_enabled}
                  onChange={(e) => setNewProvider({ ...newProvider, is_enabled: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Enable this provider</span>
              </label>
            </div>
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setIsAddingProvider(false)}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateProvider}
              disabled={createProviderMutation.isPending}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200"
            >
              {createProviderMutation.isPending ? 'Creating...' : 'Create Provider'}
            </button>
          </div>
        </div>
      )}

      {/* Payment Providers List */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Configured Providers</h3>
          <p className="text-sm text-gray-600 mt-1">
            {paymentProviders.length} provider{paymentProviders.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {paymentProviders.length === 0 ? (
          <div className="p-6 text-center">
            <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No payment providers configured</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by adding your first payment provider.
            </p>
            <button
              onClick={() => setIsAddingProvider(true)}
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200"
            >
              Add Provider
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {paymentProviders.map((provider: PaymentProvider) => {
              const isEditing = editingProvider?.id === provider.id;

              return (
                <div key={provider.id} className="p-6">
                  {isEditing ? (
                    <div className="space-y-4">
                      <div>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={editingProvider.is_enabled}
                            onChange={(e) => setEditingProvider({ ...editingProvider, is_enabled: e.target.checked })}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Enable this provider</span>
                        </label>
                      </div>
                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={() => setEditingProvider(null)}
                          className="px-3 py-1 text-gray-600 hover:text-gray-800 transition-colors duration-200 flex items-center space-x-1"
                        >
                          <XMarkIcon className="h-4 w-4" />
                          <span>Cancel</span>
                        </button>
                        <button
                          onClick={() => handleUpdateProvider(editingProvider)}
                          disabled={updateProviderMutation.isPending}
                          className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 transition-colors duration-200 flex items-center space-x-1"
                        >
                          <CheckIcon className="h-4 w-4" />
                          <span>{updateProviderMutation.isPending ? 'Saving...' : 'Save'}</span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          {getProviderIcon(provider.id)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <h4 className="text-lg font-medium text-gray-900">{getProviderName(provider.id)}</h4>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              provider.is_enabled 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {provider.is_enabled ? 'Enabled' : 'Disabled'}
                            </span>
                          </div>
                          
                          <div className="mt-2 flex items-center space-x-4 text-sm text-gray-600">
                            <div className="flex items-center space-x-1">
                              <CogIcon className="h-4 w-4" />
                              <span>Provider ID: {provider.id}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <BuildingStorefrontIcon className="h-4 w-4" />
                              <span>Status: {provider.is_enabled ? 'Active' : 'Inactive'}</span>
                            </div>
                          </div>

                          {provider.metadata?.description && (
                            <div className="mt-2">
                              <p className="text-sm text-gray-600">{provider.metadata.description}</p>
                            </div>
                          )}

                          <div className="mt-2 text-xs text-gray-500">
                            Created {new Date(provider.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => setEditingProvider(provider)}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
                          title="Edit provider"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteProvider(provider.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors duration-200"
                          title="Delete provider"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Payment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CreditCardIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{paymentProviders.length}</div>
              <div className="text-sm text-gray-600">Total Providers</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BuildingStorefrontIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {paymentProviders.filter((p: PaymentProvider) => p.is_enabled).length}
              </div>
              <div className="text-sm text-gray-600">Active Providers</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">€0.00</div>
              <div className="text-sm text-gray-600">Total Processed</div>
            </div>
          </div>
        </div>
      </div>

      {/* Provider Configuration Help */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Provider Setup Guide</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CreditCardIcon className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">Stripe</h4>
                <p className="text-sm text-gray-600">
                  Get your API keys from your Stripe dashboard. Enable webhooks for real-time updates.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CurrencyDollarIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">PayPal</h4>
                <p className="text-sm text-gray-600">
                  Configure your PayPal business account and set up API credentials.
                </p>
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <BuildingStorefrontIcon className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">Square</h4>
                <p className="text-sm text-gray-600">
                  Set up your Square application and obtain sandbox/production tokens.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CogIcon className="h-5 w-5 text-gray-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">Manual Payment</h4>
                <p className="text-sm text-gray-600">
                  Allow manual payment processing for bank transfers or cash payments.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPayments; 