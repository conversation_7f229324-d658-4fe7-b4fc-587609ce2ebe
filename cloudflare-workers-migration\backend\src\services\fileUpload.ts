import { WorkerEnv } from 'handmadein-shared';

export interface UploadResult {
  id: string;
  filename: string;
  url: string;
  size: number;
  mimetype: string;
  metadata?: Record<string, any>;
}

export interface ImageVariant {
  width: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  suffix: string;
}

export class FileUploadService {
  private env: WorkerEnv;
  private allowedMimeTypes: string[];
  private maxFileSize: number;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif',
      'application/pdf',
    ];
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
  }

  async uploadFile(
    file: File | ArrayBuffer,
    filename: string,
    mimetype: string,
    folder: string = 'uploads'
  ): Promise<UploadResult> {
    // Validate file type
    if (!this.allowedMimeTypes.includes(mimetype)) {
      throw new Error(`File type ${mimetype} is not allowed`);
    }

    // Get file buffer
    const buffer = file instanceof File ? await file.arrayBuffer() : file;
    
    // Validate file size
    if (buffer.byteLength > this.maxFileSize) {
      throw new Error(`File size exceeds maximum allowed size of ${this.maxFileSize / 1024 / 1024}MB`);
    }

    // Generate unique filename
    const fileId = crypto.randomUUID();
    const extension = this.getFileExtension(filename);
    const uniqueFilename = `${fileId}${extension}`;
    const key = `${folder}/${uniqueFilename}`;

    try {
      // Upload to R2
      await this.env.UPLOADS.put(key, buffer, {
        httpMetadata: {
          contentType: mimetype,
        },
        customMetadata: {
          originalName: filename,
          uploadedAt: new Date().toISOString(),
        },
      });

      // Generate public URL
      const url = `${this.env.FRONTEND_URL}/api/files/${key}`;

      return {
        id: fileId,
        filename: uniqueFilename,
        url,
        size: buffer.byteLength,
        mimetype,
        metadata: {
          originalName: filename,
          folder,
        },
      };
    } catch (error) {
      console.error('File upload failed:', error);
      throw new Error('Failed to upload file');
    }
  }

  async uploadImage(
    file: File | ArrayBuffer,
    filename: string,
    folder: string = 'images',
    generateVariants: boolean = true
  ): Promise<UploadResult & { variants?: UploadResult[] }> {
    const mimetype = this.getMimeTypeFromFilename(filename);
    
    if (!mimetype.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    // Upload original image
    const result = await this.uploadFile(file, filename, mimetype, folder);

    if (!generateVariants) {
      return result;
    }

    // Generate image variants
    const variants = await this.generateImageVariants(file, filename, folder);

    return {
      ...result,
      variants,
    };
  }

  async generateImageVariants(
    file: File | ArrayBuffer,
    filename: string,
    folder: string
  ): Promise<UploadResult[]> {
    const variants: ImageVariant[] = [
      { width: 150, height: 150, suffix: '_thumb', quality: 80 },
      { width: 400, height: 400, suffix: '_small', quality: 85 },
      { width: 800, suffix: '_medium', quality: 90 },
      { width: 1200, suffix: '_large', quality: 95 },
    ];

    const results: UploadResult[] = [];
    const buffer = file instanceof File ? await file.arrayBuffer() : file;

    for (const variant of variants) {
      try {
        // In a real implementation, you'd use an image processing library
        // For now, we'll just upload the original with different names
        const fileId = crypto.randomUUID();
        const extension = this.getFileExtension(filename);
        const variantFilename = `${fileId}${variant.suffix}${extension}`;
        const key = `${folder}/${variantFilename}`;

        await this.env.UPLOADS.put(key, buffer, {
          httpMetadata: {
            contentType: this.getMimeTypeFromFilename(filename),
          },
          customMetadata: {
            originalName: filename,
            variant: variant.suffix,
            width: variant.width.toString(),
            height: variant.height?.toString() || '',
            uploadedAt: new Date().toISOString(),
          },
        });

        const url = `${this.env.FRONTEND_URL}/api/files/${key}`;

        results.push({
          id: fileId,
          filename: variantFilename,
          url,
          size: buffer.byteLength,
          mimetype: this.getMimeTypeFromFilename(filename),
          metadata: {
            originalName: filename,
            variant: variant.suffix,
            width: variant.width,
            height: variant.height,
          },
        });
      } catch (error) {
        console.error(`Failed to generate variant ${variant.suffix}:`, error);
      }
    }

    return results;
  }

  async getFile(key: string): Promise<ArrayBuffer | null> {
    try {
      const object = await this.env.UPLOADS.get(key);
      if (!object) {
        return null;
      }
      return await object.arrayBuffer();
    } catch (error) {
      console.error('Failed to get file:', error);
      return null;
    }
  }

  async deleteFile(key: string): Promise<boolean> {
    try {
      await this.env.UPLOADS.delete(key);
      return true;
    } catch (error) {
      console.error('Failed to delete file:', error);
      return false;
    }
  }

  async listFiles(folder: string, limit: number = 100): Promise<string[]> {
    try {
      const list = await this.env.UPLOADS.list({
        prefix: `${folder}/`,
        limit,
      });
      
      return list.objects.map(obj => obj.key);
    } catch (error) {
      console.error('Failed to list files:', error);
      return [];
    }
  }

  private getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot) : '';
  }

  private getMimeTypeFromFilename(filename: string): string {
    const extension = this.getFileExtension(filename).toLowerCase();
    
    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.webp': 'image/webp',
      '.gif': 'image/gif',
      '.pdf': 'application/pdf',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  }

  async uploadProductImage(
    file: File | ArrayBuffer,
    filename: string,
    productId: string
  ): Promise<UploadResult & { variants?: UploadResult[] }> {
    const folder = `products/${productId}`;
    return this.uploadImage(file, filename, folder, true);
  }

  async uploadCollectionImage(
    file: File | ArrayBuffer,
    filename: string,
    collectionId: string
  ): Promise<UploadResult> {
    const folder = `collections/${collectionId}`;
    return this.uploadImage(file, filename, folder, false);
  }

  async uploadJournalImage(
    file: File | ArrayBuffer,
    filename: string,
    entryId: string
  ): Promise<UploadResult> {
    const folder = `journal/${entryId}`;
    return this.uploadImage(file, filename, folder, false);
  }

  async uploadAvatar(
    file: File | ArrayBuffer,
    filename: string,
    userId: string
  ): Promise<UploadResult> {
    const folder = `avatars/${userId}`;
    return this.uploadImage(file, filename, folder, false);
  }
} 