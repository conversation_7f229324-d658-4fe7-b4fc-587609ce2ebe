import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { 
  ChevronLeftIcon, 
  PencilIcon, 
  TrashIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  CalendarIcon,
  ShoppingBagIcon
} from '@heroicons/react/24/outline';

interface Customer {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  date_of_birth?: string;
  has_account: boolean;
  groups?: Array<{ id: string; name: string }>;
  addresses?: Array<{
    id: string;
    first_name: string;
    last_name: string;
    address_1: string;
    address_2?: string;
    city: string;
    postal_code: string;
    country: string;
    is_default_shipping: boolean;
    is_default_billing: boolean;
  }>;
  orders_count: number;
  total_spent: number;
  created_at: string;
  updated_at: string;
}

interface Order {
  id: string;
  order_number: string;
  status: string;
  total: number;
  created_at: string;
}

const CustomerDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { data: customer, isLoading, error } = useQuery<Customer>({
    queryKey: ['customer', id],
    queryFn: async () => {
      const response = await fetch(`/api/admin/customers/${id}`);
      if (!response.ok) throw new Error('Failed to fetch customer');
      return response.json();
    },
    enabled: Boolean(id)
  });

  const { data: customerOrders } = useQuery<Order[]>({
    queryKey: ['customer-orders', id],
    queryFn: async () => {
      const response = await fetch(`/api/admin/customers/${id}/orders`);
      if (!response.ok) throw new Error('Failed to fetch customer orders');
      return response.json();
    },
    enabled: Boolean(id)
  });

  const deleteCustomerMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/admin/customers/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) throw new Error('Failed to delete customer');
    },
    onSuccess: () => {
      toast.success('Customer deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      navigate('/customers');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    }
  });

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      deleteCustomerMutation.mutate();
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ro-RO', {
      style: 'currency',
      currency: 'RON'
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const classes = {
      pending: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      shipped: 'bg-purple-100 text-purple-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !customer) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Customer not found</h3>
        <p className="mt-2 text-gray-600">The customer you're looking for doesn't exist.</p>
        <button
          onClick={() => navigate('/customers')}
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Back to Customers
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-3">
          <button
            onClick={() => navigate('/customers')}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Back to Customers
          </button>
        </div>
        
        <div className="mt-4 flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {customer.first_name} {customer.last_name}
            </h1>
            <p className="text-lg text-gray-600 mt-1">{customer.email}</p>
            <div className="flex items-center space-x-4 mt-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                customer.has_account 
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {customer.has_account ? 'Registered' : 'Guest'}
              </span>
              <span className="text-sm text-gray-500">
                {customer.orders_count} orders • {formatCurrency(customer.total_spent)} spent
              </span>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => navigate(`/customers/${id}/edit`)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit
            </button>
            
            <button
              onClick={handleDelete}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Recent Orders */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium text-gray-900">Recent Orders</h2>
              <button
                onClick={() => navigate(`/orders?customer=${id}`)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                View all orders →
              </button>
            </div>
            
            {customerOrders && customerOrders.length > 0 ? (
              <div className="overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {customerOrders.slice(0, 5).map((order) => (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => navigate(`/orders/${order.id}`)}
                            className="text-sm font-medium text-blue-600 hover:text-blue-800"
                          >
                            #{order.order_number}
                          </button>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(order.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(order.total)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(order.created_at).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-6">
                <ShoppingBagIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No orders</h3>
                <p className="mt-1 text-sm text-gray-500">This customer hasn't placed any orders yet.</p>
              </div>
            )}
          </div>

          {/* Addresses */}
          {customer.addresses && customer.addresses.length > 0 && (
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Addresses</h2>
              <div className="grid gap-6 md:grid-cols-2">
                {customer.addresses.map((address) => (
                  <div key={address.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-medium text-gray-900">
                        {address.first_name} {address.last_name}
                      </h3>
                      <div className="flex space-x-2">
                        {address.is_default_shipping && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Default Shipping
                          </span>
                        )}
                        {address.is_default_billing && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Default Billing
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">
                      <div>{address.address_1}</div>
                      {address.address_2 && <div>{address.address_2}</div>}
                      <div>{address.city}, {address.postal_code}</div>
                      <div>{address.country}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Info */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Customer Information</h2>
            <dl className="space-y-4">
              <div className="flex items-start">
                <dt className="flex-shrink-0">
                  <UserIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                </dt>
                <dd className="ml-3">
                  <div className="text-sm font-medium text-gray-900">
                    {customer.first_name} {customer.last_name}
                  </div>
                  <div className="text-sm text-gray-500">Customer</div>
                </dd>
              </div>

              <div className="flex items-start">
                <dt className="flex-shrink-0">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                </dt>
                <dd className="ml-3">
                  <div className="text-sm text-gray-900">{customer.email}</div>
                  <div className="text-sm text-gray-500">Email</div>
                </dd>
              </div>

              {customer.phone && (
                <div className="flex items-start">
                  <dt className="flex-shrink-0">
                    <PhoneIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                  </dt>
                  <dd className="ml-3">
                    <div className="text-sm text-gray-900">{customer.phone}</div>
                    <div className="text-sm text-gray-500">Phone</div>
                  </dd>
                </div>
              )}

              {customer.date_of_birth && (
                <div className="flex items-start">
                  <dt className="flex-shrink-0">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                  </dt>
                  <dd className="ml-3">
                    <div className="text-sm text-gray-900">
                      {new Date(customer.date_of_birth).toLocaleDateString()}
                    </div>
                    <div className="text-sm text-gray-500">Date of Birth</div>
                  </dd>
                </div>
              )}

              <div className="flex items-start">
                <dt className="flex-shrink-0">
                  <CalendarIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                </dt>
                <dd className="ml-3">
                  <div className="text-sm text-gray-900">
                    {new Date(customer.created_at).toLocaleDateString()}
                  </div>
                  <div className="text-sm text-gray-500">Customer Since</div>
                </dd>
              </div>
            </dl>
          </div>

          {/* Customer Groups */}
          {customer.groups && customer.groups.length > 0 && (
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Customer Groups</h2>
              <div className="space-y-2">
                {customer.groups.map((group) => (
                  <div key={group.id} className="text-sm text-gray-900">
                    {group.name}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Statistics */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Statistics</h2>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">Total Orders</dt>
                <dd className="text-2xl font-bold text-gray-900">{customer.orders_count}</dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Total Spent</dt>
                <dd className="text-2xl font-bold text-gray-900">
                  {formatCurrency(customer.total_spent)}
                </dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Average Order Value</dt>
                <dd className="text-2xl font-bold text-gray-900">
                  {customer.orders_count > 0 
                    ? formatCurrency(customer.total_spent / customer.orders_count)
                    : formatCurrency(0)
                  }
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerDetail; 