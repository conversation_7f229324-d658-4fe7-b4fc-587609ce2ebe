import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  UserGroupIcon,
  TagIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface CustomerGroup {
  id: string;
  name: string;
  description?: string;
  discount_percentage: number;
  customer_count?: number;
  total_orders?: number;
  total_spent?: number;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface CreateGroupData {
  name: string;
  description?: string;
  discount_percentage: number;
  metadata?: any;
}

const CustomerGroups: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState<CustomerGroup | null>(null);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [formData, setFormData] = useState<CreateGroupData>({
    name: '',
    description: '',
    discount_percentage: 0,
  });

  const queryClient = useQueryClient();

  // Fetch customer groups
  const { data: groupsData, isLoading, error } = useQuery({
    queryKey: ['customer-groups', searchTerm],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      
      const response = await api.get(`${endpoints.customerGroups.list}?${params}`);
      return response.data;
    },
  });

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: async (data: CreateGroupData) => {
      return api.post(endpoints.customerGroups.create, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customer-groups'] });
      toast.success('Customer group created successfully');
      setIsCreateModalOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create customer group');
    },
  });

  // Update group mutation
  const updateGroupMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: CreateGroupData }) => {
      return api.put(endpoints.customerGroups.update(id), data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customer-groups'] });
      toast.success('Customer group updated successfully');
      setEditingGroup(null);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update customer group');
    },
  });

  // Delete group mutation
  const deleteGroupMutation = useMutation({
    mutationFn: async (id: string) => {
      return api.delete(endpoints.customerGroups.delete(id));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customer-groups'] });
      toast.success('Customer group deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete customer group');
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: async (groupIds: string[]) => {
      return api.post(endpoints.customerGroups.bulkDelete, { groupIds });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customer-groups'] });
      toast.success(`${selectedGroups.length} customer groups deleted successfully`);
      setSelectedGroups([]);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete customer groups');
    },
  });

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      discount_percentage: 0,
    });
  };

  const handleCreateGroup = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      toast.error('Group name is required');
      return;
    }
    createGroupMutation.mutate(formData);
  };

  const handleUpdateGroup = (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingGroup || !formData.name.trim()) {
      toast.error('Group name is required');
      return;
    }
    updateGroupMutation.mutate({ id: editingGroup.id, data: formData });
  };

  const handleDeleteGroup = (id: string) => {
    if (confirm('Are you sure you want to delete this customer group? This action cannot be undone.')) {
      deleteGroupMutation.mutate(id);
    }
  };

  const handleBulkDelete = () => {
    if (selectedGroups.length === 0) return;
    if (confirm(`Are you sure you want to delete ${selectedGroups.length} customer groups? This action cannot be undone.`)) {
      bulkDeleteMutation.mutate(selectedGroups);
    }
  };

  const handleEdit = (group: CustomerGroup) => {
    setEditingGroup(group);
    setFormData({
      name: group.name,
      description: group.description || '',
      discount_percentage: group.discount_percentage,
      metadata: group.metadata,
    });
    setIsCreateModalOpen(true);
  };

  const toggleGroupSelection = (groupId: string) => {
    setSelectedGroups(prev =>
      prev.includes(groupId)
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedGroups.length === groupsData?.groups?.length) {
      setSelectedGroups([]);
    } else {
      setSelectedGroups(groupsData?.groups?.map((group: CustomerGroup) => group.id) || []);
    }
  };

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const groups = groupsData?.groups || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load customer groups. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Customer Groups</h1>
          <p className="text-gray-600">Organize customers into groups and provide group-specific discounts</p>
        </div>
        <button
          onClick={() => {
            setEditingGroup(null);
            resetForm();
            setIsCreateModalOpen(true);
          }}
          className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Group
        </button>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search customer groups..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {selectedGroups.length > 0 && (
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-600">
              {selectedGroups.length} selected
            </span>
            <button
              onClick={handleBulkDelete}
              disabled={bulkDeleteMutation.isPending}
              className="flex items-center text-red-600 hover:text-red-800 text-sm"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete Selected
            </button>
          </div>
        )}
      </div>

      {/* Customer Groups List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={selectedGroups.length === groups.length && groups.length > 0}
              onChange={toggleSelectAll}
              className="mr-3"
            />
            <h3 className="text-lg font-medium text-gray-900">All Groups ({groups.length})</h3>
          </div>
        </div>

        {groups.length === 0 ? (
          <div className="text-center py-12">
            <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No customer groups found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'No groups match your search.' : 'Get started by creating your first customer group.'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => {
                  setEditingGroup(null);
                  resetForm();
                  setIsCreateModalOpen(true);
                }}
                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
              >
                Add Group
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {groups.map((group: CustomerGroup) => (
              <div key={group.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedGroups.includes(group.id)}
                      onChange={() => toggleGroupSelection(group.id)}
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h4 className="font-medium text-gray-900">{group.name}</h4>
                        {group.discount_percentage > 0 && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {group.discount_percentage}% discount
                          </span>
                        )}
                      </div>
                      {group.description && (
                        <p className="text-sm text-gray-500 mt-1">{group.description}</p>
                      )}
                      
                      <div className="flex items-center space-x-6 mt-2 text-sm text-gray-500">
                        <div className="flex items-center">
                          <UserGroupIcon className="h-4 w-4 mr-1" />
                          <span>{group.customer_count || 0} customers</span>
                        </div>
                        {group.total_orders !== undefined && (
                          <div className="flex items-center">
                            <ChartBarIcon className="h-4 w-4 mr-1" />
                            <span>{group.total_orders} orders</span>
                          </div>
                        )}
                        {group.total_spent !== undefined && (
                          <div className="flex items-center">
                            <TagIcon className="h-4 w-4 mr-1" />
                            <span>{formatCurrency(group.total_spent)}</span>
                          </div>
                        )}
                        <span>Created {new Date(group.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEdit(group)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteGroup(group.id)}
                      disabled={deleteGroupMutation.isPending}
                      className="text-gray-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create/Edit Group Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {editingGroup ? 'Edit Customer Group' : 'Create New Customer Group'}
            </h3>
            <form onSubmit={editingGroup ? handleUpdateGroup : handleCreateGroup}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Group Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter group name"
                    required
                    autoFocus
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe this customer group"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Discount Percentage
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      value={formData.discount_percentage}
                      onChange={(e) => setFormData({ ...formData, discount_percentage: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="0.00"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Percentage discount applied to all orders for customers in this group
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setIsCreateModalOpen(false);
                    setEditingGroup(null);
                    resetForm();
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createGroupMutation.isPending || updateGroupMutation.isPending}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {createGroupMutation.isPending || updateGroupMutation.isPending 
                    ? 'Saving...' 
                    : editingGroup 
                      ? 'Update Group' 
                      : 'Create Group'
                  }
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerGroups; 