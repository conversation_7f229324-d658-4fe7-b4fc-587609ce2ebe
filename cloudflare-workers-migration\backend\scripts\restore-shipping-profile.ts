import { DatabaseService } from '../src/services/database';

interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

const TARGET_SHIPPING_PROFILE = 'sp_01JTXB95KCCZT30EWN692SN9RD';
const TARGET_SHIPPING_OPTION = 'so_01JTXBA1J91ZM38P55CM6MQM2F';

/**
 * Script to restore a deleted shipping profile by setting deleted_at to null
 */
export async function restoreShippingProfile(env: WorkerEnv) {
  const db = new DatabaseService(env);
  
  try {
    console.log('🔧 Restoring shipping profile...');
    console.log(`Target shipping profile: ${TARGET_SHIPPING_PROFILE}`);
    
    // First, check the current state of the shipping profile
    const currentProfile = await env.DB.prepare(`
      SELECT * FROM shipping_profile WHERE id = ?
    `).bind(TARGET_SHIPPING_PROFILE).first();
    
    if (!currentProfile) {
      throw new Error(`Shipping profile ${TARGET_SHIPPING_PROFILE} not found in database`);
    }
    
    console.log(`📋 Current profile state:`);
    console.log(`   ID: ${currentProfile.id}`);
    console.log(`   Name: ${currentProfile.name || 'Unknown'}`);
    console.log(`   Type: ${currentProfile.type || 'Unknown'}`);
    console.log(`   Deleted At: ${currentProfile.deleted_at || 'null (active)'}`);
    
    if (!currentProfile.deleted_at) {
      console.log('✅ Profile is already active (deleted_at is null)');
      return {
        success: true,
        message: 'Profile is already active',
        wasAlreadyActive: true,
      };
    }
    
    // Restore the profile by setting deleted_at to null
    await env.DB.prepare(`
      UPDATE shipping_profile 
      SET deleted_at = NULL, updated_at = ?
      WHERE id = ?
    `).bind(
      new Date().toISOString(),
      TARGET_SHIPPING_PROFILE
    ).run();
    
    console.log('✅ Successfully restored shipping profile');
    console.log(`   Profile ${TARGET_SHIPPING_PROFILE} is now active`);
    
    return {
      success: true,
      message: 'Shipping profile restored successfully',
      profileId: TARGET_SHIPPING_PROFILE,
      wasAlreadyActive: false,
    };
    
  } catch (error) {
    console.error('💥 Failed to restore shipping profile:', error);
    throw error;
  }
}

/**
 * Script to restore a deleted shipping option by setting deleted_at to null
 */
export async function restoreShippingOption(env: WorkerEnv) {
  const db = new DatabaseService(env);
  
  try {
    console.log('🔧 Restoring shipping option...');
    console.log(`Target shipping option: ${TARGET_SHIPPING_OPTION}`);
    
    // First, check the current state of the shipping option
    const currentOption = await env.DB.prepare(`
      SELECT * FROM shipping_option WHERE id = ?
    `).bind(TARGET_SHIPPING_OPTION).first();
    
    if (!currentOption) {
      throw new Error(`Shipping option ${TARGET_SHIPPING_OPTION} not found in database`);
    }
    
    console.log(`📋 Current option state:`);
    console.log(`   ID: ${currentOption.id}`);
    console.log(`   Name: ${currentOption.name || 'Unknown'}`);
    console.log(`   Shipping Profile ID: ${currentOption.shipping_profile_id || 'Unknown'}`);
    console.log(`   Service Zone ID: ${currentOption.service_zone_id || 'Unknown'}`);
    console.log(`   Deleted At: ${currentOption.deleted_at || 'null (active)'}`);
    
    if (!currentOption.deleted_at) {
      console.log('✅ Option is already active (deleted_at is null)');
      return {
        success: true,
        message: 'Option is already active',
        wasAlreadyActive: true,
      };
    }
    
    // Restore the option by setting deleted_at to null
    await env.DB.prepare(`
      UPDATE shipping_option 
      SET deleted_at = NULL, updated_at = ?
      WHERE id = ?
    `).bind(
      new Date().toISOString(),
      TARGET_SHIPPING_OPTION
    ).run();
    
    console.log('✅ Successfully restored shipping option');
    console.log(`   Option ${TARGET_SHIPPING_OPTION} is now active`);
    
    return {
      success: true,
      message: 'Shipping option restored successfully',
      optionId: TARGET_SHIPPING_OPTION,
      wasAlreadyActive: false,
    };
    
  } catch (error) {
    console.error('💥 Failed to restore shipping option:', error);
    throw error;
  }
}

/**
 * Script to restore both shipping profile and shipping option
 */
export async function restoreShippingEntities(env: WorkerEnv) {
  try {
    console.log('🔧 Restoring shipping profile and option...');
    
    const profileResult = await restoreShippingProfile(env);
    const optionResult = await restoreShippingOption(env);
    
    return {
      success: true,
      message: 'Both shipping entities processed',
      profile: profileResult,
      option: optionResult,
    };
    
  } catch (error) {
    console.error('💥 Failed to restore shipping entities:', error);
    throw error;
  }
}

export default {
  restoreShippingProfile,
  restoreShippingOption,
  restoreShippingEntities,
  TARGET_SHIPPING_PROFILE,
  TARGET_SHIPPING_OPTION,
}; 