import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { Context } from 'hono';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Utility functions
function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error(error);
  return c.json({ success: false, error: message, details: error.message }, 500);
}

function safeParseJson(value: any): any {
  if (!value) return null;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }
  return value;
}

// Helper function to enrich order with related data
async function enrichOrder(order: any, db: D1Database): Promise<any> {
  const enriched = { ...order };

  // Parse JSON fields
  enriched.metadata = safeParseJson(enriched.metadata) || {};
  enriched.billing_address = safeParseJson(enriched.billing_address) || {};
  enriched.shipping_address = safeParseJson(enriched.shipping_address) || {};

  // Get order items with product and variant details
  const itemsQuery = `
    SELECT 
      oi.id, oi.order_id, oi.variant_id, oi.quantity, oi.unit_price, oi.total_price,
      oi.title as item_title, oi.thumbnail as item_thumbnail, oi.metadata,
      pv.id as variant_id, pv.title as variant_title, pv.sku, pv.barcode,
      pv.option_values, pv.manage_inventory, pv.allow_backorder, pv.inventory_quantity,
      p.id as product_id, p.handle as product_handle, p.thumbnail as product_thumbnail,
      pt.title as product_title, pt.subtitle as product_subtitle, pt.description as product_description
    FROM order_items oi
    LEFT JOIN product_variants pv ON oi.variant_id = pv.id
    LEFT JOIN products p ON pv.product_id = p.id
    LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
    WHERE oi.order_id = ? AND oi.deleted_at IS NULL
    ORDER BY oi.created_at ASC
  `;

  const items = await db.prepare(itemsQuery).bind(order.id).all();

  enriched.items = (items.results || []).map((item: any) => {
    const enrichedItem = { ...item };
    
    // Parse metadata and option values
    enrichedItem.metadata = safeParseJson(enrichedItem.metadata) || {};
    enrichedItem.option_values = safeParseJson(enrichedItem.option_values) || [];
    
    // Structure variant data for frontend compatibility
    enrichedItem.variant = {
      id: item.variant_id,
      title: item.variant_title,
      sku: item.sku,
      barcode: item.barcode,
      option_values: enrichedItem.option_values,
      manage_inventory: item.manage_inventory,
      allow_backorder: item.allow_backorder,
      inventory_quantity: item.inventory_quantity,
      product: {
        id: item.product_id,
        handle: item.product_handle,
        title: item.product_title,
        subtitle: item.product_subtitle,
        description: item.product_description,
        thumbnail: item.product_thumbnail
      }
    };

    return enrichedItem;
  });

  // Get customer details if exists
  if (order.customer_id) {
    const customerQuery = `
      SELECT id, email, first_name, last_name, phone, has_account, metadata
      FROM customers 
      WHERE id = ? AND deleted_at IS NULL
    `;
    const customer = await db.prepare(customerQuery).bind(order.customer_id).first();
    if (customer) {
      enriched.customer = {
        ...customer,
        metadata: safeParseJson(customer.metadata) || {}
      };
    }
  }

  // Get region details
  if (order.region_id) {
    const regionQuery = `SELECT * FROM regions WHERE id = ?`;
    const region = await db.prepare(regionQuery).bind(order.region_id).first();
    if (region) {
      enriched.region = region;
    }
  }

  // Get payments
  const paymentsQuery = `
    SELECT id, amount, currency_code, status, provider_id, data, metadata, created_at, updated_at
    FROM payments 
    WHERE order_id = ? AND deleted_at IS NULL
    ORDER BY created_at DESC
  `;
  const payments = await db.prepare(paymentsQuery).bind(order.id).all();
  enriched.payments = (payments.results || []).map((payment: any) => ({
    ...payment,
    data: safeParseJson(payment.data) || {},
    metadata: safeParseJson(payment.metadata) || {}
  }));

  // Get fulfillments
  const fulfillmentsQuery = `
    SELECT id, tracking_number, shipped_at, canceled_at, data, metadata, created_at, updated_at
    FROM fulfillments 
    WHERE order_id = ? AND deleted_at IS NULL
    ORDER BY created_at DESC
  `;
  const fulfillments = await db.prepare(fulfillmentsQuery).bind(order.id).all();
  enriched.fulfillments = (fulfillments.results || []).map((fulfillment: any) => ({
    ...fulfillment,
    data: safeParseJson(fulfillment.data) || {},
    metadata: safeParseJson(fulfillment.metadata) || {}
  }));

  return enriched;
}

// POST /store/orders - Create order from cart
app.post('/', async (c) => {
  try {
    const body = await c.req.json();
    const { cart_id, email, payment_method } = body;

    if (!cart_id) {
      return c.json({ success: false, error: 'Cart ID is required' }, 400);
    }

    // Get cart with items
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ? AND deleted_at IS NULL`).bind(cart_id).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Get cart items
    const cartItemsQuery = `
      SELECT cli.*, pv.product_id, p.title as product_title
      FROM cart_line_items cli
      LEFT JOIN product_variants pv ON cli.variant_id = pv.id
      LEFT JOIN products p ON pv.product_id = p.id
      WHERE cli.cart_id = ? AND cli.deleted_at IS NULL
    `;
    const cartItems = await c.env.DB.prepare(cartItemsQuery).bind(cart_id).all();

    if (!cartItems.results || cartItems.results.length === 0) {
      return c.json({ success: false, error: 'Cart is empty' }, 400);
    }

    const now = new Date().toISOString();
    const orderId = crypto.randomUUID();

    // Calculate totals
    const subtotal = (cartItems.results as any[]).reduce((sum, item) => sum + (item.unit_price * item.quantity), 0);
    const shippingTotal = 0; // TODO: Calculate from shipping methods
    const taxTotal = 0; // TODO: Calculate tax
    const total = subtotal + shippingTotal + taxTotal;

    // Create order
    const createOrderQuery = `
      INSERT INTO orders (
        id, display_id, status, fulfillment_status, payment_status, 
        customer_id, email, region_id, currency_code,
        subtotal, shipping_total, tax_total, total,
        billing_address, shipping_address, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    // Generate display ID (simple incrementing number)
    const lastOrderQuery = `SELECT MAX(display_id) as max_id FROM orders`;
    const lastOrder = await c.env.DB.prepare(lastOrderQuery).first();
    const displayId = ((lastOrder as any)?.max_id || 0) + 1;

    await c.env.DB.prepare(createOrderQuery).bind(
      orderId,
      displayId,
      'pending',
      'not_fulfilled',
      'not_paid',
      cart.customer_id,
      email || cart.email,
      cart.region_id,
      cart.currency_code || 'RON',
      subtotal,
      shippingTotal,
      taxTotal,
      total,
      cart.billing_address_id ? JSON.stringify(await c.env.DB.prepare(`SELECT * FROM customer_addresses WHERE id = ?`).bind(cart.billing_address_id).first()) : null,
      cart.shipping_address_id ? JSON.stringify(await c.env.DB.prepare(`SELECT * FROM customer_addresses WHERE id = ?`).bind(cart.shipping_address_id).first()) : null,
      cart.metadata,
      now,
      now
    ).run();

    // Create order items
    for (const cartItem of cartItems.results as any[]) {
      const orderItemId = crypto.randomUUID();
      const createItemQuery = `
        INSERT INTO order_items (
          id, order_id, variant_id, quantity, unit_price, total_price,
          title, thumbnail, metadata, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await c.env.DB.prepare(createItemQuery).bind(
        orderItemId,
        orderId,
        cartItem.variant_id,
        cartItem.quantity,
        cartItem.unit_price,
        cartItem.total_price,
        cartItem.title,
        cartItem.thumbnail,
        cartItem.metadata,
        now,
        now
      ).run();
    }

    // Mark cart as completed
    await c.env.DB.prepare(`UPDATE carts SET completed_at = ?, updated_at = ? WHERE id = ?`).bind(now, now, cart_id).run();

    // Get created order
    const order = await c.env.DB.prepare(`SELECT * FROM orders WHERE id = ?`).bind(orderId).first();
    const enrichedOrder = await enrichOrder(order, c.env.DB);

    return c.json({
      success: true,
      data: enrichedOrder,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create order');
  }
});

// GET /store/orders/:id - Get a specific order
app.get('/:id', async (c) => {
  try {
    const orderId = c.req.param('id');

    // Get order from database
    const order = await c.env.DB.prepare(`SELECT * FROM orders WHERE id = ? AND deleted_at IS NULL`).bind(orderId).first();

    if (!order) {
      return c.json({
        success: false,
        error: 'Order not found',
      }, 404);
    }

    const enrichedOrder = await enrichOrder(order, c.env.DB);

    return c.json({
      success: true,
      data: enrichedOrder,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch order');
  }
});

// GET /store/orders/by-cart/:cart_id - Get order by cart ID
app.get('/by-cart/:cart_id', async (c) => {
  try {
    const cartId = c.req.param('cart_id');

    // Find order created from this cart
    const orderQuery = `
      SELECT o.* FROM orders o
      LEFT JOIN carts c ON c.id = ?
      WHERE c.completed_at IS NOT NULL
      AND o.customer_id = c.customer_id
      AND o.email = c.email
      AND o.deleted_at IS NULL
      ORDER BY o.created_at DESC
      LIMIT 1
    `;

    const order = await c.env.DB.prepare(orderQuery).bind(cartId).first();

    if (!order) {
      return c.json({
        success: false,
        error: 'Order not found for this cart',
      }, 404);
    }

    const enrichedOrder = await enrichOrder(order, c.env.DB);

    return c.json({
      success: true,
      data: enrichedOrder,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch order by cart');
  }
});

// GET /store/orders - List orders for customer (requires email or customer_id)
app.get('/', async (c) => {
  try {
    const email = c.req.query('email');
    const customer_id = c.req.query('customer_id');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = parseInt(c.req.query('offset') || '0');

    if (!email && !customer_id) {
      return c.json({ success: false, error: 'Email or customer ID is required' }, 400);
    }

    let query = `
      SELECT * FROM orders
      WHERE deleted_at IS NULL
    `;
    const params: any[] = [];

    if (customer_id) {
      query += ` AND customer_id = ?`;
      params.push(customer_id);
    } else if (email) {
      query += ` AND email = ?`;
      params.push(email);
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const orders = await c.env.DB.prepare(query).bind(...params).all();

    // Enrich orders with related data
    const enrichedOrders = [];
    for (const order of orders.results || []) {
      const enrichedOrder = await enrichOrder(order, c.env.DB);
      enrichedOrders.push(enrichedOrder);
    }

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM orders WHERE deleted_at IS NULL`;
    const countParams: any[] = [];

    if (customer_id) {
      countQuery += ` AND customer_id = ?`;
      countParams.push(customer_id);
    } else if (email) {
      countQuery += ` AND email = ?`;
      countParams.push(email);
    }

    const countResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();
    const total = (countResult as any)?.total || 0;

    return c.json({
      success: true,
      data: enrichedOrders,
      pagination: {
        limit,
        offset,
        total,
        count: enrichedOrders.length
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch orders');
  }
});

export const ordersSimplifiedRoutes = app;
