AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Product Reviews System with Lambda functions and DynamoDB

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - staging
      - prod
    Description: Environment name
  
  MedusaApiUrl:
    Type: String
    Default: http://localhost:9000
    Description: Medusa API URL
  
  MedusaApiKey:
    Type: String
    NoEcho: true
    Description: Medusa API Key for authentication

Resources:
  # DynamoDB Table for Product Reviews
  ProductReviewsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ProductReviews-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: product_id
          AttributeType: S
        - AttributeName: review_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: product_id
          KeyType: HASH
        - AttributeName: review_id
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: user_id-product_id-index
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
            - AttributeName: product_id
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  # Lambda function for getting product ratings
  GetProductRatingsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub get-product-ratings-${Environment}
      CodeUri: ./
      Handler: get-product-ratings.handler
      Runtime: nodejs22.x
      Timeout: 10
      MemorySize: 128
      Environment:
        Variables:
          REVIEWS_TABLE_NAME: !Ref ProductReviewsTable
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref ProductReviewsTable
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /product-ratings
            Method: get

  # Lambda function for getting product reviews
  GetProductReviewsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub get-product-reviews-${Environment}
      CodeUri: ./
      Handler: get-product-reviews.handler
      Runtime: nodejs22.x
      Timeout: 10
      MemorySize: 128
      Environment:
        Variables:
          REVIEWS_TABLE_NAME: !Ref ProductReviewsTable
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref ProductReviewsTable
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /product-reviews/{productId}
            Method: get

  # Lambda function for submitting product reviews
  SubmitProductReviewFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub submit-product-review-${Environment}
      CodeUri: ./
      Handler: submit-product-review.handler
      Runtime: nodejs22.x
      Timeout: 15
      MemorySize: 256
      Environment:
        Variables:
          REVIEWS_TABLE_NAME: !Ref ProductReviewsTable
          MEDUSA_API_URL: !Ref MedusaApiUrl
          MEDUSA_API_KEY: !Ref MedusaApiKey
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref ProductReviewsTable
        - Statement:
            Effect: Allow
            Action:
              - 'execute-api:Invoke'
            Resource: '*'
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /product-reviews
            Method: post

Outputs:
  ProductReviewsTable:
    Description: DynamoDB table for product reviews
    Value: !Ref ProductReviewsTable
  
  ApiEndpoint:
    Description: API Gateway endpoint URL
    Value: !Sub https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}
  
  GetProductRatingsFunction:
    Description: Lambda function for getting product ratings
    Value: !Ref GetProductRatingsFunction
  
  GetProductReviewsFunction:
    Description: Lambda function for getting product reviews
    Value: !Ref GetProductReviewsFunction
  
  SubmitProductReviewFunction:
    Description: Lambda function for submitting product reviews
    Value: !Ref SubmitProductReviewFunction 