				import worker, * as OTHER_EXPORTS from "C:\\Users\\<USER>\\PhpstormProjects\\ro\\cloudflare-workers-migration\\backend\\src\\index.ts";
				import * as __MIDDLEWARE_0__ from "C:\\Users\\<USER>\\PhpstormProjects\\ro\\cloudflare-workers-migration\\node_modules\\.pnpm\\wrangler@3.114.9_@cloudflare+workers-types@4.20250531.0\\node_modules\\wrangler\\templates\\middleware\\middleware-ensure-req-body-drained.ts";
import * as __MIDDLEWARE_1__ from "C:\\Users\\<USER>\\PhpstormProjects\\ro\\cloudflare-workers-migration\\node_modules\\.pnpm\\wrangler@3.114.9_@cloudflare+workers-types@4.20250531.0\\node_modules\\wrangler\\templates\\middleware\\middleware-miniflare3-json-error.ts";

				export * from "C:\\Users\\<USER>\\PhpstormProjects\\ro\\cloudflare-workers-migration\\backend\\src\\index.ts";

				export const __INTERNAL_WRANGLER_MIDDLEWARE__ = [
					
					__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default
				]
				export default worker;