import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { jwt, sign } from 'hono/jwt';
import { Context } from 'hono';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// JWT middleware for protected routes
const requireAuth = jwt({
  secret: 'dev-jwt-secret-change-in-production-please-make-this-very-long-and-secure-2024',
});

// ===========================================
// UTILITIES
// ===========================================

function generateId(prefix: string): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error(error);
  return c.json({ error: message, details: error.message }, 500);
}

function safeParseDate(dateValue: any): string | null {
  if (!dateValue) return null;
  
  try {
    // Handle double-encoded JSON dates
    if (typeof dateValue === 'string' && dateValue.startsWith('"') && dateValue.endsWith('"')) {
      dateValue = JSON.parse(dateValue);
    }
    
    // Validate the date
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      return null;
    }
    
    return date.toISOString();
  } catch (error) {
    console.warn('Failed to parse date:', dateValue, error);
    return null;
  }
}

// ===========================================
// AUTHENTICATION ROUTES
// ===========================================

app.post('/auth/login', async (c) => {
  try {
    const { email, password } = await c.req.json();
    
    if (!email || !password) {
      return c.json({ error: 'Email and password are required' }, 400);
    }

    // Check user credentials
    const user = await c.env.DB.prepare(`
      SELECT id, email, password_hash, first_name, last_name, role, is_active
      FROM users 
      WHERE email = ? AND deleted_at IS NULL
    `).bind(email).first();

    if (!user || !user.is_active) {
      return c.json({ error: 'Invalid credentials' }, 401);
    }

    // Note: In production, you should properly hash/verify passwords
    // For now, we'll use a simple check
    if (password !== 'admin123') {
      return c.json({ error: 'Invalid credentials' }, 401);
    }

    // Update last login
    await c.env.DB.prepare(`
      UPDATE users 
      SET last_login_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(user.id).run();

    // Generate JWT token
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
      exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24), // 24 hours
    };

    const token = await sign(payload, c.env.JWT_SECRET || 'dev-jwt-secret-change-in-production-please-make-this-very-long-and-secure-2024');

    return c.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
      }
    });

  } catch (error) {
    return handleError(c, error, 'Login failed');
  }
});

// Test endpoint to verify auth is working
app.get('/auth/me', requireAuth, async (c) => {
  try {
    // Get JWT payload from the middleware
    const payload = c.get('jwtPayload');
    
    // Get user details from database
    const user = await c.env.DB.prepare(`
      SELECT id, email, first_name, last_name, role, is_active
      FROM users 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(payload.id).first();

    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    return c.json({
      user: {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to get user info');
  }
});

// Apply authentication middleware to all /api/* routes except login endpoints
app.use('/api/*', async (c, next) => {
  // Skip authentication for login endpoints
  const path = c.req.path;
  if (path.endsWith('/auth/login') || path.endsWith('/auth/logout')) {
    return next();
  }
  
  // Apply authentication for all other API routes
  return requireAuth(c, next);
});

// ===========================================
// PRODUCT ROUTES (Protected)
// ===========================================

// Get all products
app.get('/api/products', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const collection_id = c.req.query('collection_id') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        p.id, p.handle, p.status, p.collection_id, p.type_id, p.thumbnail,
        p.weight, p.dimensions, p.requires_shipping, p.is_giftcard, p.is_discountable,
        p.tags, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description,
        c.handle as collection_handle,
        ct.title as collection_title,
        ptype.name as type_name,
        COUNT(pv.id) as variant_count
      FROM products p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      LEFT JOIN collections c ON p.collection_id = c.id
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'en'
      LEFT JOIN product_types ptype ON p.type_id = ptype.id
      LEFT JOIN product_variants pv ON p.id = pv.product_id AND pv.deleted_at IS NULL
      WHERE p.deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (pt.title LIKE ? OR p.handle LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (status) {
      query += ` AND p.status = ?`;
      params.push(status);
    }
    
    if (collection_id) {
      query += ` AND p.collection_id = ?`;
      params.push(collection_id);
    }

    query += ` GROUP BY p.id ORDER BY p.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const products = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `SELECT COUNT(DISTINCT p.id) as total FROM products p`;
    let countParams: any[] = [];
    
    if (search || status || collection_id) {
      countQuery += ` LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en' WHERE p.deleted_at IS NULL`;
      
      if (search) {
        countQuery += ` AND (pt.title LIKE ? OR p.handle LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`);
      }
      
      if (status) {
        countQuery += ` AND p.status = ?`;
        countParams.push(status);
      }
      
      if (collection_id) {
        countQuery += ` AND p.collection_id = ?`;
        countParams.push(collection_id);
      }
    } else {
      countQuery += ` WHERE p.deleted_at IS NULL`;
    }

    const count = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    // Process products to match the simplified schema structure
    const processedProducts = (products.results || []).map((product: any) => ({
      ...product,
      tags: product.tags ? JSON.parse(product.tags) : [],
      metadata: product.metadata ? JSON.parse(product.metadata) : {},
      dimensions: product.dimensions ? JSON.parse(product.dimensions) : null,
      collection: product.collection_id ? {
        id: product.collection_id,
        handle: product.collection_handle,
        translations: [{
          collection_id: product.collection_id,
          language_code: 'en',
          title: product.collection_title
        }].filter(t => t.title)
      } : null,
      type: product.type_id ? {
        id: product.type_id,
        name: product.type_name
      } : null,
      translations: [{
        product_id: product.id,
        language_code: 'en',
        title: product.title,
        subtitle: product.subtitle,
        description: product.description
      }].filter(t => t.title),
      variants: [], // Will be populated separately if needed
      images: [], // Will be populated separately if needed
      options: [] // Will be populated separately if needed
    }));

    return c.json({
      success: true,
      data: processedProducts,
      pagination: {
        page,
        limit,
        total: count?.total || 0,
        totalPages: Math.ceil((Number(count?.total) || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch products');
  }
});

// Get single product
app.get('/api/products/:id', async (c) => {
  try {
    const id = c.req.param('id');

    const product = await c.env.DB.prepare(`
      SELECT 
        p.id, p.handle, p.status, p.collection_id, p.type_id, p.thumbnail,
        p.weight, p.dimensions, p.requires_shipping, p.is_giftcard, p.is_discountable,
        p.tags, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description,
        c.handle as collection_handle,
        ct.title as collection_title,
        ptype.name as type_name
      FROM products p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      LEFT JOIN collections c ON p.collection_id = c.id
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'en'
      LEFT JOIN product_types ptype ON p.type_id = ptype.id
      WHERE p.id = ? AND p.deleted_at IS NULL
    `).bind(id).first();

    if (!product) {
      return c.json({ error: 'Product not found' }, 404);
    }

    // Get variants with pricing
    const variants = await c.env.DB.prepare(`
      SELECT 
        pv.id, pv.title, pv.sku, pv.barcode, pv.allow_backorder,
        pv.manage_inventory, pv.weight, pv.dimensions, pv.sort_order,
        pv.inventory_quantity, pv.option_values, pv.metadata,
        pv.created_at, pv.updated_at,
        vp.price, vp.compare_at_price, vp.cost_price, vp.currency_code
      FROM product_variants pv
      LEFT JOIN variant_prices vp ON pv.id = vp.variant_id 
        AND vp.currency_code = 'USD' 
        AND (vp.region_id IS NULL OR vp.region_id = 'reg_default')
        AND (vp.starts_at IS NULL OR vp.starts_at <= datetime('now'))
        AND (vp.ends_at IS NULL OR vp.ends_at >= datetime('now'))
      WHERE pv.product_id = ? AND pv.deleted_at IS NULL
      ORDER BY pv.sort_order, pv.created_at
    `).bind(id).all();

    // Get images
    const images = await c.env.DB.prepare(`
      SELECT pi.id, pi.url, pi.alt_text, pi.sort_order, pi.metadata, pi.created_at
      FROM product_images pi
      WHERE pi.product_id = ?
      ORDER BY pi.sort_order, pi.created_at
    `).bind(id).all();

    // Get options
    const options = await c.env.DB.prepare(`
      SELECT po.id, po.name, po.type, po.sort_order, po.created_at
      FROM product_options po
      WHERE po.product_id = ?
      ORDER BY po.sort_order, po.created_at
    `).bind(id).all();

    return c.json({
      success: true,
      data: {
        ...product,
        variants: variants.results || [],
        images: images.results || [],
        options: options.results || [],
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product');
  }
});

// Create product
app.post('/api/products', async (c) => {
  try {
    const data = await c.req.json();
    const productId = generateId('prod');

    // Create product
    await c.env.DB.prepare(`
      INSERT INTO products (
        id, handle, status, collection_id, type_id, thumbnail, weight, dimensions,
        requires_shipping, is_giftcard, is_discountable, tags, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      productId,
      data.handle,
      data.status || 'draft',
      data.collection_id || null,
      data.type_id || null,
      data.thumbnail || null,
      data.weight || null,
      JSON.stringify(data.dimensions || {}),
      data.requires_shipping !== false,
      data.is_giftcard || false,
      data.is_discountable !== false,
      JSON.stringify(data.tags || []),
      JSON.stringify(data.metadata || {})
    ).run();

    // Create product translation
    if (data.title) {
      await c.env.DB.prepare(`
        INSERT INTO product_translations (
          product_id, language_code, title, subtitle, description, seo_title, seo_description
        ) VALUES (?, 'en', ?, ?, ?, ?, ?)
      `).bind(
        productId,
        data.title,
        data.subtitle || null,
        data.description || null,
        data.seo_title || null,
        data.seo_description || null
      ).run();
    }

    return c.json({ id: productId, message: 'Product created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create product');
  }
});

// Update product
app.put('/api/products/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const data = await c.req.json();

    // Update product
    await c.env.DB.prepare(`
      UPDATE products SET
        handle = ?, status = ?, collection_id = ?, type_id = ?, thumbnail = ?,
        weight = ?, dimensions = ?, requires_shipping = ?, is_giftcard = ?,
        is_discountable = ?, tags = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(
      data.handle,
      data.status,
      data.collection_id || null,
      data.type_id || null,
      data.thumbnail || null,
      data.weight || null,
      JSON.stringify(data.dimensions || {}),
      data.requires_shipping !== false,
      data.is_giftcard || false,
      data.is_discountable !== false,
      JSON.stringify(data.tags || []),
      JSON.stringify(data.metadata || {}),
      id
    ).run();

    // Update or create translation
    const existingTranslation = await c.env.DB.prepare(`
      SELECT product_id FROM product_translations 
      WHERE product_id = ? AND language_code = 'en'
    `).bind(id).first();

    if (existingTranslation) {
      await c.env.DB.prepare(`
        UPDATE product_translations SET
          title = ?, subtitle = ?, description = ?, seo_title = ?, seo_description = ?
        WHERE product_id = ? AND language_code = 'en'
      `).bind(
        data.title,
        data.subtitle || null,
        data.description || null,
        data.seo_title || null,
        data.seo_description || null,
        id
      ).run();
    } else if (data.title) {
      await c.env.DB.prepare(`
        INSERT INTO product_translations (
          product_id, language_code, title, subtitle, description, seo_title, seo_description
        ) VALUES (?, 'en', ?, ?, ?, ?, ?)
      `).bind(
        id,
        data.title,
        data.subtitle || null,
        data.description || null,
        data.seo_title || null,
        data.seo_description || null
      ).run();
    }

    return c.json({ message: 'Product updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update product');
  }
});

// Delete product
app.delete('/api/products/:id', async (c) => {
  try {
    const id = c.req.param('id');

    await c.env.DB.prepare(`
      UPDATE products 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'Product deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete product');
  }
});

// Bulk delete products
app.post('/api/products/bulk-delete', async (c) => {
  try {
    const { productIds } = await c.req.json();

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return c.json({ error: 'Product IDs are required' }, 400);
    }

    const placeholders = productIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE products 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...productIds).run();

    return c.json({ message: `${productIds.length} products deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete products');
  }
});

// Bulk update product status
app.post('/api/products/bulk-update', async (c) => {
  try {
    const { productIds, status } = await c.req.json();

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return c.json({ error: 'Product IDs are required' }, 400);
    }

    if (!status) {
      return c.json({ error: 'Status is required' }, 400);
    }

    const placeholders = productIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE products 
      SET status = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(status, ...productIds).run();

    return c.json({ message: `${productIds.length} products updated successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk update products');
  }
});

// ===========================================
// PRODUCT VARIANT ROUTES
// ===========================================

// Get product variants
app.get('/api/products/:id/variants', async (c) => {
  try {
    const id = c.req.param('id');

    const variants = await c.env.DB.prepare(`
      SELECT pv.id, pv.title, pv.sku, pv.barcode, pv.allow_backorder,
             pv.manage_inventory, pv.weight, pv.dimensions, pv.sort_order,
             pv.inventory_quantity, pv.option_values, pv.metadata,
             pv.created_at, pv.updated_at
      FROM product_variants pv
      WHERE pv.product_id = ? AND pv.deleted_at IS NULL
      ORDER BY pv.sort_order, pv.created_at
    `).bind(id).all();

    return c.json({
      success: true,
      data: variants.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product variants');
  }
});

// Get product images
app.get('/api/products/:id/images', async (c) => {
  try {
    const id = c.req.param('id');

    const images = await c.env.DB.prepare(`
      SELECT pi.id, pi.url, pi.alt_text, pi.sort_order, pi.metadata, pi.created_at
      FROM product_images pi
      WHERE pi.product_id = ?
      ORDER BY pi.sort_order, pi.created_at
    `).bind(id).all();

    return c.json({
      success: true,
      data: images.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product images');
  }
});

// Get product options
app.get('/api/products/:id/options', async (c) => {
  try {
    const id = c.req.param('id');

    const options = await c.env.DB.prepare(`
      SELECT po.id, po.name, po.type, po.sort_order, po.created_at
      FROM product_options po
      WHERE po.product_id = ?
      ORDER BY po.sort_order, po.created_at
    `).bind(id).all();

    return c.json({
      success: true,
      data: options.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product options');
  }
});

// Create variant
app.post('/api/products/:productId/variants', async (c) => {
  try {
    const productId = c.req.param('productId');
    const data = await c.req.json();
    const variantId = generateId('variant');

    await c.env.DB.prepare(`
      INSERT INTO product_variants (
        id, product_id, title, sku, barcode, weight, dimensions, sort_order,
        option_values, manage_inventory, allow_backorder, inventory_quantity, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      variantId,
      productId,
      data.title,
      data.sku || null,
      data.barcode || null,
      data.weight || null,
      JSON.stringify(data.dimensions || {}),
      data.sort_order || 0,
      JSON.stringify(data.option_values || []),
      data.manage_inventory !== false,
      data.allow_backorder || false,
      data.inventory_quantity || 0,
      JSON.stringify(data.metadata || {})
    ).run();

    return c.json({ id: variantId, message: 'Variant created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create variant');
  }
});

// ===========================================
// COLLECTIONS ROUTES
// ===========================================

// Get all collections
app.get('/api/collections', async (c) => {
  try {
    const collections = await c.env.DB.prepare(`
      SELECT 
        c.id, c.handle, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
        ct.title, ct.description, ct.seo_title, ct.seo_description
      FROM collections c
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'en'
      WHERE c.deleted_at IS NULL
      ORDER BY c.sort_order, c.created_at DESC
    `).all();

    // Process collections to match the simplified schema structure
    const processedCollections = (collections.results || []).map((collection: any) => ({
      ...collection,
      metadata: collection.metadata ? JSON.parse(collection.metadata) : {},
      translations: [{
        collection_id: collection.id,
        language_code: 'en',
        title: collection.title,
        description: collection.description,
        seo_title: collection.seo_title,
        seo_description: collection.seo_description
      }].filter(t => t.title)
    }));

    return c.json({
      success: true,
      data: processedCollections
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch collections');
  }
});

// Bulk delete collections
app.post('/api/collections/bulk-delete', async (c) => {
  try {
    const { collectionIds } = await c.req.json();

    if (!collectionIds || !Array.isArray(collectionIds) || collectionIds.length === 0) {
      return c.json({ error: 'Collection IDs are required' }, 400);
    }

    const placeholders = collectionIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE collections 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...collectionIds).run();

    return c.json({ message: `${collectionIds.length} collections deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete collections');
  }
});

// ===========================================
// CUSTOMERS ROUTES
// ===========================================

// Get all customers
app.get('/api/customers', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        c.id, c.email, c.phone, c.first_name, c.last_name, c.date_of_birth,
        c.group_id, c.accepts_marketing, c.tax_exempt, c.metadata, c.created_at, c.updated_at,
        cg.name as group_name,
        COALESCE(order_stats.orders_count, 0) as orders_count,
        COALESCE(order_stats.total_spent, 0) as total_spent,
        ca.city as address_city,
        ca.country_code as address_country
      FROM customers c
      LEFT JOIN customer_groups cg ON c.group_id = cg.id
      LEFT JOIN (
        SELECT 
          customer_id,
          COUNT(id) as orders_count,
          SUM(total_amount) as total_spent
        FROM orders 
        GROUP BY customer_id
      ) order_stats ON c.id = order_stats.customer_id
      LEFT JOIN customer_addresses ca ON c.id = ca.customer_id AND ca.is_default = 1 
      WHERE c.deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (c.email LIKE ? OR c.first_name LIKE ? OR c.last_name LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    query += ` ORDER BY c.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const customersResult = await c.env.DB.prepare(query).bind(...params).all();

    // Process customers data to match frontend expectations
    const customers = (customersResult.results || []).map((customer: any) => ({
      id: customer.id,
      email: customer.email,
      phone: customer.phone,
      first_name: customer.first_name,
      last_name: customer.last_name,
      date_of_birth: safeParseDate(customer.date_of_birth),
      group_id: customer.group_id,
      accepts_marketing: Boolean(customer.accepts_marketing),
      tax_exempt: Boolean(customer.tax_exempt),
      has_account: Boolean(customer.email), // Simple logic: if has email, considered as having account
      metadata: customer.metadata ? (typeof customer.metadata === 'string' ? JSON.parse(customer.metadata) : customer.metadata) : null,
      created_at: safeParseDate(customer.created_at),
      updated_at: safeParseDate(customer.updated_at),
      orders_count: Number(customer.orders_count) || 0,
      total_spent: Number(customer.total_spent) || 0,
      addresses: customer.address_city ? [{
        id: 'default',
        city: customer.address_city,
        country_code: customer.address_country,
        is_default: true
      }] : [],
      groups: customer.group_name ? [{ id: customer.group_id, name: customer.group_name }] : [],
      group_name: customer.group_name
    }));

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM customers WHERE deleted_at IS NULL`;
    let countParams: any[] = [];
    
    if (search) {
      countQuery += ` AND (email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    const count = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      success: true,
      data: customers,
      pagination: {
        page,
        limit,
        total: count?.total || 0,
        pages: Math.ceil((Number(count?.total) || 0) / limit)
      },
      stats: {
        withAccounts: customers.filter((c: any) => c.has_account).length,
        newThisMonth: 0, // TODO: Calculate new customers this month
        avgLifetimeValue: customers.length > 0 ? customers.reduce((sum: number, c: any) => sum + c.total_spent, 0) / customers.length : 0
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch customers');
  }
});

// ===========================================
// ORDERS ROUTES
// ===========================================

// Get all orders with pagination and filtering
app.get('/api/orders', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status');
    const search = c.req.query('search');
    const financial_status = c.req.query('financial_status');
    const fulfillment_status = c.req.query('fulfillment_status');
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let params = [];

    if (status) {
      whereConditions.push('o.status = ?');
      params.push(status);
    }

    if (financial_status) {
      whereConditions.push('o.financial_status = ?');
      params.push(financial_status);
    }

    if (fulfillment_status) {
      whereConditions.push('o.fulfillment_status = ?');
      params.push(fulfillment_status);
    }

    if (search) {
      whereConditions.push('(o.number LIKE ? OR o.email LIKE ? OR c.first_name LIKE ? OR c.last_name LIKE ?)');
      const searchParam = `%${search}%`;
      params.push(searchParam, searchParam, searchParam, searchParam);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    const ordersQuery = `
      SELECT 
        o.id, o.number, o.customer_id, o.email, o.currency_code, o.region_id,
        o.status, o.financial_status, o.fulfillment_status,
        o.subtotal, o.tax_amount, o.shipping_amount, o.discount_amount, o.total_amount,
        o.created_at, o.updated_at, o.cancelled_at,
        c.first_name, c.last_name, c.email as customer_email
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      ${whereClause}
      ORDER BY o.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(*) as total
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      ${whereClause}
    `;

    const [ordersResult, countResult] = await Promise.all([
      c.env.DB.prepare(ordersQuery).bind(...params, limit, offset).all(),
      c.env.DB.prepare(countQuery).bind(...params).first()
    ]);

    const total = (countResult?.total as number) || 0;
    const totalPages = Math.ceil(total / limit);

    // Process orders to match the simplified schema structure
    const processedOrders = (ordersResult.results || []).map((order: any) => ({
      ...order,
      billing_address: order.billing_address ? JSON.parse(order.billing_address) : null,
      shipping_address: order.shipping_address ? JSON.parse(order.shipping_address) : null,
      metadata: order.metadata ? JSON.parse(order.metadata) : {},
      customer: order.customer_id ? {
        id: order.customer_id,
        first_name: order.customer_first_name,
        last_name: order.customer_last_name,
        email: order.customer_email || order.email
      } : null,
      items: [], // Will be populated separately if needed
      payments: [], // Will be populated separately if needed
      fulfillments: [] // Will be populated separately if needed
    }));

    return c.json({
      success: true,
      data: processedOrders,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch orders');
  }
});

// Get single order with full details
app.get('/api/orders/:id', async (c) => {
  try {
    const id = c.req.param('id');

    // Get order details
    const order = await c.env.DB.prepare(`
      SELECT 
        o.*,
        c.first_name, c.last_name, c.email as customer_email, c.phone as customer_phone
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE o.id = ? 
    `).bind(id).first();

    if (!order) {
      return c.json({ error: 'Order not found' }, 404);
    }

    // Get order items with proper product title from translations
    const items = await c.env.DB.prepare(`
      SELECT 
        oi.*,
        pv.title as variant_title,
        pv.sku,
        pt.title as product_title
      FROM order_items oi
      LEFT JOIN product_variants pv ON oi.variant_id = pv.id
      LEFT JOIN products p ON pv.product_id = p.id
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      WHERE oi.order_id = ?
      ORDER BY oi.created_at
    `).bind(id).all();

    // Get payments
    const payments = await c.env.DB.prepare(`
      SELECT * FROM payments 
      WHERE order_id = ? 
      ORDER BY created_at DESC
    `).bind(id).all();

    // Get fulfillments (if implemented)
    const fulfillments = await c.env.DB.prepare(`
      SELECT * FROM fulfillments 
      WHERE order_id = ? 
      ORDER BY created_at DESC
    `).bind(id).all();

    // Parse JSON fields
    const orderData = {
      ...order,
      billing_address: order.billing_address ? JSON.parse(String(order.billing_address)) : null,
      shipping_address: order.shipping_address ? JSON.parse(String(order.shipping_address)) : null,
      customer: order.customer_id ? {
        id: order.customer_id,
        first_name: order.first_name,
        last_name: order.last_name,
        email: order.customer_email,
        phone: order.customer_phone,
      } : null,
      items: items.results,
      payments: payments.results,
      fulfillments: fulfillments.results,
    };

    return c.json({ order: orderData });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch order');
  }
});

// Update order status
app.put('/api/orders/:id/status', async (c) => {
  try {
    const id = c.req.param('id');
    const { status } = await c.req.json();

    const validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
    if (!validStatuses.includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    await c.env.DB.prepare(`
      UPDATE orders 
      SET status = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(status, id).run();

    return c.json({ message: 'Order status updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update order status');
  }
});

// Update order notes
app.put('/api/orders/:id/notes', async (c) => {
  try {
    const id = c.req.param('id');
    const { notes } = await c.req.json();

    await c.env.DB.prepare(`
      UPDATE orders 
      SET notes = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(notes || null, id).run();

    return c.json({ message: 'Order notes updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update order notes');
  }
});

// Create fulfillment
app.post('/api/orders/:id/fulfillments', async (c) => {
  try {
    const orderId = c.req.param('id');
    const { tracking_number, tracking_url, carrier, items } = await c.req.json();

    const fulfillmentId = crypto.randomUUID();

    await c.env.DB.prepare(`
      INSERT INTO fulfillments (
        id, order_id, status, tracking_number, tracking_url, carrier, 
        metadata, created_at, updated_at
      ) VALUES (?, ?, 'shipped', ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      fulfillmentId,
      orderId, 
      tracking_number || null,
      tracking_url || null,
      carrier || null,
      JSON.stringify({ items: items || [] })
    ).run();

    return c.json({ 
      message: 'Fulfillment created successfully',
      fulfillment_id: fulfillmentId 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create fulfillment');
  }
});

// ===========================================
// RETURNS & EXCHANGES
// ===========================================

// Get all return reasons
app.get('/api/returns/reasons', async (c) => {
  try {
    const reasons = await c.env.DB.prepare(`
      SELECT * FROM return_reasons 
      WHERE is_active = 1 
      ORDER BY sort_order, label
    `).all();

    return c.json({ success: true, data: reasons.results });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch return reasons');
  }
});

// Get returns with pagination and filtering
app.get('/api/returns', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status');
    const search = c.req.query('search');
    const sort = c.req.query('sort') || 'created_at:desc';
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE r.cancelled_at IS NULL';
    let queryParams = [];

    if (status) {
      whereClause += ' AND r.status = ?';
      queryParams.push(status);
    }

    if (search) {
      whereClause += ' AND (r.return_number LIKE ? OR o.number LIKE ? OR c.email LIKE ?)';
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    const [sortField, sortDirection] = sort.split(':');
    const validSortFields = ['created_at', 'return_number', 'status', 'total_amount'];
    const orderBy = validSortFields.includes(sortField) ? sortField : 'created_at';
    const direction = sortDirection === 'asc' ? 'ASC' : 'DESC';

    const query = `
      SELECT 
        r.*,
        o.number as order_number,
        o.customer_id,
        COALESCE(c.email, o.email) as customer_email,
        COALESCE(c.first_name, '') as customer_first_name,
        COALESCE(c.last_name, '') as customer_last_name,
        COUNT(ri.id) as item_count
      FROM returns r
      LEFT JOIN orders o ON r.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN return_items ri ON r.id = ri.return_id
      ${whereClause}
      GROUP BY r.id
      ORDER BY r.${orderBy} ${direction}
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(DISTINCT r.id) as total
      FROM returns r
      LEFT JOIN orders o ON r.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      ${whereClause}
    `;

    const [results, countResult] = await Promise.all([
      c.env.DB.prepare(query).bind(...queryParams, limit, offset).all(),
      c.env.DB.prepare(countQuery).bind(...queryParams).first()
    ]);

    const total = (countResult?.total as number) || 0;

    return c.json({
      success: true,
      data: results.results,
      meta: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch returns');
  }
});

// Get single return with details
app.get('/api/returns/:id', async (c) => {
  try {
    const returnId = c.req.param('id');

    const returnData = await c.env.DB.prepare(`
      SELECT 
        r.*,
        o.number as order_number,
        o.customer_id,
        o.total_amount as order_total,
        o.currency_code as order_currency,
        COALESCE(c.email, o.email) as customer_email,
        COALESCE(c.first_name, '') as customer_first_name,
        COALESCE(c.last_name, '') as customer_last_name
      FROM returns r
      LEFT JOIN orders o ON r.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE r.id = ?
    `).bind(returnId).first();

    if (!returnData) {
      return c.json({ error: 'Return not found' }, 404);
    }

    // Get return items with product details
    const returnItems = await c.env.DB.prepare(`
      SELECT 
        ri.*,
        oi.product_title,
        oi.variant_title,
        oi.unit_price,
        oi.total_price as original_total,
        rr.label as reason_label,
        rr.description as reason_description
      FROM return_items ri
      LEFT JOIN order_items oi ON ri.order_item_id = oi.id
      LEFT JOIN return_reasons rr ON ri.reason_id = rr.id
      WHERE ri.return_id = ?
      ORDER BY ri.created_at
    `).bind(returnId).all();

    return c.json({
      success: true,
      data: {
        ...returnData,
        items: returnItems.results
      }
    });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch return');
  }
});

// Create new return
app.post('/api/returns', async (c) => {
  try {
    const body = await c.req.json();
    const { 
      order_id, 
      items, // Array of {order_item_id, reason_id, quantity, note}
      customer_note,
      admin_note 
    } = body;

    if (!order_id || !items || !items.length) {
      return c.json({ error: 'Order ID and items are required' }, 400);
    }

    // Verify order exists
    const order = await c.env.DB.prepare('SELECT * FROM orders WHERE id = ?').bind(order_id).first();
    if (!order) {
      return c.json({ error: 'Order not found' }, 404);
    }

    const returnId = generateId('ret');
    const returnNumber = `RET-${Date.now()}`;

    // Calculate total refund amount
    let totalRefund = 0;
    for (const item of items) {
      const orderItem = await c.env.DB.prepare('SELECT * FROM order_items WHERE id = ?').bind(item.order_item_id).first();
      if (orderItem) {
        totalRefund += ((orderItem.unit_price as number) * item.quantity);
      }
    }

    // Create return
    await c.env.DB.prepare(`
      INSERT INTO returns (
        id, order_id, status, return_number, customer_note, admin_note,
        refund_amount, total_amount, currency_code, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      returnId, order_id, 'requested', returnNumber, customer_note || null, admin_note || null,
      totalRefund, totalRefund, order.currency_code
    ).run();

    // Create return items
    for (const item of items) {
      const itemId = generateId('ret_item');
      const orderItem = await c.env.DB.prepare('SELECT * FROM order_items WHERE id = ?').bind(item.order_item_id).first();
      const refundAmount = orderItem ? ((orderItem.unit_price as number) * item.quantity) : 0;

      await c.env.DB.prepare(`
        INSERT INTO return_items (
          id, return_id, order_item_id, reason_id, quantity, note, refund_amount, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        itemId, returnId, item.order_item_id, item.reason_id, item.quantity, item.note || null, refundAmount
      ).run();
    }

    return c.json({ 
      success: true, 
      data: { id: returnId, return_number: returnNumber },
      message: 'Return created successfully' 
    }, 201);
  } catch (error) {
    return handleError(c, error, 'Failed to create return');
  }
});

// Update return status
app.put('/api/returns/:id/status', async (c) => {
  try {
    const returnId = c.req.param('id');
    const { status, admin_note, tracking_number, tracking_url } = await c.req.json();

    const validStatuses = ['requested', 'approved', 'received', 'processing', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    let updateData = [status];
    let setClauses = ['status = ?'];

    if (admin_note) {
      setClauses.push('admin_note = ?');
      updateData.push(admin_note);
    }

    if (tracking_number) {
      setClauses.push('tracking_number = ?');
      updateData.push(tracking_number);
    }

    if (tracking_url) {
      setClauses.push('tracking_url = ?');
      updateData.push(tracking_url);
    }

    // Add status-specific timestamp updates
    if (status === 'received') {
      setClauses.push('received_at = CURRENT_TIMESTAMP');
    } else if (status === 'processing') {
      setClauses.push('processed_at = CURRENT_TIMESTAMP');
    } else if (status === 'completed') {
      setClauses.push('completed_at = CURRENT_TIMESTAMP');
    } else if (status === 'cancelled') {
      setClauses.push('cancelled_at = CURRENT_TIMESTAMP');
    }

    setClauses.push('updated_at = CURRENT_TIMESTAMP');
    updateData.push(returnId);

    await c.env.DB.prepare(`
      UPDATE returns 
      SET ${setClauses.join(', ')}
      WHERE id = ?
    `).bind(...updateData).run();

    return c.json({ success: true, message: 'Return status updated successfully' });
  } catch (error) {
    return handleError(c, error, 'Failed to update return status');
  }
});

// Get exchanges with pagination and filtering
app.get('/api/exchanges', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status');
    const search = c.req.query('search');
    const sort = c.req.query('sort') || 'created_at:desc';
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE e.cancelled_at IS NULL';
    let queryParams = [];

    if (status) {
      whereClause += ' AND e.status = ?';
      queryParams.push(status);
    }

    if (search) {
      whereClause += ' AND (e.exchange_number LIKE ? OR o.number LIKE ? OR c.email LIKE ?)';
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    const [sortField, sortDirection] = sort.split(':');
    const validSortFields = ['created_at', 'exchange_number', 'status', 'difference_amount'];
    const orderBy = validSortFields.includes(sortField) ? sortField : 'created_at';
    const direction = sortDirection === 'asc' ? 'ASC' : 'DESC';

    const query = `
      SELECT 
        e.*,
        o.number as order_number,
        o.customer_id,
        COALESCE(c.email, o.email) as customer_email,
        COALESCE(c.first_name, '') as customer_first_name,
        COALESCE(c.last_name, '') as customer_last_name,
        COUNT(DISTINCT eri.id) + COUNT(DISTINCT eni.id) as item_count
      FROM exchanges e
      LEFT JOIN orders o ON e.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN exchange_return_items eri ON e.id = eri.exchange_id
      LEFT JOIN exchange_new_items eni ON e.id = eni.exchange_id
      ${whereClause}
      GROUP BY e.id
      ORDER BY e.${orderBy} ${direction}
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(DISTINCT e.id) as total
      FROM exchanges e
      LEFT JOIN orders o ON e.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      ${whereClause}
    `;

    const [results, countResult] = await Promise.all([
      c.env.DB.prepare(query).bind(...queryParams, limit, offset).all(),
      c.env.DB.prepare(countQuery).bind(...queryParams).first()
    ]);

    const total = (countResult?.total as number) || 0;

    return c.json({
      success: true,
      data: results.results,
      meta: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch exchanges');
  }
});

// Get single exchange with details
app.get('/api/exchanges/:id', async (c) => {
  try {
    const exchangeId = c.req.param('id');

    const exchangeData = await c.env.DB.prepare(`
      SELECT 
        e.*,
        o.number as order_number,
        o.customer_id,
        o.total_amount as order_total,
        o.currency_code as order_currency,
        COALESCE(c.email, o.email) as customer_email,
        COALESCE(c.first_name, '') as customer_first_name,
        COALESCE(c.last_name, '') as customer_last_name
      FROM exchanges e
      LEFT JOIN orders o ON e.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE e.id = ?
    `).bind(exchangeId).first();

    if (!exchangeData) {
      return c.json({ error: 'Exchange not found' }, 404);
    }

    // Get return items
    const returnItems = await c.env.DB.prepare(`
      SELECT 
        eri.*,
        oi.product_title,
        oi.variant_title,
        oi.unit_price,
        oi.total_price as original_total,
        rr.label as reason_label
      FROM exchange_return_items eri
      LEFT JOIN order_items oi ON eri.order_item_id = oi.id
      LEFT JOIN return_reasons rr ON eri.reason_id = rr.id
      WHERE eri.exchange_id = ?
      ORDER BY eri.created_at
    `).bind(exchangeId).all();

    // Get new items
    const newItems = await c.env.DB.prepare(`
      SELECT 
        eni.*,
        pt.title as product_title,
        pv.title as variant_title,
        pv.sku
      FROM exchange_new_items eni
      LEFT JOIN product_variants pv ON eni.variant_id = pv.id
      LEFT JOIN products p ON pv.product_id = p.id
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      WHERE eni.exchange_id = ?
      ORDER BY eni.created_at
    `).bind(exchangeId).all();

    return c.json({
      success: true,
      data: {
        ...exchangeData,
        return_items: returnItems.results,
        new_items: newItems.results
      }
    });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch exchange');
  }
});

// Create new exchange
app.post('/api/exchanges', async (c) => {
  try {
    const body = await c.req.json();
    const { 
      order_id, 
      return_items, // Array of {order_item_id, reason_id, quantity, note}
      new_items, // Array of {variant_id, quantity}
      customer_note,
      admin_note,
      shipping_address
    } = body;

    if (!order_id || (!return_items?.length && !new_items?.length)) {
      return c.json({ error: 'Order ID and items are required' }, 400);
    }

    // Verify order exists
    const order = await c.env.DB.prepare('SELECT * FROM orders WHERE id = ?').bind(order_id).first();
    if (!order) {
      return c.json({ error: 'Order not found' }, 404);
    }

    const exchangeId = generateId('exc');
    const exchangeNumber = `EXC-${Date.now()}`;

    // Calculate difference amount
    let returnValue = 0;
    let newItemsValue = 0;

    // Calculate return value
    if (return_items?.length) {
      for (const item of return_items) {
        const orderItem = await c.env.DB.prepare('SELECT * FROM order_items WHERE id = ?').bind(item.order_item_id).first();
        if (orderItem) {
          returnValue += ((orderItem.unit_price as number) * item.quantity);
        }
      }
    }

    // Calculate new items value
    if (new_items?.length) {
      for (const item of new_items) {
        const variant = await c.env.DB.prepare(`
          SELECT vp.price 
          FROM variant_prices vp 
          WHERE vp.variant_id = ? AND vp.currency_code = ?
          ORDER BY vp.created_at DESC 
          LIMIT 1
        `).bind(item.variant_id, order.currency_code).first();
        
        if (variant) {
          newItemsValue += ((variant.price as number) * item.quantity);
        }
      }
    }

    const differenceAmount = newItemsValue - returnValue;

    // Create exchange
    await c.env.DB.prepare(`
      INSERT INTO exchanges (
        id, order_id, status, exchange_number, customer_note, admin_note,
        difference_amount, currency_code, shipping_address, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      exchangeId, order_id, 'requested', exchangeNumber, customer_note || null, admin_note || null,
      differenceAmount, order.currency_code, shipping_address ? JSON.stringify(shipping_address) : null
    ).run();

    // Create return items
    if (return_items?.length) {
      for (const item of return_items) {
        const itemId = generateId('exc_ret_item');
        await c.env.DB.prepare(`
          INSERT INTO exchange_return_items (
            id, exchange_id, order_item_id, reason_id, quantity, note, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `).bind(
          itemId, exchangeId, item.order_item_id, item.reason_id || null, item.quantity, item.note || null
        ).run();
      }
    }

    // Create new items
    if (new_items?.length) {
      for (const item of new_items) {
        const itemId = generateId('exc_new_item');
        const variant = await c.env.DB.prepare(`
          SELECT vp.price 
          FROM variant_prices vp 
          WHERE vp.variant_id = ? AND vp.currency_code = ?
          ORDER BY vp.created_at DESC 
          LIMIT 1
        `).bind(item.variant_id, order.currency_code).first();
        
        const unitPrice = variant ? (variant.price as number) : 0;
        const totalPrice = unitPrice * item.quantity;

        await c.env.DB.prepare(`
          INSERT INTO exchange_new_items (
            id, exchange_id, variant_id, quantity, unit_price, total_price, note, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `).bind(
          itemId, exchangeId, item.variant_id, item.quantity, unitPrice, totalPrice, item.note || null
        ).run();
      }
    }

    return c.json({ 
      success: true, 
      data: { id: exchangeId, exchange_number: exchangeNumber, difference_amount: differenceAmount },
      message: 'Exchange created successfully' 
    }, 201);
  } catch (error) {
    return handleError(c, error, 'Failed to create exchange');
  }
});

// Update exchange status
app.put('/api/exchanges/:id/status', async (c) => {
  try {
    const exchangeId = c.req.param('id');
    const { status, admin_note, tracking_number, tracking_url } = await c.req.json();

    const validStatuses = ['requested', 'approved', 'processing', 'shipped', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    let updateData = [status];
    let setClauses = ['status = ?'];

    if (admin_note) {
      setClauses.push('admin_note = ?');
      updateData.push(admin_note);
    }

    if (tracking_number) {
      setClauses.push('tracking_number = ?');
      updateData.push(tracking_number);
    }

    if (tracking_url) {
      setClauses.push('tracking_url = ?');
      updateData.push(tracking_url);
    }

    // Add status-specific timestamp updates
    if (status === 'shipped') {
      setClauses.push('shipped_at = CURRENT_TIMESTAMP');
    } else if (status === 'completed') {
      setClauses.push('completed_at = CURRENT_TIMESTAMP');
    } else if (status === 'cancelled') {
      setClauses.push('cancelled_at = CURRENT_TIMESTAMP');
    }

    setClauses.push('updated_at = CURRENT_TIMESTAMP');
    updateData.push(exchangeId);

    await c.env.DB.prepare(`
      UPDATE exchanges 
      SET ${setClauses.join(', ')}
      WHERE id = ?
    `).bind(...updateData).run();

    return c.json({ success: true, message: 'Exchange status updated successfully' });
  } catch (error) {
    return handleError(c, error, 'Failed to update exchange status');
  }
});

// ===========================================
// SETTINGS ROUTES
// ===========================================

// Get all settings
app.get('/api/settings', async (c) => {
  try {
    const category = c.req.query('category');
    
    let query = 'SELECT * FROM settings WHERE 1=1';
    const params: any[] = [];
    
    if (category) {
      query += ' AND category = ?';
      params.push(category);
    }
    
    query += ' ORDER BY category, key';
    
    const settings = await c.env.DB.prepare(query).bind(...params).all();
    
    // Group settings by category
    const grouped: any = {};
    settings.results.forEach((setting: any) => {
      if (!grouped[setting.category]) {
        grouped[setting.category] = {};
      }
      try {
        grouped[setting.category][setting.key] = JSON.parse(String(setting.value));
      } catch {
        grouped[setting.category][setting.key] = setting.value;
      }
    });
    
    return c.json({ settings: category ? grouped[category] || {} : grouped });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch settings');
  }
});

// Get specific setting
// app.get('/api/settings/:key', async (c) => {
//   try {
//     const key = c.req.param('key');
    
//     const setting = await c.env.DB.prepare(`
//       SELECT * FROM settings WHERE key = ?
//     `).bind(key).first();
    
//     if (!setting) {
//       return c.json({ error: 'Setting not found' }, 404);
//     }
    
//     let value;
//     try {
//       value = JSON.parse(String(setting.value));
//     } catch {
//       value = setting.value;
//     }
    
//     return c.json({ 
//       key: setting.key,
//       value,
//       category: setting.category,
//       is_public: setting.is_public,
//       updated_at: setting.updated_at
//     });
    
//   } catch (error) {
//     return handleError(c, error, 'Failed to fetch setting');
//   }
// });

// Update settings (bulk update)
app.put('/api/settings', async (c) => {
  try {
    const { settings, category } = await c.req.json();
    
    if (!settings) {
      return c.json({ error: 'Settings object is required' }, 400);
    }
    
    // Begin transaction by preparing all statements
    const statements = [];
    
    for (const [key, value] of Object.entries(settings)) {
      const jsonValue = JSON.stringify(value);
      
      statements.push(
        c.env.DB.prepare(`
          INSERT INTO settings (key, value, category, updated_at)
          VALUES (?, ?, ?, CURRENT_TIMESTAMP)
          ON CONFLICT(key) DO UPDATE SET 
            value = excluded.value,
            category = COALESCE(excluded.category, category),
            updated_at = CURRENT_TIMESTAMP
        `).bind(key, jsonValue, category || 'general')
      );
    }
    
    // Execute all statements
    for (const stmt of statements) {
      await stmt.run();
    }
    
    return c.json({ message: 'Settings updated successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to update settings');
  }
});

// Update single setting
app.put('/api/settings/:key', async (c) => {
  try {
    const key = c.req.param('key');
    const { value, category, is_public } = await c.req.json();
    
    const jsonValue = JSON.stringify(value);
    
    await c.env.DB.prepare(`
      INSERT INTO settings (key, value, category, is_public, updated_at)
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
      ON CONFLICT(key) DO UPDATE SET 
        value = excluded.value,
        category = COALESCE(excluded.category, category),
        is_public = COALESCE(excluded.is_public, is_public),
        updated_at = CURRENT_TIMESTAMP
    `).bind(key, jsonValue, category || 'general', is_public || false).run();
    
    return c.json({ message: 'Setting updated successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to update setting');
  }
});

// Delete setting
app.delete('/api/settings/:key', async (c) => {
  try {
    const key = c.req.param('key');
    
    await c.env.DB.prepare(`
      DELETE FROM settings WHERE key = ?
    `).bind(key).run();
    
    return c.json({ message: 'Setting deleted successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to delete setting');
  }
});

// Get all currencies
app.get('/api/currencies', async (c) => {
  try {
    const currenciesResult = await c.env.DB.prepare(`
      SELECT * FROM currencies ORDER BY code
    `).all();
    
    return c.json({ currencies: currenciesResult.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch currencies');
  }
});

// Create/Update currency
app.post('/api/currencies', async (c) => {
  try {
    const body = await c.req.json();
    const { code, name, symbol, symbol_native, decimal_digits, /* exchange_rate, */ is_active, is_default } = body; // decimal_places from schema, but route uses decimal_digits

    if (!code || !name || !symbol) {
      return c.json({ error: 'Code, name, and symbol are required' }, 400);
    }

    const upperCode = String(code).toUpperCase();
    // const nativeSymbol = symbol_native || symbol; // Schema has symbol_native as NOT NULL, but not in simplified.ts body destructuring
    // const digits = typeof decimal_digits === 'number' && !isNaN(decimal_digits) ? decimal_digits : 2; // Schema uses decimal_places
    const decimalPlaces = typeof decimal_digits === 'number' && !isNaN(decimal_digits) ? decimal_digits : (typeof body.decimal_places === 'number' && !isNaN(body.decimal_places) ? body.decimal_places : 2) ;
    const isActive = is_active === true || is_active === 1 || String(is_active).toLowerCase() === 'true';
    const shouldBeDefault = is_default === true || is_default === 1 || String(is_default).toLowerCase() === 'true';
    const exchangeRate = typeof body.exchange_rate === 'number' && !isNaN(body.exchange_rate) ? body.exchange_rate : 1.0;


    // If this currency is being set as default, unset any other existing default currency
    if (shouldBeDefault) {
      await c.env.DB.prepare(`
        UPDATE currencies SET is_default = 0 WHERE is_default = 1 AND code != ?
      `).bind(upperCode).run();
    }

    // Try to update existing currency
    // Note: simplified_ecommerce_schema.sql for `currencies` does not have rounding or raw_rounding, but has decimal_places, exchange_rate, is_active
    const updateResult = await c.env.DB.prepare(`
      UPDATE currencies 
      SET name = ?, 
          symbol = ?, 
          -- symbol_native = ?,  -- Not in simplified.ts body, but in schema as NOT NULL
          decimal_places = ?, 
          exchange_rate = ?, 
          is_active = ?,
          is_default = ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE code = ?
    `).bind(
      name,
      symbol,
      // nativeSymbol, 
      decimalPlaces,
      exchangeRate,
      isActive ? 1 : 0,
      shouldBeDefault ? 1 : 0,
      upperCode
    ).run();

    // If no rows were updated (currency didn't exist), insert a new one
    if (updateResult.meta.changes === 0) {
      await c.env.DB.prepare(`
        INSERT INTO currencies (
          code, name, symbol, decimal_places, exchange_rate, is_default, is_active, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `).bind(
        upperCode,
        name,
        symbol,
        decimalPlaces,
        exchangeRate,
        shouldBeDefault ? 1 : 0,
        isActive ? 1 : 0
      ).run();
    }
    
    if (shouldBeDefault) {
        await c.env.DB.prepare('UPDATE currencies SET is_default = 1 WHERE code = ?')
          .bind(upperCode)
          .run();
    }

    return c.json({ message: 'Currency saved successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to save currency');
  }
});

// Get all regions
app.get('/api/regions', async (c) => {
  try {
    const regions = await c.env.DB.prepare(`
      SELECT * FROM region ORDER BY name
    `).all();
    
    return c.json({ regions: regions.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch regions');
  }
});

// Create/Update region
app.post('/api/regions', async (c) => {
  try {
    const { id, name, currency_code, automatic_taxes, metadata } = await c.req.json();
    
    if (!name || !currency_code) {
      return c.json({ error: 'Name and currency code are required' }, 400);
    }
    
    const regionId = id || generateId('reg');
    
    await c.env.DB.prepare(`
      INSERT INTO region (
        id, name, currency_code, automatic_taxes, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      ON CONFLICT(id) DO UPDATE SET 
        name = excluded.name,
        currency_code = excluded.currency_code,
        automatic_taxes = excluded.automatic_taxes,
        metadata = excluded.metadata,
        updated_at = CURRENT_TIMESTAMP
    `).bind(
      regionId,
      name,
      currency_code,
      automatic_taxes || 1,
      JSON.stringify(metadata || {})
    ).run();
    
    return c.json({ id: regionId, message: 'Region saved successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to save region');
  }
});

// Get tax rates
app.get('/api/tax-rates', async (c) => {
  try {
    const taxRates = await c.env.DB.prepare(`
      SELECT 
        tr.*,
        treg.country_code,
        treg.province_code
      FROM tax_rate tr
      LEFT JOIN tax_region treg ON tr.tax_region_id = treg.id
      WHERE tr.deleted_at IS NULL
      ORDER BY tr.name
    `).all();
    
    return c.json({ tax_rates: taxRates.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch tax rates');
  }
});

// Create tax rate
app.post('/api/tax-rates', async (c) => {
  try {
    const { name, code, rate, country_code, province_code, is_default, is_combinable } = await c.req.json();
    
    if (!name || !code || rate === undefined) {
      return c.json({ error: 'Name, code, and rate are required' }, 400);
    }
    
    // Create tax region first
    const taxRegionId = generateId('treg');
    await c.env.DB.prepare(`
      INSERT INTO tax_region (
        id, country_code, province_code, created_at, updated_at
      ) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(taxRegionId, country_code, province_code).run();
    
    // Create tax rate
    const taxRateId = generateId('tr');
    await c.env.DB.prepare(`
      INSERT INTO tax_rate (
        id, rate, code, name, is_default, is_combinable, tax_region_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      taxRateId,
      rate,
      code,
      name,
      is_default || 0,
      is_combinable || 0,
      taxRegionId
    ).run();
    
    return c.json({ id: taxRateId, message: 'Tax rate created successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create tax rate');
  }
});

// Get shipping zones
app.get('/api/shipping-zones', async (c) => {
  try {
    const zones = await c.env.DB.prepare(`
      SELECT * FROM service_zone ORDER BY name
    `).all();
    
    return c.json({ shipping_zones: zones.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch shipping zones');
  }
});

// Create shipping zone
app.post('/api/shipping-zones', async (c) => {
  try {
    const { name, fulfillment_set_id, metadata } = await c.req.json();
    
    if (!name) {
      return c.json({ error: 'Name is required' }, 400);
    }
    
    const zoneId = generateId('sz');
    
    await c.env.DB.prepare(`
      INSERT INTO service_zone (
        id, name, fulfillment_set_id, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      zoneId,
      name,
      fulfillment_set_id || null,
      JSON.stringify(metadata || {})
    ).run();
    
    return c.json({ id: zoneId, message: 'Shipping zone created successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create shipping zone');
  }
});

// Get payment providers
app.get('/api/payment-providers', async (c) => {
  try {
    const providers = await c.env.DB.prepare(`
      SELECT * FROM payment_provider ORDER BY id
    `).all();
    
    return c.json({ payment_providers: providers.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch payment providers');
  }
});

// Update payment provider
app.put('/api/payment-providers/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const { is_enabled } = await c.req.json();
    
    await c.env.DB.prepare(`
      UPDATE payment_provider 
      SET is_enabled = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(is_enabled ? 1 : 0, id).run();
    
    return c.json({ message: 'Payment provider updated successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to update payment provider');
  }
});

// Get webhooks
app.get('/api/webhooks', async (c) => {
  try {
    const webhooks = await c.env.DB.prepare(`
      SELECT id, name, url, events, is_active, last_success_at, last_error, created_at, updated_at
      FROM webhook_deliveries
      ORDER BY created_at DESC
    `).all();
    
    return c.json({ webhooks: webhooks.results || [] });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch webhooks');
  }
});

// Create webhook
app.post('/api/webhooks', async (c) => {
  try {
    const { name, url, events, is_active } = await c.req.json();
    
    if (!name || !url || !events) {
      return c.json({ error: 'Name, URL, and events are required' }, 400);
    }
    
    const webhookId = generateId('wh');
    const secret = generateId('whsec'); // Generate webhook secret
    
    await c.env.DB.prepare(`
      INSERT INTO webhooks (
        id, name, url, events, is_active, secret, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      webhookId,
      name,
      url,
      JSON.stringify(events),
      is_active ? 1 : 0,
      secret
    ).run();
    
    return c.json({ 
      id: webhookId, 
      secret,
      message: 'Webhook created successfully' 
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create webhook');
  }
});

// Test webhook
app.post('/api/webhooks/:id/test', async (c) => {
  try {
    const id = c.req.param('id');
    
    const webhook = await c.env.DB.prepare(`
      SELECT * FROM webhooks WHERE id = ?
    `).bind(id).first();
    
    if (!webhook) {
      return c.json({ error: 'Webhook not found' }, 404);
    }
    
    // Send test payload
    const testPayload = {
      event: 'webhook.test',
      data: {
        message: 'This is a test webhook from your store',
        timestamp: new Date().toISOString()
      }
    };
    
    try {
      const response = await fetch(String(webhook.url), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Signature': String(webhook.secret)
        },
        body: JSON.stringify(testPayload)
      });
      
      // Log delivery attempt
      await c.env.DB.prepare(`
        INSERT INTO webhook_deliveries (
          id, webhook_id, event_type, payload, response_status, delivered_at, created_at
        ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        generateId('whd'),
        id,
        'webhook.test',
        JSON.stringify(testPayload),
        response.status
      ).run();
      
      if (response.ok) {
        await c.env.DB.prepare(`
          UPDATE webhooks 
          SET last_success_at = CURRENT_TIMESTAMP, last_error = NULL, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(id).run();
        
        return c.json({ message: 'Webhook test successful', status: response.status });
      } else {
        const errorText = await response.text();
        await c.env.DB.prepare(`
          UPDATE webhooks 
          SET last_error = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(`HTTP ${response.status}: ${errorText}`, id).run();
        
        return c.json({ 
          error: 'Webhook test failed', 
          status: response.status,
          details: errorText 
        }, 400);
      }
      
    } catch (fetchError: any) {
      await c.env.DB.prepare(`
        UPDATE webhooks 
        SET last_error = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(fetchError.message, id).run();
      
      return c.json({ 
        error: 'Failed to send webhook', 
        details: fetchError.message 
      }, 400);
    }
    
  } catch (error) {
    return handleError(c, error, 'Failed to test webhook');
  }
});

// Settings schema/structure for frontend
app.get('/api/settings/schema', async (c) => {
  try {
    const schema = {
      general: {
        title: 'General Settings',
        description: 'Basic store information and defaults',
        fields: {
          store_name: { type: 'text', label: 'Store Name', required: true },
          store_description: { type: 'textarea', label: 'Store Description' },
          store_email: { type: 'email', label: 'Store Email', required: true },
          store_phone: { type: 'tel', label: 'Store Phone' },
          store_address: { type: 'textarea', label: 'Store Address' },
          default_currency: { type: 'select', label: 'Default Currency', options: 'currencies' },
          default_language: { type: 'select', label: 'Default Language', options: 'languages' },
          timezone: { type: 'select', label: 'Timezone', options: 'timezones' }
        }
      },
      localization: {
        title: 'Localization',
        description: 'Multi-language and multi-currency settings',
        fields: {
          enable_multi_currency: { type: 'boolean', label: 'Enable Multi-Currency' },
          enable_multi_language: { type: 'boolean', label: 'Enable Multi-Language' },
          auto_detect_currency: { type: 'boolean', label: 'Auto-detect Currency by Location' },
          auto_detect_language: { type: 'boolean', label: 'Auto-detect Language by Browser' }
        }
      },
      tax: {
        title: 'Tax Settings',
        description: 'Tax calculation and display preferences',
        fields: {
          tax_inclusive: { type: 'boolean', label: 'Prices Include Tax' },
          tax_calculation: { type: 'select', label: 'Tax Calculation', options: [
            { value: 'automatic', label: 'Automatic' },
            { value: 'manual', label: 'Manual' }
          ]},
          display_tax_breakdown: { type: 'boolean', label: 'Show Tax Breakdown to Customers' },
          require_tax_exempt_certificate: { type: 'boolean', label: 'Require Tax Exempt Certificate' }
        }
      },
      inventory: {
        title: 'Inventory Management',
        description: 'Stock tracking and inventory alerts',
        fields: {
          enable_inventory_tracking: { type: 'boolean', label: 'Enable Inventory Tracking' },
          allow_backorders: { type: 'boolean', label: 'Allow Backorders' },
          low_stock_threshold: { type: 'number', label: 'Low Stock Alert Threshold', min: 0 },
          out_of_stock_behavior: { type: 'select', label: 'Out of Stock Behavior', options: [
            { value: 'hide', label: 'Hide Product' },
            { value: 'show_unavailable', label: 'Show as Unavailable' },
            { value: 'allow_backorder', label: 'Allow Backorder' }
          ]},
          send_low_stock_alerts: { type: 'boolean', label: 'Send Low Stock Email Alerts' }
        }
      },
      shipping: {
        title: 'Shipping Settings',
        description: 'Shipping options and fulfillment',
        fields: {
          require_shipping_address: { type: 'boolean', label: 'Require Shipping Address' },
          enable_local_delivery: { type: 'boolean', label: 'Enable Local Delivery' },
          enable_pickup: { type: 'boolean', label: 'Enable Store Pickup' },
          default_shipping_method: { type: 'select', label: 'Default Shipping Method', options: 'shipping_methods' },
          free_shipping_threshold: { type: 'number', label: 'Free Shipping Threshold', min: 0 }
        }
      },
      payments: {
        title: 'Payment Settings',
        description: 'Payment gateway and processing options',
        fields: {
          enable_guest_checkout: { type: 'boolean', label: 'Enable Guest Checkout' },
          require_payment_confirmation: { type: 'boolean', label: 'Require Payment Confirmation' },
          payment_terms: { type: 'textarea', label: 'Payment Terms and Conditions' },
          accepted_payment_methods: { type: 'multiselect', label: 'Accepted Payment Methods', options: [
            { value: 'credit_card', label: 'Credit Card' },
            { value: 'paypal', label: 'PayPal' },
            { value: 'apple_pay', label: 'Apple Pay' },
            { value: 'google_pay', label: 'Google Pay' },
            { value: 'bank_transfer', label: 'Bank Transfer' }
          ]}
        }
      },
      security: {
        title: 'Security Settings',
        description: 'Security and authentication preferences',
        fields: {
          require_account_activation: { type: 'boolean', label: 'Require Email Activation for New Accounts' },
          enable_two_factor: { type: 'boolean', label: 'Enable Two-Factor Authentication' },
          session_timeout: { type: 'number', label: 'Session Timeout (minutes)', min: 1 },
          password_min_length: { type: 'number', label: 'Minimum Password Length', min: 6 },
          enable_captcha: { type: 'boolean', label: 'Enable CAPTCHA on Forms' }
        }
      },
      notifications: {
        title: 'Notifications',
        description: 'Email and webhook notification settings',
        fields: {
          enable_order_notifications: { type: 'boolean', label: 'Send Order Confirmation Emails' },
          enable_shipping_notifications: { type: 'boolean', label: 'Send Shipping Notification Emails' },
          enable_low_stock_notifications: { type: 'boolean', label: 'Send Low Stock Alerts' },
          notification_email: { type: 'email', label: 'Notification Email Address' },
          enable_customer_notifications: { type: 'boolean', label: 'Allow Customer Marketing Emails' }
        }
      },
      seo: {
        title: 'SEO Settings',
        description: 'Search engine optimization settings',
        fields: {
          meta_title: { type: 'text', label: 'Default Meta Title' },
          meta_description: { type: 'textarea', label: 'Default Meta Description' },
          enable_sitemap: { type: 'boolean', label: 'Generate XML Sitemap' },
          enable_robots_txt: { type: 'boolean', label: 'Generate robots.txt' },
          google_analytics_id: { type: 'text', label: 'Google Analytics ID' },
          facebook_pixel_id: { type: 'text', label: 'Facebook Pixel ID' }
        }
      },
      advanced: {
        title: 'Advanced Settings',
        description: 'Advanced configuration options',
        fields: {
          enable_api: { type: 'boolean', label: 'Enable REST API' },
          enable_webhooks: { type: 'boolean', label: 'Enable Webhooks' },
          custom_css: { type: 'textarea', label: 'Custom CSS', rows: 10 },
          custom_javascript: { type: 'textarea', label: 'Custom JavaScript', rows: 10 },
          maintenance_mode: { type: 'boolean', label: 'Enable Maintenance Mode' },
          maintenance_message: { type: 'textarea', label: 'Maintenance Message' }
        }
      }
    };
    
    return c.json({ schema });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch settings schema');
  }
});

// ===========================================
// PRODUCT TYPES ROUTES
// ===========================================

// Get all product types
app.get('/api/product-types', async (c) => {
  try {
    const productTypes = await c.env.DB.prepare(`
      SELECT id, name, metadata, created_at
      FROM product_types
      ORDER BY name ASC
    `).all();

    return c.json({
      success: true,
      product_types: productTypes.results || [] 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product types');
  }
});

// ===========================================
// CATEGORIES ROUTES
// ===========================================

// Get all categories
app.get('/api/categories', async (c) => {
  try {
    const categories = await c.env.DB.prepare(`
      SELECT 
        c.id, c.parent_id, c.handle, c.sort_order, c.is_active, c.metadata, c.created_at,
        ct.name, ct.description, ct.seo_title, ct.seo_description
      FROM categories c
      LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.language_code = 'en'
      WHERE c.deleted_at IS NULL
      ORDER BY c.sort_order, c.created_at DESC
    `).all();

    return c.json({ 
      success: true,
      categories: categories.results || [] 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch categories');
  }
});

// ===========================================
// CUSTOMER GROUPS ROUTES
// ===========================================

// Get all customer groups
app.get('/api/customer-groups', async (c) => {
  try {
    const search = c.req.query('search');
    
    let whereClause = 'deleted_at IS NULL';
    let params = [];

    if (search) {
      whereClause += ' AND (name LIKE ? OR description LIKE ?)';
      const searchParam = `%${search}%`;
      params.push(searchParam, searchParam);
    }

    const groups = await c.env.DB.prepare(`
      SELECT 
        cg.*,
        COUNT(c.id) as customer_count
      FROM customer_groups cg
      LEFT JOIN customers c ON cg.id = c.group_id AND c.deleted_at IS NULL
      WHERE ${whereClause}
      GROUP BY cg.id
      ORDER BY cg.created_at DESC
    `).bind(...params).all();

    // Add additional stats for each group
    const groupsWithStats = await Promise.all(
      groups.results.map(async (group) => {
        const stats = await c.env.DB.prepare(`
          SELECT 
            COUNT(DISTINCT o.id) as total_orders,
            COALESCE(SUM(o.total_amount), 0) as total_spent
          FROM orders o
          JOIN customers c ON o.customer_id = c.id
          WHERE c.group_id = ? AND c.deleted_at IS NULL AND o.deleted_at IS NULL
        `).bind(group.id).first();

        return {
          ...group,
          total_orders: stats?.total_orders || 0,
          total_spent: stats?.total_spent || 0,
        };
      })
    );

    return c.json({ groups: groupsWithStats });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch customer groups');
  }
});

// Create customer group
app.post('/api/customer-groups', async (c) => {
  try {
    const { name, description, discount_percentage, metadata } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'Group name is required' }, 400);
    }

    const id = crypto.randomUUID();

    await c.env.DB.prepare(`
      INSERT INTO customer_groups (
        id, name, description, discount_percentage, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      id,
      name.trim(),
      description?.trim() || null,
      discount_percentage || 0,
      metadata ? JSON.stringify(metadata) : null
    ).run();

    return c.json({ 
      message: 'Customer group created successfully',
      group_id: id 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create customer group');
  }
});

// Update customer group
app.put('/api/customer-groups/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const { name, description, discount_percentage, metadata } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'Group name is required' }, 400);
    }

    await c.env.DB.prepare(`
      UPDATE customer_groups 
      SET 
        name = ?, 
        description = ?, 
        discount_percentage = ?, 
        metadata = ?, 
        updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      name.trim(),
      description?.trim() || null,
      discount_percentage || 0,
      metadata ? JSON.stringify(metadata) : null,
      id
    ).run();

    return c.json({ message: 'Customer group updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update customer group');
  }
});

// Delete customer group
app.delete('/api/customer-groups/:id', async (c) => {
  try {
    const id = c.req.param('id');

    // Soft delete the group
    await c.env.DB.prepare(`
      UPDATE customer_groups 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    // Remove group assignment from customers
    await c.env.DB.prepare(`
      UPDATE customers 
      SET group_id = NULL, updated_at = CURRENT_TIMESTAMP
      WHERE group_id = ?
    `).bind(id).run();

    return c.json({ message: 'Customer group deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete customer group');
  }
});

// Bulk delete customer groups
app.post('/api/customer-groups/bulk-delete', async (c) => {
  try {
    const { groupIds } = await c.req.json();

    if (!groupIds || !Array.isArray(groupIds) || groupIds.length === 0) {
      return c.json({ error: 'Group IDs are required' }, 400);
    }

    const placeholders = groupIds.map(() => '?').join(',');
    
    // Soft delete the groups
    await c.env.DB.prepare(`
      UPDATE customer_groups 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...groupIds).run();

    // Remove group assignments from customers
    await c.env.DB.prepare(`
      UPDATE customers 
      SET group_id = NULL, updated_at = CURRENT_TIMESTAMP
      WHERE group_id IN (${placeholders})
    `).bind(...groupIds).run();

    return c.json({ 
      message: `${groupIds.length} customer groups deleted successfully` 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to delete customer groups');
  }
});

// ===========================================
// TAGS ROUTES
// ===========================================

// Get all tags from products (extract from products.tags JSON)
app.get('/api/tags', async (c) => {
  try {
    const products = await c.env.DB.prepare(`
      SELECT tags FROM products 
      WHERE deleted_at IS NULL AND tags IS NOT NULL AND tags != '[]'
    `).all();

    const tagsMap = new Map();
    
    for (const product of products.results) {
      if (product.tags) {
        try {
          const productTags = JSON.parse(String(product.tags));
          if (Array.isArray(productTags)) {
            productTags.forEach(tag => {
              if (tag && typeof tag === 'string') {
                const count = tagsMap.get(tag) || 0;
                tagsMap.set(tag, count + 1);
              }
            });
          }
        } catch (e) {
          // Skip invalid JSON
        }
      }
    }

    const tags = Array.from(tagsMap.entries()).map(([name, usage_count]) => ({
      id: name.toLowerCase().replace(/\s+/g, '-'),
      name,
      usage_count,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })).sort((a, b) => b.usage_count - a.usage_count);

    return c.json({ tags });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch tags');
  }
});

// Delete a tag (remove from all products)
app.delete('/api/tags/:tagName', async (c) => {
  try {
    const tagName = decodeURIComponent(c.req.param('tagName'));

    // Get all products that have this tag
    const products = await c.env.DB.prepare(`
      SELECT id, tags FROM products 
      WHERE deleted_at IS NULL AND tags IS NOT NULL AND tags != '[]'
    `).all();

    const updates = [];
    
    for (const product of products.results) {
      if (product.tags) {
        try {
          const productTags = JSON.parse(String(product.tags));
          if (Array.isArray(productTags) && productTags.includes(tagName)) {
            const newTags = productTags.filter(tag => tag !== tagName);
            updates.push({
              id: product.id,
              tags: JSON.stringify(newTags)
            });
          }
        } catch (e) {
          // Skip invalid JSON
        }
      }
    }

    // Update all products
    for (const update of updates) {
      await c.env.DB.prepare(`
        UPDATE products 
        SET tags = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `).bind(update.tags, update.id).run();
    }

    return c.json({ 
      message: 'Tag deleted successfully',
      updated_products: updates.length 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to delete tag');
  }
});

// ===========================================
// INVENTORY MANAGEMENT ROUTES
// ===========================================

// INVENTORY ITEMS ROUTES

// Get all inventory items
app.get('/api/inventory/items', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const lowStock = c.req.query('low_stock') === 'true';
    const requiresShipping = c.req.query('requires_shipping');
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        ii.id, ii.sku, ii.title, ii.description, ii.thumbnail, ii.origin_country,
        ii.hs_code, ii.mid_code, ii.material, ii.weight, ii.length, ii.height, ii.width,
        ii.requires_shipping, ii.metadata, ii.created_at, ii.updated_at,
        COALESCE(SUM(il.stocked_quantity), 0) as total_quantity,
        COALESCE(SUM(il.stocked_quantity - il.reserved_quantity), 0) as available_quantity,
        COALESCE(SUM(il.reserved_quantity), 0) as reserved_quantity,
        COUNT(DISTINCT il.location_id) as location_count
      FROM inventory_item ii
      LEFT JOIN inventory_level il ON ii.id = il.inventory_item_id
      WHERE ii.deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (ii.sku LIKE ? OR ii.title LIKE ? OR ii.description LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (requiresShipping && requiresShipping !== 'all') {
      query += ` AND ii.requires_shipping = ?`;
      params.push(requiresShipping === 'true' ? 1 : 0);
    }

    query += ` GROUP BY ii.id`;
    
    if (lowStock) {
      query += ` HAVING available_quantity < 10`;
    }

    query += ` ORDER BY ii.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const items = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `SELECT COUNT(DISTINCT ii.id) as total FROM inventory_item ii`;
    let countParams = [];
    
    if (search || requiresShipping) {
      countQuery += ` WHERE ii.deleted_at IS NULL`;
      
      if (search) {
        countQuery += ` AND (ii.sku LIKE ? OR ii.title LIKE ? OR ii.description LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }
      
      if (requiresShipping && requiresShipping !== 'all') {
        countQuery += ` AND ii.requires_shipping = ?`;
        countParams.push(requiresShipping === 'true' ? 1 : 0);
      }
    } else {
      countQuery += ` WHERE ii.deleted_at IS NULL`;
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      items: items.results,
      total: totalResult?.total || 0,
      page,
      limit,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch inventory items');
  }
});

// Create inventory item
app.post('/api/inventory/items', async (c) => {
  try {
    const data = await c.req.json();
    const id = generateId('iitem');

    await c.env.DB.prepare(`
      INSERT INTO inventory_item (
        id, sku, title, description, thumbnail, origin_country, hs_code, mid_code,
        material, weight, length, height, width, requires_shipping, metadata,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      id, data.sku, data.title, data.description || null, data.thumbnail || null,
      data.origin_country || null, data.hs_code || null, data.mid_code || null,
      data.material || null, data.weight || null, data.length || null,
      data.height || null, data.width || null, data.requires_shipping ? 1 : 0,
      JSON.stringify(data.metadata || {})
    ).run();

    return c.json({ message: 'Inventory item created successfully', id });

  } catch (error) {
    return handleError(c, error, 'Failed to create inventory item');
  }
});

// Update inventory item
app.put('/api/inventory/items/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const data = await c.req.json();
    
    await c.env.DB.prepare(`
      UPDATE inventory_item SET 
        sku = ?, title = ?, description = ?, thumbnail = ?, origin_country = ?,
        hs_code = ?, mid_code = ?, material = ?, weight = ?, length = ?, height = ?,
        width = ?, requires_shipping = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      data.sku, data.title, data.description || null, data.thumbnail || null,
      data.origin_country || null, data.hs_code || null, data.mid_code || null,
      data.material || null, data.weight || null, data.length || null,
      data.height || null, data.width || null, data.requires_shipping ? 1 : 0,
      JSON.stringify(data.metadata || {}), id
    ).run();

    return c.json({ message: 'Inventory item updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update inventory item');
  }
});

// Delete inventory item
app.delete('/api/inventory/items/:id', async (c) => {
  try {
    const id = c.req.param('id');
    
    await c.env.DB.prepare(`
      UPDATE inventory_item 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'Inventory item deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete inventory item');
  }
});

// STOCK LOCATIONS ROUTES

// Get all stock locations
app.get('/api/inventory/locations', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        sl.id, sl.name, sl.metadata, sl.created_at, sl.updated_at,
        sla.id as address_id, sla.address_1, sla.address_2, sla.company, sla.city,
        sla.country_code, sla.phone, sla.province, sla.postal_code,
        COUNT(DISTINCT il.inventory_item_id) as item_count,
        COALESCE(SUM(il.stocked_quantity), 0) as total_inventory,
        COUNT(CASE WHEN il.stocked_quantity - il.reserved_quantity < 10 THEN 1 END) as low_stock_items
      FROM stock_location sl
      LEFT JOIN stock_location_address sla ON sl.address_id = sla.id
      LEFT JOIN inventory_level il ON sl.id = il.location_id
      WHERE sl.deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (sl.name LIKE ? OR sla.city LIKE ? OR sla.country_code LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    query += ` GROUP BY sl.id ORDER BY sl.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const locations = await c.env.DB.prepare(query).bind(...params).all();

    // Format results with nested address
    const formattedLocations = locations.results.map(loc => ({
      id: loc.id,
      name: loc.name,
      metadata: loc.metadata ? JSON.parse(String(loc.metadata)) : null,
      created_at: loc.created_at,
      updated_at: loc.updated_at,
      item_count: loc.item_count,
      total_inventory: loc.total_inventory,
      low_stock_items: loc.low_stock_items,
      address: loc.address_id ? {
        id: loc.address_id,
        address_1: loc.address_1,
        address_2: loc.address_2,
        company: loc.company,
        city: loc.city,
        country_code: loc.country_code,
        phone: loc.phone,
        province: loc.province,
        postal_code: loc.postal_code,
      } : null,
    }));

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM stock_location sl WHERE sl.deleted_at IS NULL`;
    let countParams = [];
    
    if (search) {
      countQuery += ` AND EXISTS (
        SELECT 1 FROM stock_location_address sla 
        WHERE sl.address_id = sla.id 
        AND (sl.name LIKE ? OR sla.city LIKE ? OR sla.country_code LIKE ?)
      )`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      locations: formattedLocations,
      total: totalResult?.total || 0,
      page,
      limit,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch stock locations');
  }
});

// Create stock location
app.post('/api/inventory/locations', async (c) => {
  try {
    const data = await c.req.json();
    const locationId = generateId('sloc');
    let addressId = null;
    
    // Create address first if provided
    if (data.address && data.address.address_1) {
      addressId = generateId('sladdr');
      await c.env.DB.prepare(`
        INSERT INTO stock_location_address (
          id, address_1, address_2, company, city,
          country_code, phone, province, postal_code, metadata,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        addressId, data.address.address_1, data.address.address_2 || null,
        data.address.company || null, data.address.city, data.address.country_code,
        data.address.phone || null, data.address.province || null,
        data.address.postal_code, JSON.stringify({})
      ).run();
    }
    
    // Create location with address_id reference
    await c.env.DB.prepare(`
      INSERT INTO stock_location (id, name, address_id, metadata, created_at, updated_at)
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(locationId, data.name, addressId, JSON.stringify(data.metadata || {})).run();

    return c.json({ message: 'Stock location created successfully', id: locationId });

  } catch (error) {
    return handleError(c, error, 'Failed to create stock location');
  }
});

// Update stock location
app.put('/api/inventory/locations/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const data = await c.req.json();
    
    // Get current location to check existing address_id
    const currentLocation = await c.env.DB.prepare(`
      SELECT address_id FROM stock_location WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first();
    
    let addressId = currentLocation?.address_id || null;
    
    // Handle address update/creation
    if (data.address && data.address.address_1) {
      if (addressId) {
        // Update existing address
        await c.env.DB.prepare(`
          UPDATE stock_location_address SET 
            address_1 = ?, address_2 = ?, company = ?, city = ?, country_code = ?,
            phone = ?, province = ?, postal_code = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(
          data.address.address_1, data.address.address_2 || null,
          data.address.company || null, data.address.city, data.address.country_code,
          data.address.phone || null, data.address.province || null,
          data.address.postal_code, addressId
        ).run();
      } else {
        // Create new address
        addressId = generateId('sladdr');
        await c.env.DB.prepare(`
          INSERT INTO stock_location_address (
            id, address_1, address_2, company, city,
            country_code, phone, province, postal_code, metadata,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `).bind(
          addressId, data.address.address_1, data.address.address_2 || null,
          data.address.company || null, data.address.city, data.address.country_code,
          data.address.phone || null, data.address.province || null,
          data.address.postal_code, JSON.stringify({})
        ).run();
      }
    }
    
    // Update location
    await c.env.DB.prepare(`
      UPDATE stock_location 
      SET name = ?, address_id = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(data.name, addressId, JSON.stringify(data.metadata || {}), id).run();

    return c.json({ message: 'Stock location updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update stock location');
  }
});

// Delete stock location
app.delete('/api/inventory/locations/:id', async (c) => {
  try {
    const id = c.req.param('id');
    
    await c.env.DB.prepare(`
      UPDATE stock_location 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'Stock location deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete stock location');
  }
});

// INVENTORY LEVELS ROUTES

// Get all inventory levels
app.get('/api/inventory/levels', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const locationId = c.req.query('location_id');
    const lowStock = c.req.query('low_stock') === 'true';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        il.id, il.inventory_item_id, il.location_id, il.stocked_quantity,
        il.reserved_quantity, il.incoming_quantity, 
        (il.stocked_quantity - il.reserved_quantity) as available_quantity,
        il.metadata, il.created_at, il.updated_at,
        ii.sku, ii.title, ii.thumbnail,
        sl.name as location_name
      FROM inventory_level il
      JOIN inventory_item ii ON il.inventory_item_id = ii.id
      JOIN stock_location sl ON il.location_id = sl.id
      WHERE ii.deleted_at IS NULL AND sl.deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (ii.sku LIKE ? OR ii.title LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (locationId) {
      query += ` AND il.location_id = ?`;
      params.push(locationId);
    }
    
    if (lowStock) {
      query += ` AND (il.stocked_quantity - il.reserved_quantity) < 10`;
    }

    query += ` ORDER BY il.updated_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const levels = await c.env.DB.prepare(query).bind(...params).all();

    // Format results
    const formattedLevels = levels.results.map(level => ({
      id: level.id,
      inventory_item_id: level.inventory_item_id,
      location_id: level.location_id,
      stocked_quantity: level.stocked_quantity,
      reserved_quantity: level.reserved_quantity,
      incoming_quantity: level.incoming_quantity,
      available_quantity: level.available_quantity,
      metadata: level.metadata ? JSON.parse(String(level.metadata)) : null,
      created_at: level.created_at,
      updated_at: level.updated_at,
      item: {
        id: level.inventory_item_id,
        sku: level.sku,
        title: level.title,
        thumbnail: level.thumbnail,
      },
      location: {
        id: level.location_id,
        name: level.location_name,
      },
    }));

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM inventory_level il
      JOIN inventory_item ii ON il.inventory_item_id = ii.id
      JOIN stock_location sl ON il.location_id = sl.id
      WHERE ii.deleted_at IS NULL AND sl.deleted_at IS NULL
    `;
    let countParams = [];
    
    if (search) {
      countQuery += ` AND (ii.sku LIKE ? OR ii.title LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`);
    }
    
    if (locationId) {
      countQuery += ` AND il.location_id = ?`;
      countParams.push(locationId);
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      levels: formattedLevels,
      total: totalResult?.total || 0,
      page,
      limit,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch inventory levels');
  }
});

// INVENTORY ADJUSTMENTS ROUTES

// Create inventory adjustment
app.post('/api/inventory/adjustments', async (c) => {
  try {
    const data = await c.req.json();
    const { inventory_level_id, adjustment_type, quantity, reason, reference } = data;
    
    // Get current level
    const level = await c.env.DB.prepare(`
      SELECT stocked_quantity FROM inventory_level WHERE id = ?
    `).bind(inventory_level_id).first();

    if (!level) {
      return c.json({ error: 'Inventory level not found' }, 404);
    }

    let newQuantity;
    switch (adjustment_type) {
      case 'increase':
        newQuantity = Number(level.stocked_quantity) + quantity;
        break;
      case 'decrease':
        newQuantity = Math.max(0, Number(level.stocked_quantity) - quantity);
        break;
      case 'set':
        newQuantity = quantity;
        break;
      default:
        return c.json({ error: 'Invalid adjustment type' }, 400);
    }

    // Update inventory level
    await c.env.DB.prepare(`
      UPDATE inventory_level 
      SET stocked_quantity = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(newQuantity, inventory_level_id).run();

    // Log the adjustment (you could create an adjustments table for this)
    
    return c.json({ 
      message: 'Stock adjustment applied successfully',
      old_quantity: level.stocked_quantity,
      new_quantity: newQuantity
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create stock adjustment');
  }
});

// INVENTORY RESERVATIONS ROUTES

// Get all inventory reservations
app.get('/api/inventory/reservations', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const locationId = c.req.query('location_id');
    const status = c.req.query('status');
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        ri.id, ri.line_item_id, ri.inventory_item_id, ri.location_id,
        ri.quantity, ri.description, ri.created_by, ri.metadata,
        ri.created_at, ri.updated_at,
        ii.sku, ii.title, ii.thumbnail,
        sl.name as location_name,
        oi.quantity as line_item_quantity,
        o.id as order_id, o.display_id as order_display_id
      FROM reservation_item ri
      JOIN inventory_item ii ON ri.inventory_item_id = ii.id
      JOIN stock_location sl ON ri.location_id = sl.id
      LEFT JOIN order_item oi ON ri.line_item_id = oi.item_id
      LEFT JOIN "order" o ON oi.order_id = o.id
      WHERE ii.deleted_at IS NULL AND sl.deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (ii.sku LIKE ? OR ii.title LIKE ? OR o.display_id LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (locationId) {
      query += ` AND ri.location_id = ?`;
      params.push(locationId);
    }

    query += ` ORDER BY ri.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const reservations = await c.env.DB.prepare(query).bind(...params).all();

    // Format results
    const formattedReservations = reservations.results.map(res => ({
      id: res.id,
      line_item_id: res.line_item_id,
      inventory_item_id: res.inventory_item_id,
      location_id: res.location_id,
      quantity: res.quantity,
      description: res.description,
      created_by: res.created_by,
      metadata: res.metadata ? JSON.parse(String(res.metadata)) : null,
      created_at: res.created_at,
      updated_at: res.updated_at,
      item: {
        id: res.inventory_item_id,
        sku: res.sku,
        title: res.title,
        thumbnail: res.thumbnail,
      },
      location: {
        id: res.location_id,
        name: res.location_name,
      },
      line_item: res.line_item_id ? {
        id: res.line_item_id,
        order_id: res.order_id,
        order_display_id: res.order_display_id,
        quantity: res.line_item_quantity,
      } : null,
    }));

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM reservation_item ri
      JOIN inventory_item ii ON ri.inventory_item_id = ii.id
      JOIN stock_location sl ON ri.location_id = sl.id
      LEFT JOIN order_item oi ON ri.line_item_id = oi.item_id
      LEFT JOIN "order" o ON oi.order_id = o.id
      WHERE ii.deleted_at IS NULL AND sl.deleted_at IS NULL
    `;
    let countParams = [];
    
    if (search) {
      countQuery += ` AND (ii.sku LIKE ? OR ii.title LIKE ? OR o.display_id LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (locationId) {
      countQuery += ` AND ri.location_id = ?`;
      countParams.push(locationId);
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      reservations: formattedReservations,
      total: totalResult?.total || 0,
      page,
      limit,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch inventory reservations');
  }
});

// Cancel inventory reservation
app.delete('/api/inventory/reservations/:id', async (c) => {
  try {
    const id = c.req.param('id');
    
    // Get reservation details for updating inventory level
    const reservation = await c.env.DB.prepare(`
      SELECT inventory_item_id, location_id, quantity 
      FROM reservation_item 
      WHERE id = ?
    `).bind(id).first();

    if (!reservation) {
      return c.json({ error: 'Reservation not found' }, 404);
    }

    // Delete reservation
    await c.env.DB.prepare(`
      DELETE FROM reservation_item WHERE id = ?
    `).bind(id).run();

    // Update inventory level (reduce reserved quantity)
    await c.env.DB.prepare(`
      UPDATE inventory_level 
      SET reserved_quantity = reserved_quantity - ?, updated_at = CURRENT_TIMESTAMP
      WHERE inventory_item_id = ? AND location_id = ?
    `).bind(reservation.quantity, reservation.inventory_item_id, reservation.location_id).run();

    return c.json({ message: 'Reservation cancelled successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to cancel reservation');
  }
});

// ===========================================
// ANALYTICS AND DASHBOARD ROUTES
// ===========================================

// Get dashboard analytics
app.get('/api/analytics/dashboard', async (c) => {
  try {
    // Get basic counts and totals
    const [
      orderStats,
      customerStats,
      productStats,
      revenueStats
    ] = await Promise.all([
      // Order statistics
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as total_orders,
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
          COUNT(CASE WHEN created_at >= date('now', '-30 days') THEN 1 END) as orders_last_30_days
        FROM orders 
        WHERE deleted_at IS NULL
      `).first(),
      
      // Customer statistics
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as total_customers,
          COUNT(CASE WHEN created_at >= date('now', '-30 days') THEN 1 END) as new_customers_last_30_days
        FROM customers 
        WHERE deleted_at IS NULL
      `).first(),
      
      // Product statistics
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as total_products,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_products,
          COUNT(CASE WHEN inventory_quantity <= 10 THEN 1 END) as low_stock_products
        FROM products p
        LEFT JOIN product_variants pv ON p.id = pv.product_id
        WHERE p.deleted_at IS NULL
      `).first(),
      
      // Revenue statistics
      c.env.DB.prepare(`
        SELECT 
          COALESCE(SUM(total_amount), 0) as total_revenue,
          COALESCE(SUM(CASE WHEN created_at >= date('now', '-30 days') THEN total_amount ELSE 0 END), 0) as revenue_last_30_days,
          COALESCE(SUM(CASE WHEN created_at >= date('now', '-7 days') THEN total_amount ELSE 0 END), 0) as revenue_last_7_days
        FROM orders 
        WHERE deleted_at IS NULL AND status NOT IN ('cancelled', 'refunded')
      `).first()
    ]);

    // Get recent orders
    const recentOrders = await c.env.DB.prepare(`
      SELECT 
        o.id, o.number, o.total_amount, o.currency_code, o.status, o.created_at,
        c.first_name, c.last_name, c.email
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      ORDER BY o.created_at DESC
      LIMIT 10
    `).all();

    return c.json({
      dashboard: {
        orders: {
          total: orderStats?.total_orders || 0,
          pending: orderStats?.pending_orders || 0,
          last_30_days: orderStats?.orders_last_30_days || 0,
        },
        customers: {
          total: customerStats?.total_customers || 0,
          new_last_30_days: customerStats?.new_customers_last_30_days || 0,
        },
        products: {
          total: productStats?.total_products || 0,
          active: productStats?.active_products || 0,
          low_stock: productStats?.low_stock_products || 0,
        },
        revenue: {
          total: revenueStats?.total_revenue || 0,
          last_30_days: revenueStats?.revenue_last_30_days || 0,
          last_7_days: revenueStats?.revenue_last_7_days || 0,
        },
        recent_orders: recentOrders.results,
      },
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch dashboard data');
  }
});

// ===========================================
// PROMOTIONS ROUTES
// ===========================================

// Get all promotions
app.get('/api/promotions', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const type = c.req.query('type') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        p.id, p.code, p.campaign_id, p.is_automatic, p.type, p.status,
        p.created_at, p.updated_at,
        pc.name as campaign_name, pc.description as campaign_description,
        pc.starts_at as campaign_starts_at, pc.ends_at as campaign_ends_at,
        pam.type as application_type, pam.value as discount_value,
        pam.currency_code, pam.target_type, pam.allocation,
        COUNT(DISTINCT pr.id) as rules_count
      FROM promotion p
      LEFT JOIN promotion_campaign pc ON p.campaign_id = pc.id
      LEFT JOIN promotion_application_method pam ON p.id = pam.promotion_id
      LEFT JOIN promotion_promotion_rule ppr ON p.id = ppr.promotion_id
      LEFT JOIN promotion_rule pr ON ppr.promotion_rule_id = pr.id
      WHERE p.deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (p.code LIKE ? OR pc.name LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (status) {
      query += ` AND p.status = ?`;
      params.push(status);
    }
    
    if (type) {
      query += ` AND p.type = ?`;
      params.push(type);
    }

    query += ` GROUP BY p.id ORDER BY p.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const promotions = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `SELECT COUNT(DISTINCT p.id) as total FROM promotion p`;
    let countParams: any[] = [];
    
    if (search || status || type) {
      countQuery += ` LEFT JOIN promotion_campaign pc ON p.campaign_id = pc.id WHERE p.deleted_at IS NULL`;
      
      if (search) {
        countQuery += ` AND (p.code LIKE ? OR pc.name LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`);
      }
      
      if (status) {
        countQuery += ` AND p.status = ?`;
        countParams.push(status);
      }
      
      if (type) {
        countQuery += ` AND p.type = ?`;
        countParams.push(type);
      }
    } else {
      countQuery += ` WHERE p.deleted_at IS NULL`;
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      data: promotions.results,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        pages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch promotions');
  }
});

// Get single promotion with details
app.get('/api/promotions/:id', async (c) => {
  try {
    const id = c.req.param('id');

    const promotion = await c.env.DB.prepare(`
      SELECT 
        p.*,
        pc.name as campaign_name, pc.description as campaign_description,
        pc.starts_at as campaign_starts_at, pc.ends_at as campaign_ends_at,
        pam.type as application_type, pam.value as discount_value,
        pam.currency_code, pam.target_type, pam.allocation, pam.max_quantity,
        pam.apply_to_quantity, pam.buy_rules_min_quantity
      FROM promotion p
      LEFT JOIN promotion_campaign pc ON p.campaign_id = pc.id
      LEFT JOIN promotion_application_method pam ON p.id = pam.promotion_id
      WHERE p.id = ? AND p.deleted_at IS NULL
    `).bind(id).first();

    if (!promotion) {
      return c.json({ error: 'Promotion not found' }, 404);
    }

    // Get promotion rules
    const rules = await c.env.DB.prepare(`
      SELECT pr.*, prv.value
      FROM promotion_rule pr
      JOIN promotion_promotion_rule ppr ON pr.id = ppr.promotion_rule_id
      LEFT JOIN promotion_rule_value prv ON pr.id = prv.promotion_rule_id
      WHERE ppr.promotion_id = ? AND pr.deleted_at IS NULL
      ORDER BY pr.created_at
    `).bind(id).all();

    return c.json({
      promotion: {
        ...promotion,
        rules: rules.results
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch promotion');
  }
});

// Create promotion
app.post('/api/promotions', async (c) => {
  try {
    const data = await c.req.json();
    const promotionId = generateId('promo');

    // Create promotion
    await c.env.DB.prepare(`
      INSERT INTO promotion (
        id, code, campaign_id, is_automatic, type, status,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      promotionId,
      data.code,
      data.campaign_id || null,
      data.is_automatic ? 1 : 0,
      data.type,
      data.status || 'inactive'
    ).run();

    // Create application method
    if (data.application_method) {
      const applicationMethodId = generateId('pam');
      await c.env.DB.prepare(`
        INSERT INTO promotion_application_method (
          id, promotion_id, type, target_type, value, currency_code,
          allocation, max_quantity, apply_to_quantity, buy_rules_min_quantity,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        applicationMethodId, promotionId,
        data.application_method.type,
        data.application_method.target_type,
        data.application_method.value || null,
        data.application_method.currency_code || null,
        data.application_method.allocation || null,
        data.application_method.max_quantity || null,
        data.application_method.apply_to_quantity || null,
        data.application_method.buy_rules_min_quantity || null
      ).run();
    }

    // Create promotion rules
    if (data.rules && Array.isArray(data.rules)) {
      for (const rule of data.rules) {
        const ruleId = generateId('prule');
        
        await c.env.DB.prepare(`
          INSERT INTO promotion_rule (
            id, attribute, operator, description,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `).bind(ruleId, rule.attribute, rule.operator, rule.description || null).run();

        // Link rule to promotion
        await c.env.DB.prepare(`
          INSERT INTO promotion_promotion_rule (promotion_id, promotion_rule_id)
          VALUES (?, ?)
        `).bind(promotionId, ruleId).run();

        // Add rule values
        if (rule.values && Array.isArray(rule.values)) {
          for (const value of rule.values) {
            const valueId = generateId('prval');
            await c.env.DB.prepare(`
              INSERT INTO promotion_rule_value (
                id, promotion_rule_id, value,
                created_at, updated_at
              ) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `).bind(valueId, ruleId, value).run();
          }
        }
      }
    }

    return c.json({ id: promotionId, message: 'Promotion created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create promotion');
  }
});

// Update promotion
app.put('/api/promotions/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const data = await c.req.json();

    // Update promotion
    await c.env.DB.prepare(`
      UPDATE promotion SET
        code = ?, campaign_id = ?, is_automatic = ?, type = ?, status = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      data.code,
      data.campaign_id || null,
      data.is_automatic ? 1 : 0,
      data.type,
      data.status,
      id
    ).run();

    // Update application method if provided
    if (data.application_method) {
      const existingMethod = await c.env.DB.prepare(`
        SELECT id FROM promotion_application_method WHERE promotion_id = ?
      `).bind(id).first();

      if (existingMethod) {
        await c.env.DB.prepare(`
          UPDATE promotion_application_method SET
            type = ?, target_type = ?, value = ?, currency_code = ?,
            allocation = ?, max_quantity = ?, apply_to_quantity = ?,
            buy_rules_min_quantity = ?, updated_at = CURRENT_TIMESTAMP
          WHERE promotion_id = ?
        `).bind(
          data.application_method.type,
          data.application_method.target_type,
          data.application_method.value || null,
          data.application_method.currency_code || null,
          data.application_method.allocation || null,
          data.application_method.max_quantity || null,
          data.application_method.apply_to_quantity || null,
          data.application_method.buy_rules_min_quantity || null,
          id
        ).run();
      }
    }

    return c.json({ message: 'Promotion updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update promotion');
  }
});

// Delete promotion
app.delete('/api/promotions/:id', async (c) => {
  try {
    const id = c.req.param('id');

    await c.env.DB.prepare(`
      UPDATE promotion 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'Promotion deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete promotion');
  }
});

// Bulk delete promotions
app.post('/api/promotions/bulk-delete', async (c) => {
  try {
    const { promotionIds } = await c.req.json();

    if (!promotionIds || !Array.isArray(promotionIds) || promotionIds.length === 0) {
      return c.json({ error: 'Promotion IDs are required' }, 400);
    }

    const placeholders = promotionIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE promotion 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...promotionIds).run();

    return c.json({ message: `${promotionIds.length} promotions deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete promotions');
  }
});

// ===========================================
// JOURNAL ROUTES
// ===========================================

// Get all journal entries
app.get('/api/journal', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const published = c.req.query('published');
    const author = c.req.query('author') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        id, title, content, excerpt, slug, author, image_url,
        published, published_at, tags, metadata,
        created_at, updated_at
      FROM journal_entry
      WHERE deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (published === 'true') {
      query += ` AND published = 1`;
    } else if (published === 'false') {
      query += ` AND published = 0`;
    }
    
    if (author) {
      query += ` AND author LIKE ?`;
      params.push(`%${author}%`);
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const entries = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM journal_entry WHERE deleted_at IS NULL`;
    let countParams = [];
    
    if (search || published || author) {
      if (search) {
        countQuery += ` AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }
      
      if (published === 'true') {
        countQuery += ` AND published = 1`;
      } else if (published === 'false') {
        countQuery += ` AND published = 0`;
      }
      
      if (author) {
        countQuery += ` AND author LIKE ?`;
        countParams.push(`%${author}%`);
      }
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    // Parse JSON fields and add calculated fields
    const formattedEntries = entries.results.map(entry => ({
      ...entry,
      metadata: entry.metadata ? JSON.parse(String(entry.metadata)) : null,
      tags: entry.tags ? String(entry.tags).split(',').map((tag: string) => tag.trim()) : [],
      word_count: entry.content ? String(entry.content).split(/\s+/).length : 0,
      reading_time: entry.content ? Math.ceil(String(entry.content).split(/\s+/).length / 200) : 0,
    }));

    return c.json({
      data: formattedEntries,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        pages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch journal entries');
  }
});

// Get single journal entry
app.get('/api/journal/:id', async (c) => {
  try {
    const id = c.req.param('id');

    const entry = await c.env.DB.prepare(`
      SELECT *
      FROM journal_entry
      WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first();

    if (!entry) {
      return c.json({ error: 'Journal entry not found' }, 404);
    }

    return c.json({
      entry: {
        ...entry,
        metadata: entry.metadata ? JSON.parse(String(entry.metadata)) : null,
        tags: entry.tags ? String(entry.tags).split(',').map((tag: string) => tag.trim()) : [],
        word_count: entry.content ? String(entry.content).split(/\s+/).length : 0,
        reading_time: entry.content ? Math.ceil(String(entry.content).split(/\s+/).length / 200) : 0,
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch journal entry');
  }
});

// Create journal entry
app.post('/api/journal', async (c) => {
  try {
    const data = await c.req.json();
    const entryId = generateId('journal');

    // Generate slug if not provided
    let slug = data.slug;
    if (!slug) {
      slug = data.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
    }

    // Ensure slug is unique
    const existingSlug = await c.env.DB.prepare(`
      SELECT id FROM journal_entry WHERE slug = ? AND deleted_at IS NULL
    `).bind(slug).first();

    if (existingSlug) {
      slug = `${slug}-${Date.now()}`;
    }

    await c.env.DB.prepare(`
      INSERT INTO journal_entry (
        id, title, content, excerpt, slug, author, image_url,
        published, published_at, tags, metadata,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      entryId,
      data.title,
      data.content || '',
      data.excerpt || null,
      slug,
      data.author || null,
      data.image_url || null,
      data.published ? 1 : 0,
      data.published && data.published_at ? data.published_at : (data.published ? new Date().toISOString() : null),
      Array.isArray(data.tags) ? data.tags.join(', ') : (data.tags || null),
      data.metadata ? JSON.stringify(data.metadata) : null
    ).run();

    return c.json({ id: entryId, slug, message: 'Journal entry created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create journal entry');
  }
});

// Update journal entry
app.put('/api/journal/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const data = await c.req.json();

    // Update slug if title changed
    let slug = data.slug;
    if (!slug && data.title) {
      slug = data.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
      
      // Check if slug already exists (excluding current entry)
      const existingSlug = await c.env.DB.prepare(`
        SELECT id FROM journal_entry WHERE slug = ? AND id != ? AND deleted_at IS NULL
      `).bind(slug, id).first();

      if (existingSlug) {
        slug = `${slug}-${Date.now()}`;
      }
    }

    await c.env.DB.prepare(`
      UPDATE journal_entry SET
        title = ?, content = ?, excerpt = ?, slug = ?, author = ?, image_url = ?,
        published = ?, published_at = ?, tags = ?, metadata = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      data.title,
      data.content || '',
      data.excerpt || null,
      slug,
      data.author || null,
      data.image_url || null,
      data.published ? 1 : 0,
      data.published && data.published_at ? data.published_at : (data.published ? new Date().toISOString() : null),
      Array.isArray(data.tags) ? data.tags.join(', ') : (data.tags || null),
      data.metadata ? JSON.stringify(data.metadata) : null,
      id
    ).run();

    return c.json({ message: 'Journal entry updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update journal entry');
  }
});

// Delete journal entry
app.delete('/api/journal/:id', async (c) => {
  try {
    const id = c.req.param('id');

    await c.env.DB.prepare(`
      UPDATE journal_entry 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'Journal entry deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete journal entry');
  }
});

// Publish/unpublish journal entry
app.post('/api/journal/:id/publish', async (c) => {
  try {
    const id = c.req.param('id');

    const entry = await c.env.DB.prepare(`
      SELECT published FROM journal_entry WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first();

    if (!entry) {
      return c.json({ error: 'Journal entry not found' }, 404);
    }

    const newPublishedState = !entry.published;
    const publishedAt = newPublishedState ? new Date().toISOString() : null;

    await c.env.DB.prepare(`
      UPDATE journal_entry 
      SET published = ?, published_at = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(newPublishedState ? 1 : 0, publishedAt, id).run();

    return c.json({ 
      message: `Journal entry ${newPublishedState ? 'published' : 'unpublished'} successfully`,
      published: newPublishedState
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update journal entry status');
  }
});

// Bulk delete journal entries
app.post('/api/journal/bulk-delete', async (c) => {
  try {
    const { entryIds } = await c.req.json();

    if (!entryIds || !Array.isArray(entryIds) || entryIds.length === 0) {
      return c.json({ error: 'Entry IDs are required' }, 400);
    }

    const placeholders = entryIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE journal_entry 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...entryIds).run();

    return c.json({ message: `${entryIds.length} journal entries deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete journal entries');
  }
});

// Get journal analytics
app.get('/api/journal/analytics', async (c) => {
  try {
    const [totalEntries, publishedEntries, draftEntries, monthlyStats] = await Promise.all([
      c.env.DB.prepare(`
        SELECT COUNT(*) as total FROM journal_entry WHERE deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COUNT(*) as published FROM journal_entry 
        WHERE published = 1 AND deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COUNT(*) as drafts FROM journal_entry 
        WHERE published = 0 AND deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as entries,
          strftime('%Y-%m', created_at) as month
        FROM journal_entry 
        WHERE deleted_at IS NULL 
        GROUP BY strftime('%Y-%m', created_at)
        ORDER BY month DESC
        LIMIT 12
      `).all()
    ]);

    return c.json({
      analytics: {
        total_entries: totalEntries?.total || 0,
        published_entries: publishedEntries?.published || 0,
        draft_entries: draftEntries?.drafts || 0,
        monthly_stats: monthlyStats.results
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch journal analytics');
  }
});

// ===========================================
// GIFT CARDS ROUTES
// ===========================================

// Get all gift cards (products with is_giftcard = true)
app.get('/api/gift-cards', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        p.id, p.handle, p.status, p.thumbnail, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description,
        COUNT(pv.id) as variant_count,
        MIN(pr.amount) as min_price,
        MAX(pr.amount) as max_price
      FROM product p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      LEFT JOIN product_variant pv ON p.id = pv.product_id AND pv.deleted_at IS NULL
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      LEFT JOIN price pr ON pvps.price_set_id = pr.price_set_id
      WHERE p.deleted_at IS NULL AND p.is_giftcard = 1
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (pt.title LIKE ? OR p.handle LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (status) {
      query += ` AND p.status = ?`;
      params.push(status);
    }

    query += ` GROUP BY p.id ORDER BY p.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const giftCards = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM product p WHERE p.deleted_at IS NULL AND p.is_giftcard = 1`;
    let countParams: any[] = [];
    
    if (search || status) {
      countQuery += ` LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'`;
      
      if (search) {
        countQuery += ` AND (pt.title LIKE ? OR p.handle LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`);
      }
      
      if (status) {
        countQuery += ` AND p.status = ?`;
        countParams.push(status);
      }
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      gift_cards: giftCards.results,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch gift cards');
  }
});

// Get single gift card
app.get('/api/gift-cards/:id', async (c) => {
  try {
    const id = c.req.param('id');

    const giftCard = await c.env.DB.prepare(`
      SELECT 
        p.id, p.handle, p.status, p.thumbnail, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description
      FROM product p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      WHERE p.id = ? AND p.deleted_at IS NULL AND p.is_giftcard = 1
    `).bind(id).first();

    if (!giftCard) {
      return c.json({ error: 'Gift card not found' }, 404);
    }

    // Get variants with prices
    const variants = await c.env.DB.prepare(`
      SELECT 
        pv.id, pv.title, pv.sku, pv.metadata,
        pr.amount, pr.currency_code
      FROM product_variant pv
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      LEFT JOIN price pr ON pvps.price_set_id = pr.price_set_id
      WHERE pv.product_id = ? AND pv.deleted_at IS NULL
      ORDER BY pr.amount
    `).bind(id).all();

    return c.json({
      gift_card: {
        ...giftCard,
        variants: variants.results,
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch gift card');
  }
});

// Create gift card
app.post('/api/gift-cards', async (c) => {
  try {
    const data = await c.req.json();
    const giftCardId = generateId('gcard');

    // Create gift card product
    await c.env.DB.prepare(`
      INSERT INTO product (
        id, handle, status, thumbnail, is_giftcard, discountable,
        metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, 1, 0, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      giftCardId,
      data.handle,
      data.status || 'draft',
      data.thumbnail || null,
      JSON.stringify(data.metadata || {})
    ).run();

    // Create product translation
    if (data.title) {
      await c.env.DB.prepare(`
        INSERT INTO product_translations (
          product_id, language_code, title, subtitle, description
        ) VALUES (?, 'en', ?, ?, ?)
      `).bind(
        giftCardId,
        data.title,
        data.subtitle || null,
        data.description || null
      ).run();
    }

    return c.json({ id: giftCardId, message: 'Gift card created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create gift card');
  }
});

// Update gift card
app.put('/api/gift-cards/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const data = await c.req.json();

    // Update gift card
    await c.env.DB.prepare(`
      UPDATE product SET
        handle = ?, status = ?, thumbnail = ?, metadata = ?, 
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_giftcard = 1
    `).bind(
      data.handle,
      data.status,
      data.thumbnail || null,
      JSON.stringify(data.metadata || {}),
      id
    ).run();

    // Update translation
    const existingTranslation = await c.env.DB.prepare(`
      SELECT product_id FROM product_translations 
      WHERE product_id = ? AND language_code = 'en'
    `).bind(id).first();

    if (existingTranslation) {
      await c.env.DB.prepare(`
        UPDATE product_translations SET
          title = ?, subtitle = ?, description = ?
        WHERE product_id = ? AND language_code = 'en'
      `).bind(
        data.title,
        data.subtitle || null,
        data.description || null,
        id
      ).run();
    } else if (data.title) {
      await c.env.DB.prepare(`
        INSERT INTO product_translations (
          product_id, language_code, title, subtitle, description
        ) VALUES (?, 'en', ?, ?, ?)
      `).bind(
        id,
        data.title,
        data.subtitle || null,
        data.description || null
      ).run();
    }

    return c.json({ message: 'Gift card updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update gift card');
  }
});

// Delete gift card
app.delete('/api/gift-cards/:id', async (c) => {
  try {
    const id = c.req.param('id');

    await c.env.DB.prepare(`
      UPDATE product 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND is_giftcard = 1
    `).bind(id).run();

    return c.json({ message: 'Gift card deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete gift card');
  }
});

// Bulk delete gift cards
app.post('/api/gift-cards/bulk-delete', async (c) => {
  try {
    const { giftCardIds } = await c.req.json();

    if (!giftCardIds || !Array.isArray(giftCardIds) || giftCardIds.length === 0) {
      return c.json({ error: 'Gift card IDs are required' }, 400);
    }

    const placeholders = giftCardIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE product 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders}) AND is_giftcard = 1
    `).bind(...giftCardIds).run();

    return c.json({ message: `${giftCardIds.length} gift cards deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete gift cards');
  }
});

// ===========================================
// CAMPAIGNS ROUTES
// ===========================================

// Get all promotion campaigns
app.get('/api/campaigns', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        pc.id, pc.name, pc.description, pc.campaign_identifier,
        pc.starts_at, pc.ends_at, pc.created_at, pc.updated_at,
        COUNT(DISTINCT p.id) as promotion_count,
        CASE 
          WHEN pc.ends_at < datetime('now') THEN 'expired'
          WHEN pc.starts_at > datetime('now') THEN 'scheduled'
          WHEN pc.starts_at <= datetime('now') AND (pc.ends_at IS NULL OR pc.ends_at >= datetime('now')) THEN 'active'
          ELSE 'inactive'
        END as status
      FROM promotion_campaign pc
      LEFT JOIN promotion p ON pc.id = p.campaign_id AND p.deleted_at IS NULL
      WHERE pc.deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (pc.name LIKE ? OR pc.description LIKE ? OR pc.campaign_identifier LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    query += ` GROUP BY pc.id`;
    
    if (status) {
      query += ` HAVING status = ?`;
      params.push(status);
    }

    query += ` ORDER BY pc.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const campaigns = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `
      SELECT COUNT(DISTINCT pc.id) as total 
      FROM promotion_campaign pc
      WHERE pc.deleted_at IS NULL
    `;
    let countParams: any[] = [];
    
    if (search) {
      countQuery += ` AND (pc.name LIKE ? OR pc.description LIKE ? OR pc.campaign_identifier LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      campaigns: campaigns.results,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch campaigns');
  }
});

// Get single campaign with promotions
app.get('/api/campaigns/:id', async (c) => {
  try {
    const id = c.req.param('id');

    const campaign = await c.env.DB.prepare(`
      SELECT *
      FROM promotion_campaign
      WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first();

    if (!campaign) {
      return c.json({ error: 'Campaign not found' }, 404);
    }

    // Get associated promotions
    const promotions = await c.env.DB.prepare(`
      SELECT 
        p.id, p.code, p.type, p.status, p.is_automatic,
        pam.type as application_type, pam.value as discount_value,
        pam.currency_code, pam.target_type
      FROM promotion p
      LEFT JOIN promotion_application_method pam ON p.id = pam.promotion_id
      WHERE p.campaign_id = ? AND p.deleted_at IS NULL
      ORDER BY p.created_at DESC
    `).bind(id).all();

    return c.json({
      campaign: {
        ...campaign,
        promotions: promotions.results
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch campaign');
  }
});

// Create campaign
app.post('/api/campaigns', async (c) => {
  try {
    const data = await c.req.json();
    const campaignId = generateId('camp');

    await c.env.DB.prepare(`
      INSERT INTO promotion_campaign (
        id, name, description, campaign_identifier, starts_at, ends_at,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      campaignId,
      data.name,
      data.description || null,
      data.campaign_identifier,
      data.starts_at || null,
      data.ends_at || null
    ).run();

    return c.json({ id: campaignId, message: 'Campaign created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create campaign');
  }
});

// Update campaign
app.put('/api/campaigns/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const data = await c.req.json();

    await c.env.DB.prepare(`
      UPDATE promotion_campaign SET
        name = ?, description = ?, campaign_identifier = ?,
        starts_at = ?, ends_at = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      data.name,
      data.description || null,
      data.campaign_identifier,
      data.starts_at || null,
      data.ends_at || null,
      id
    ).run();

    return c.json({ message: 'Campaign updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update campaign');
  }
});

// Delete campaign
app.delete('/api/campaigns/:id', async (c) => {
  try {
    const id = c.req.param('id');

    await c.env.DB.prepare(`
      UPDATE promotion_campaign 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'Campaign deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete campaign');
  }
});

// Bulk delete campaigns
app.post('/api/campaigns/bulk-delete', async (c) => {
  try {
    const { campaignIds } = await c.req.json();

    if (!campaignIds || !Array.isArray(campaignIds) || campaignIds.length === 0) {
      return c.json({ error: 'Campaign IDs are required' }, 400);
    }

    const placeholders = campaignIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE promotion_campaign 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...campaignIds).run();

    return c.json({ message: `${campaignIds.length} campaigns deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete campaigns');
  }
});

// ===========================================
// FILE MANAGEMENT ROUTES
// ===========================================

// Get all folders
app.get('/api/files/folders', async (c) => {
  try {
    const search = c.req.query('search') || '';
    const parentId = c.req.query('parent_id');

    let query = `
      SELECT 
        id, name, parent_id, path, metadata, created_at, updated_at,
        (SELECT COUNT(*) FROM file_folder cf WHERE cf.parent_id = f.id) as subfolder_count,
        (SELECT COUNT(*) FROM file_upload fu WHERE fu.folder_id = f.id) as file_count
      FROM file_folder f
      WHERE deleted_at IS NULL
    `;

    const params: any[] = [];

    if (search) {
      query += ` AND name LIKE ?`;
      params.push(`%${search}%`);
    }

    if (parentId) {
      query += ` AND parent_id = ?`;
      params.push(parentId);
    } else {
      query += ` AND parent_id IS NULL`;
    }

    query += ` ORDER BY name`;

    const folders = await c.env.DB.prepare(query).bind(...params).all();

    return c.json({
      folders: folders.results.map((folder: any) => ({
        ...folder,
        metadata: folder.metadata ? JSON.parse(String(folder.metadata)) : null,
      }))
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch folders');
  }
});

// Create folder
app.post('/api/files/folders', async (c) => {
  try {
    const { name, parent_id, description } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'Folder name is required' }, 400);
    }

    const folderId = generateId('folder');
    
    // Build path
    let path = name;
    if (parent_id) {
      const parent = await c.env.DB.prepare(`
        SELECT path FROM file_folder WHERE id = ? AND deleted_at IS NULL
      `).bind(parent_id).first();
      
      if (parent) {
        path = `${parent.path}/${name}`;
      }
    }

    await c.env.DB.prepare(`
      INSERT INTO file_folder (
        id, name, parent_id, path, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      folderId,
      name.trim(),
      parent_id || null,
      path,
      JSON.stringify({ description: description || null })
    ).run();

    return c.json({ id: folderId, message: 'Folder created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create folder');
  }
});

// Update folder
app.put('/api/files/folders/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const { name, description } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'Folder name is required' }, 400);
    }

    // Get current folder info
    const folder = await c.env.DB.prepare(`
      SELECT parent_id, path FROM file_folder WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first();

    if (!folder) {
      return c.json({ error: 'Folder not found' }, 404);
    }

    // Build new path
    let newPath = name.trim();
    if (folder.parent_id) {
      const parent = await c.env.DB.prepare(`
        SELECT path FROM file_folder WHERE id = ? AND deleted_at IS NULL
      `).bind(folder.parent_id).first();
      
      if (parent) {
        newPath = `${parent.path}/${name.trim()}`;
      }
    }

    await c.env.DB.prepare(`
      UPDATE file_folder 
      SET name = ?, path = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      name.trim(),
      newPath,
      JSON.stringify({ description: description || null }),
      id
    ).run();

    return c.json({ message: 'Folder updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update folder');
  }
});

// Delete folder
app.delete('/api/files/folders/:id', async (c) => {
  try {
    const id = c.req.param('id');

    // Check if folder has files or subfolders
    const [files, subfolders] = await Promise.all([
      c.env.DB.prepare(`SELECT COUNT(*) as count FROM file_upload WHERE folder_id = ?`).bind(id).first(),
      c.env.DB.prepare(`SELECT COUNT(*) as count FROM file_folder WHERE parent_id = ? AND deleted_at IS NULL`).bind(id).first()
    ]);

    if ((Number(files?.count) || 0) > 0 || (Number(subfolders?.count) || 0) > 0) {
      return c.json({ error: 'Cannot delete folder that contains files or subfolders' }, 400);
    }

    await c.env.DB.prepare(`
      UPDATE file_folder 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'Folder deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete folder');
  }
});

// Get all files
app.get('/api/files', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const folderId = c.req.query('folder_id');
    const mimeType = c.req.query('mime_type') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        fu.id, fu.name, fu.original_name, fu.mime_type, fu.size,
        fu.url, fu.thumbnail_url, fu.r2_key, fu.metadata,
        fu.created_at, fu.updated_at,
        ff.name as folder_name, ff.path as folder_path
      FROM file_upload fu
      LEFT JOIN file_folder ff ON fu.folder_id = ff.id
      WHERE fu.deleted_at IS NULL
    `;

    const params: any[] = [];

    if (search) {
      query += ` AND (fu.name LIKE ? OR fu.original_name LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }

    if (folderId) {
      query += ` AND fu.folder_id = ?`;
      params.push(folderId);
    } else if (folderId === '') {
      query += ` AND fu.folder_id IS NULL`;
    }

    if (mimeType) {
      if (mimeType === 'image') {
        query += ` AND fu.mime_type LIKE 'image/%'`;
      } else if (mimeType === 'video') {
        query += ` AND fu.mime_type LIKE 'video/%'`;
      } else if (mimeType === 'document') {
        query += ` AND fu.mime_type IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain')`;
      } else {
        query += ` AND fu.mime_type = ?`;
        params.push(mimeType);
      }
    }

    query += ` ORDER BY fu.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const files = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM file_upload fu WHERE fu.deleted_at IS NULL`;
    let countParams: any[] = [];

    if (search) {
      countQuery += ` AND (fu.name LIKE ? OR fu.original_name LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`);
    }

    if (folderId) {
      countQuery += ` AND fu.folder_id = ?`;
      countParams.push(folderId);
    } else if (folderId === '') {
      countQuery += ` AND fu.folder_id IS NULL`;
    }

    if (mimeType) {
      if (mimeType === 'image') {
        countQuery += ` AND fu.mime_type LIKE 'image/%'`;
      } else if (mimeType === 'video') {
        countQuery += ` AND fu.mime_type LIKE 'video/%'`;
      } else if (mimeType === 'document') {
        countQuery += ` AND fu.mime_type IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain')`;
      } else {
        countQuery += ` AND fu.mime_type = ?`;
        countParams.push(mimeType);
      }
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      files: files.results.map((file: any) => ({
        ...file,
        metadata: file.metadata ? JSON.parse(String(file.metadata)) : null,
      })),
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch files');
  }
});

// Upload file (single file)
app.post('/api/files/upload', async (c) => {
  try {
    const formData = await c.req.formData();
    const file = formData.get('file') as File;
    const folderId = formData.get('folder_id') as string | null;
    const name = formData.get('name') as string || file.name;

    if (!file) {
      return c.json({ error: 'No file provided' }, 400);
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return c.json({ error: 'File size must be less than 10MB' }, 400);
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const uniqueName = `${Date.now()}_${Math.random().toString(36).substring(2)}.${fileExtension}`;
    const r2Key = `uploads/${uniqueName}`;

    // For this example, we'll store the file info but you would typically upload to R2
    // await c.env.R2_BUCKET.put(r2Key, file.stream());

    const fileId = generateId('file');
    const url = `/files/${r2Key}`;
    let thumbnailUrl = null;

    // Generate thumbnail URL for images
    if (file.type.startsWith('image/')) {
      thumbnailUrl = `/files/thumbnails/${r2Key}`;
    }

    await c.env.DB.prepare(`
      INSERT INTO file_upload (
        id, name, original_name, mime_type, size, folder_id,
        url, thumbnail_url, r2_key, metadata,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      fileId,
      name,
      file.name,
      file.type,
      file.size,
      folderId || null,
      url,
      thumbnailUrl,
      r2Key,
      JSON.stringify({})
    ).run();

    return c.json({
      id: fileId,
      name,
      original_name: file.name,
      mime_type: file.type,
      size: file.size,
      url,
      thumbnail_url: thumbnailUrl,
      message: 'File uploaded successfully'
    }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to upload file');
  }
});

// Update file
app.put('/api/files/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const { name, folder_id } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'File name is required' }, 400);
    }

    await c.env.DB.prepare(`
      UPDATE file_upload 
      SET name = ?, folder_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(name.trim(), folder_id || null, id).run();

    return c.json({ message: 'File updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update file');
  }
});

// Delete file
app.delete('/api/files/:id', async (c) => {
  try {
    const id = c.req.param('id');

    // Get file info
    const file = await c.env.DB.prepare(`
      SELECT r2_key FROM file_upload WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first();

    if (!file) {
      return c.json({ error: 'File not found' }, 404);
    }

    // Delete from R2 (in a real implementation)
    // await c.env.R2_BUCKET.delete(file.r2_key);

    // Soft delete from database
    await c.env.DB.prepare(`
      UPDATE file_upload 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'File deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete file');
  }
});

// Bulk delete files
app.post('/api/files/bulk-delete', async (c) => {
  try {
    const { fileIds } = await c.req.json();

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return c.json({ error: 'File IDs are required' }, 400);
    }

    // Get file info for R2 deletion
    const placeholders = fileIds.map(() => '?').join(',');
    const files = await c.env.DB.prepare(`
      SELECT r2_key FROM file_upload 
      WHERE id IN (${placeholders}) AND deleted_at IS NULL
    `).bind(...fileIds).all();

    // Delete from R2 (in a real implementation)
    // for (const file of files.results) {
    //   await c.env.R2_BUCKET.delete(file.r2_key);
    // }

    // Soft delete from database
    await c.env.DB.prepare(`
      UPDATE file_upload 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...fileIds).run();

    return c.json({ message: `${fileIds.length} files deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete files');
  }
});

// Move files to folder
app.post('/api/files/move', async (c) => {
  try {
    const { fileIds, folder_id } = await c.req.json();

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return c.json({ error: 'File IDs are required' }, 400);
    }

    const placeholders = fileIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE file_upload 
      SET folder_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id IN (${placeholders}) AND deleted_at IS NULL
    `).bind(folder_id || null, ...fileIds).run();

    return c.json({ message: `${fileIds.length} files moved successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to move files');
  }
});

// Get file analytics
app.get('/api/files/analytics', async (c) => {
  try {
    const [totalFiles, totalSize, fileTypes, recentUploads] = await Promise.all([
      c.env.DB.prepare(`
        SELECT COUNT(*) as total FROM file_upload WHERE deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COALESCE(SUM(size), 0) as total_size FROM file_upload WHERE deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT 
          CASE 
            WHEN mime_type LIKE 'image/%' THEN 'Images'
            WHEN mime_type LIKE 'video/%' THEN 'Videos'
            WHEN mime_type LIKE 'audio/%' THEN 'Audio'
            WHEN mime_type IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain') THEN 'Documents'
            ELSE 'Other'
          END as type,
          COUNT(*) as count,
          COALESCE(SUM(size), 0) as total_size
        FROM file_upload 
        WHERE deleted_at IS NULL
        GROUP BY type
        ORDER BY count DESC
      `).all(),
      
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as uploads,
          DATE(created_at) as date
        FROM file_upload 
        WHERE deleted_at IS NULL AND created_at >= date('now', '-30 days')
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
      `).all()
    ]);

    return c.json({
      analytics: {
        total_files: totalFiles?.total || 0,
        total_size: totalSize?.total_size || 0,
        file_types: fileTypes?.results || [],
        recent_uploads: recentUploads?.results || []
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch file analytics');
  }
});

// ===========================================
// ADDITIONAL API ENDPOINTS
// ===========================================

// Get sales channels
app.get('/api/sales-channels', async (c) => {
  try {
    const channels = await c.env.DB.prepare(`
      SELECT * FROM sales_channel ORDER BY name
    `).all();
    
    return c.json({ data: channels.results || [] });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch sales channels');
  }
});

// Create sales channel
app.post('/api/sales-channels', async (c) => {
  try {
    const { name, description, is_disabled } = await c.req.json();
    
    if (!name) {
      return c.json({ error: 'Name is required' }, 400);
    }
    
    const channelId = generateId('sc');
    
    await c.env.DB.prepare(`
      INSERT INTO sales_channel (
        id, name, description, is_disabled, created_at, updated_at
      ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      channelId,
      name,
      description || null,
      is_disabled ? 1 : 0
    ).run();
    
    return c.json({ 
      id: channelId, 
      message: 'Sales channel created successfully' 
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create sales channel');
  }
});

// Get stock locations
app.get('/api/stock-locations', async (c) => {
  try {
    const locations = await c.env.DB.prepare(`
      SELECT sl.*, sla.address_1, sla.city, sla.country_code
      FROM stock_location sl
      LEFT JOIN stock_location_address sla ON sl.address_id = sla.id
      ORDER BY sl.name
    `).all();
    
    return c.json({ data: locations.results || [] });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch stock locations');
  }
});

// Create stock location
app.post('/api/stock-locations', async (c) => {
  try {
    const { name, address } = await c.req.json();
    
    if (!name) {
      return c.json({ error: 'Name is required' }, 400);
    }
    
    const locationId = generateId('sloc');
    let addressId = null;
    
    // Create address if provided
    if (address) {
      addressId = generateId('addr');
      await c.env.DB.prepare(`
        INSERT INTO stock_location_address (
          id, address_1, address_2, company, city, country_code, 
          phone, province, postal_code, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        addressId,
        address.address_1 || null,
        address.address_2 || null,
        address.company || null,
        address.city || null,
        address.country_code || 'US',
        address.phone || null,
        address.province || null,
        address.postal_code || null
      ).run();
    }
    
    await c.env.DB.prepare(`
      INSERT INTO stock_location (
        id, name, address_id, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      locationId,
      name,
      addressId,
      JSON.stringify({})
    ).run();
    
    return c.json({ 
      id: locationId, 
      message: 'Stock location created successfully' 
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create stock location');
  }
});

// Get store settings
app.get('/api/settings/store', async (c) => {
  try {
    const store = await c.env.DB.prepare(`
      SELECT * FROM store LIMIT 1
    `).first();
    
    return c.json({ data: store || {} });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch store settings');
  }
});

// Update store settings
app.put('/api/settings/store', async (c) => {
  try {
    const storeData = await c.req.json();
    
    // Check if store exists
    const existingStore = await c.env.DB.prepare(`
      SELECT id FROM store LIMIT 1
    `).first();
    
    if (existingStore) {
      // Update existing store
      await c.env.DB.prepare(`
        UPDATE store SET 
          name = ?, 
          default_sales_channel_id = ?, 
          default_region_id = ?, 
          default_location_id = ?, 
          metadata = ?, 
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(
        storeData.name || null,
        storeData.default_sales_channel_id || null,
        storeData.default_region_id || null,
        storeData.default_location_id || null,
        JSON.stringify(storeData.metadata || {}),
        existingStore.id
      ).run();
    } else {
      // Create new store
      const storeId = generateId('store');
      await c.env.DB.prepare(`
        INSERT INTO store (
          id, name, default_sales_channel_id, default_region_id, 
          default_location_id, metadata, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        storeId,
        storeData.name || 'My Store',
        storeData.default_sales_channel_id || null,
        storeData.default_region_id || null,
        storeData.default_location_id || null,
        JSON.stringify(storeData.metadata || {})
      ).run();
    }
    
    return c.json({ message: 'Store settings updated successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to update store settings');
  }
});

// Get tax rates
app.get('/api/tax-rates', async (c) => {
  try {
    const taxRates = await c.env.DB.prepare(`
      SELECT tr.*, treg.country_code, treg.province_code
      FROM tax_rate tr
      LEFT JOIN tax_region treg ON tr.tax_region_id = treg.id
      ORDER BY tr.name
    `).all();
    
    const taxRegions = await c.env.DB.prepare(`
      SELECT * FROM tax_region ORDER BY country_code, province_code
    `).all();
    
    return c.json({ 
      data: {
        taxRates: taxRates.results || [],
        taxRegions: taxRegions.results || []
      }
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch tax rates');
  }
});

// Create tax rate
app.post('/api/tax-rates', async (c) => {
  try {
    const { name, code, rate, is_default, is_combinable, tax_region_id, metadata } = await c.req.json();
    
    if (!name || !code || rate === undefined) {
      return c.json({ error: 'Name, code, and rate are required' }, 400);
    }
    
    const taxRateId = generateId('tr');
    
    await c.env.DB.prepare(`
      INSERT INTO tax_rate (
        id, rate, code, name, is_default, is_combinable, 
        tax_region_id, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      taxRateId,
      rate,
      code,
      name,
      is_default ? 1 : 0,
      is_combinable ? 1 : 0,
      tax_region_id || null,
      JSON.stringify(metadata || {})
    ).run();
    
    return c.json({ 
      id: taxRateId, 
      message: 'Tax rate created successfully' 
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create tax rate');
  }
});

// Update tax rate
app.put('/api/tax-rates/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const { name, code, rate, is_default, is_combinable, metadata } = await c.req.json();
    
    await c.env.DB.prepare(`
      UPDATE tax_rate SET 
        rate = ?, code = ?, name = ?, is_default = ?, 
        is_combinable = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(
      rate,
      code,
      name,
      is_default ? 1 : 0,
      is_combinable ? 1 : 0,
      JSON.stringify(metadata || {}),
      id
    ).run();
    
    return c.json({ message: 'Tax rate updated successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to update tax rate');
  }
});

// Delete tax rate
app.delete('/api/tax-rates/:id', async (c) => {
  try {
    const id = c.req.param('id');
    
    await c.env.DB.prepare(`
      UPDATE tax_rate SET 
        deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();
    
    return c.json({ message: 'Tax rate deleted successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to delete tax rate');
  }
});

// ===========================================
// USER MANAGEMENT ROUTES
// ===========================================

// Get all users
app.get('/api/settings/users', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const role = c.req.query('role') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        id, email, first_name, last_name, role, is_active,
        last_login_at, created_at, updated_at
      FROM users
      WHERE deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (role) {
      query += ` AND role = ?`;
      params.push(role);
    }
    
    if (status === 'active') {
      query += ` AND is_active = 1`;
    } else if (status === 'inactive') {
      query += ` AND is_active = 0`;
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const users = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM users WHERE deleted_at IS NULL`;
    let countParams: any[] = [];
    
    if (search || role || status) {
      if (search) {
        countQuery += ` AND (email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }
      
      if (role) {
        countQuery += ` AND role = ?`;
        countParams.push(role);
      }
      
      if (status === 'active') {
        countQuery += ` AND is_active = 1`;
      } else if (status === 'inactive') {
        countQuery += ` AND is_active = 0`;
      }
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    return c.json({
      data: users.results,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil((Number(totalResult?.total) || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch users');
  }
});

// Get single user
app.get('/api/settings/users/:id', async (c) => {
  try {
    const id = c.req.param('id');

    const user = await c.env.DB.prepare(`
      SELECT 
        id, email, first_name, last_name, role, is_active,
        last_login_at, created_at, updated_at
      FROM users
      WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first();

    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    return c.json({ user });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch user');
  }
});

// Create user
app.post('/api/settings/users', async (c) => {
  try {
    const { email, first_name, last_name, role, is_active, password } = await c.req.json();

    if (!email || !first_name || !last_name) {
      return c.json({ error: 'Email, first name, and last name are required' }, 400);
    }

    // Check if email already exists
    const existingUser = await c.env.DB.prepare(`
      SELECT id FROM users WHERE email = ? AND deleted_at IS NULL
    `).bind(email).first();

    if (existingUser) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    const userId = generateId('user');

    // Note: In production, you should properly hash passwords
    const passwordHash = password || 'admin123'; // Default password

    await c.env.DB.prepare(`
      INSERT INTO users (
        id, email, first_name, last_name, role, is_active, password_hash,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      userId,
      email,
      first_name,
      last_name,
      role || 'admin',
      is_active !== false ? 1 : 0,
      passwordHash
    ).run();

    return c.json({ id: userId, message: 'User created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create user');
  }
});

// Update user
app.put('/api/settings/users/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const { email, first_name, last_name, role, is_active, password } = await c.req.json();

    if (!email || !first_name || !last_name) {
      return c.json({ error: 'Email, first name, and last name are required' }, 400);
    }

    // Check if email already exists for other users
    const existingUser = await c.env.DB.prepare(`
      SELECT id FROM users WHERE email = ? AND id != ? AND deleted_at IS NULL
    `).bind(email, id).first();

    if (existingUser) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    let updateQuery = `
      UPDATE users SET
        email = ?, first_name = ?, last_name = ?, role = ?, is_active = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `;
    
    let params = [
      email,
      first_name,
      last_name,
      role || 'admin',
      is_active !== false ? 1 : 0,
      id
    ];

    // Update password if provided
    if (password && password.trim()) {
      updateQuery = `
        UPDATE users SET
          email = ?, first_name = ?, last_name = ?, role = ?, is_active = ?,
          password_hash = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND deleted_at IS NULL
      `;
      params = [
        email,
        first_name,
        last_name,
        role || 'admin',
        is_active !== false ? 1 : 0,
        password.trim(),
        id
      ];
    }

    await c.env.DB.prepare(updateQuery).bind(...params).run();

    return c.json({ message: 'User updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update user');
  }
});

// Delete user
app.delete('/api/settings/users/:id', async (c) => {
  try {
    const id = c.req.param('id');

    // Get current user ID from JWT to prevent self-deletion
    const payload = c.get('jwtPayload');
    if (payload && payload.id === id) {
      return c.json({ error: 'Cannot delete your own account' }, 400);
    }

    await c.env.DB.prepare(`
      UPDATE users 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'User deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete user');
  }
});

// Bulk delete users
app.post('/api/settings/users/bulk-delete', async (c) => {
  try {
    const { userIds } = await c.req.json();

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return c.json({ error: 'User IDs are required' }, 400);
    }

    // Get current user ID from JWT to prevent self-deletion
    const payload = c.get('jwtPayload');
    if (payload && userIds.includes(payload.id)) {
      return c.json({ error: 'Cannot delete your own account' }, 400);
    }

    const placeholders = userIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE users 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...userIds).run();

    return c.json({ message: `${userIds.length} users deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete users');
  }
});

// Update user status (activate/deactivate)
app.put('/api/settings/users/:id/status', async (c) => {
  try {
    const id = c.req.param('id');
    const { is_active } = await c.req.json();

    // Get current user ID from JWT to prevent self-deactivation
    const payload = c.get('jwtPayload');
    if (payload && payload.id === id && !is_active) {
      return c.json({ error: 'Cannot deactivate your own account' }, 400);
    }

    await c.env.DB.prepare(`
      UPDATE users 
      SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(is_active ? 1 : 0, id).run();

    return c.json({ message: `User ${is_active ? 'activated' : 'deactivated'} successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to update user status');
  }
});

// Reset user password
app.post('/api/settings/users/:id/reset-password', async (c) => {
  try {
    const id = c.req.param('id');
    const { password } = await c.req.json();

    if (!password || password.trim().length < 6) {
      return c.json({ error: 'Password must be at least 6 characters long' }, 400);
    }

    // Note: In production, you should properly hash passwords
    await c.env.DB.prepare(`
      UPDATE users 
      SET password_hash = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(password.trim(), id).run();

    return c.json({ message: 'Password reset successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to reset password');
  }
});

// Get user roles (for dropdown/select options)
app.get('/api/settings/user-roles', async (c) => {
  try {
    const roles = [
      { value: 'admin', label: 'Administrator', description: 'Full access to all features' },
      { value: 'manager', label: 'Manager', description: 'Can manage products, orders, and customers' },
      { value: 'editor', label: 'Editor', description: 'Can edit content and view reports' },
      { value: 'viewer', label: 'Viewer', description: 'Read-only access to most features' }
    ];

    return c.json({ roles });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch user roles');
  }
});

// ===========================================
// API KEYS MANAGEMENT ROUTES
// ===========================================

// Get all API keys
app.get('/api/settings/api-keys', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        id, title, type, redacted_token, prefix, last_used_at, 
        expires_at, created_at, updated_at
      FROM api_key
      WHERE deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (title LIKE ? OR prefix LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (status === 'active') {
      query += ` AND (expires_at IS NULL OR expires_at > datetime('now'))`;
    } else if (status === 'expired') {
      query += ` AND expires_at IS NOT NULL AND expires_at <= datetime('now')`;
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const apiKeys = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM api_key WHERE deleted_at IS NULL`;
    let countParams: any[] = [];
    
    if (search || status) {
      if (search) {
        countQuery += ` AND (title LIKE ? OR prefix LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`);
      }
      
      if (status === 'active') {
        countQuery += ` AND (expires_at IS NULL OR expires_at > datetime('now'))`;
      } else if (status === 'expired') {
        countQuery += ` AND expires_at IS NOT NULL AND expires_at <= datetime('now')`;
      }
    }

    const totalResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();

    // Add computed status to each API key
    const apiKeysWithStatus = apiKeys.results.map((key: any) => ({
      ...key,
      status: (!key.expires_at || new Date(String(key.expires_at)) > new Date()) ? 'active' : 'expired',
      is_expired: key.expires_at && new Date(String(key.expires_at)) <= new Date()
    }));

    return c.json({
      data: apiKeysWithStatus,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil((Number(totalResult?.total) || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch API keys');
  }
});

// Get single API key
app.get('/api/settings/api-keys/:id', async (c) => {
  try {
    const id = c.req.param('id');

    const apiKey = await c.env.DB.prepare(`
      SELECT 
        id, title, type, redacted_token, prefix, last_used_at,
        expires_at, created_at, updated_at
      FROM api_key
      WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first();

    if (!apiKey) {
      return c.json({ error: 'API key not found' }, 404);
    }

    return c.json({ 
      data: {
        ...apiKey,
        status: (!apiKey.expires_at || new Date(String(apiKey.expires_at)) > new Date()) ? 'active' : 'expired',
        is_expired: apiKey.expires_at && new Date(String(apiKey.expires_at)) <= new Date()
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch API key');
  }
});

// Create API key
app.post('/api/settings/api-keys', async (c) => {
  try {
    const { title, type, expires_at } = await c.req.json();

    if (!title || !type) {
      return c.json({ error: 'Title and type are required' }, 400);
    }

    const apiKeyId = generateId('ak');
    
    // Generate a secure token
    const token = generateId('sk') + '_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const prefix = token.substring(0, 12);
    const redactedToken = prefix + '...' + token.substring(token.length - 4);

    await c.env.DB.prepare(`
      INSERT INTO api_key (
        id, title, type, token, redacted_token, prefix, expires_at,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      apiKeyId,
      title,
      type,
      token,
      redactedToken,
      prefix,
      expires_at || null
    ).run();

    return c.json({ 
      id: apiKeyId, 
      token, // Return full token only on creation
      redacted_token: redactedToken,
      message: 'API key created successfully. Please save the token as it will not be shown again.' 
    }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create API key');
  }
});

// Update API key
app.put('/api/settings/api-keys/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const { title, expires_at } = await c.req.json();

    if (!title) {
      return c.json({ error: 'Title is required' }, 400);
    }

    await c.env.DB.prepare(`
      UPDATE api_key SET
        title = ?, expires_at = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(title, expires_at || null, id).run();

    return c.json({ message: 'API key updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update API key');
  }
});

// Delete API key
app.delete('/api/settings/api-keys/:id', async (c) => {
  try {
    const id = c.req.param('id');

    await c.env.DB.prepare(`
      UPDATE api_key 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run();

    return c.json({ message: 'API key deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete API key');
  }
});

// Bulk delete API keys
app.post('/api/settings/api-keys/bulk-delete', async (c) => {
  try {
    const { apiKeyIds } = await c.req.json();

    if (!apiKeyIds || !Array.isArray(apiKeyIds) || apiKeyIds.length === 0) {
      return c.json({ error: 'API key IDs are required' }, 400);
    }

    const placeholders = apiKeyIds.map(() => '?').join(',');
    
    await c.env.DB.prepare(`
      UPDATE api_key 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...apiKeyIds).run();

    return c.json({ message: `${apiKeyIds.length} API keys deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete API keys');
  }
});

// Regenerate API key token
app.post('/api/settings/api-keys/:id/regenerate', async (c) => {
  try {
    const id = c.req.param('id');

    // Generate a new secure token
    const token = generateId('sk') + '_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const prefix = token.substring(0, 12);
    const redactedToken = prefix + '...' + token.substring(token.length - 4);

    await c.env.DB.prepare(`
      UPDATE api_key 
      SET token = ?, redacted_token = ?, prefix = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(token, redactedToken, prefix, id).run();

    return c.json({ 
      token, // Return full token only on regeneration
      redacted_token: redactedToken,
      message: 'API key regenerated successfully. Please save the new token as it will not be shown again.' 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to regenerate API key');
  }
});

// Get API key usage statistics
app.get('/api/settings/api-keys/stats', async (c) => {
  try {
    const [totalKeys, activeKeys, expiredKeys, recentUsage] = await Promise.all([
      c.env.DB.prepare(`
        SELECT COUNT(*) as total FROM api_key WHERE deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COUNT(*) as active FROM api_key 
        WHERE deleted_at IS NULL AND (expires_at IS NULL OR expires_at > datetime('now'))
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COUNT(*) as expired FROM api_key 
        WHERE deleted_at IS NULL AND expires_at IS NOT NULL AND expires_at <= datetime('now')
      `).first(),
      
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as usage_count,
          DATE(last_used_at) as date
        FROM api_key 
        WHERE deleted_at IS NULL AND last_used_at >= date('now', '-30 days')
        GROUP BY DATE(last_used_at)
        ORDER BY date DESC
        LIMIT 30
      `).all()
    ]);

    return c.json({
      stats: {
        total_keys: totalKeys?.total || 0,
        active_keys: activeKeys?.active || 0,
        expired_keys: expiredKeys?.expired || 0,
        recent_usage: recentUsage?.results || []
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch API key statistics');
  }
});

// ===========================================
// PRODUCT IMAGES ROUTES
// ===========================================

// Get product images
app.get('/api/products/:id/images', async (c) => {
  try {
    const id = c.req.param('id');

    const images = await c.env.DB.prepare(`
      SELECT pi.id, pi.url, pi.alt_text, pi.sort_order, pi.metadata, pi.created_at
      FROM product_images pi
      WHERE pi.product_id = ?
      ORDER BY pi.sort_order, pi.created_at
    `).bind(id).all();

    return c.json({
      success: true,
      data: images.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product images');
  }
});

// ===========================================
// PRODUCT OPTION VALUES ROUTES  
// ===========================================

// Get option values for a specific option
app.get('/api/products/:productId/options/:optionId/values', async (c) => {
  try {
    const optionId = c.req.param('optionId');

    const values = await c.env.DB.prepare(`
      SELECT pov.id, pov.value, pov.hex_color, pov.image_url, pov.sort_order, pov.created_at
      FROM product_option_values pov
      WHERE pov.option_id = ?
      ORDER BY pov.sort_order, pov.created_at
    `).bind(optionId).all();

    return c.json({
      success: true,
      data: values.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch option values');
  }
});

export default app; 