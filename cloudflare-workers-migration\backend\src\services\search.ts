interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

export interface SearchResult<T> {
  hits: T[];
  found: number;
  page: number;
  search_time_ms: number;
  facet_counts?: Record<string, any>;
}

export interface SearchQuery {
  q: string;
  filters?: Record<string, any>;
  sort?: string;
  page?: number;
  limit?: number;
  facets?: string[];
}

export interface ProductSearchHit {
  id: string;
  title: string;
  description?: string;
  handle: string;
  thumbnail?: string;
  status: string;
  collection_id?: string;
  variants?: Array<{
    id: string;
    title: string;
    sku?: string;
    inventory_quantity: number;
  }>;
  images?: Array<{
    url: string;
    alt_text?: string;
  }>;
  tags?: string[];
  metadata?: Record<string, any>;
}

export class SearchService {
  private env: WorkerEnv;
  private typesenseEnabled: boolean;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.typesenseEnabled = !!env.TYPESENSE_API_KEY;
  }

  /**
   * Search products
   */
  async searchProducts(query: SearchQuery): Promise<SearchResult<ProductSearchHit>> {
    const startTime = Date.now();

    if (this.typesenseEnabled) {
      return await this.searchProductsWithTypesense(query);
    } else {
      return await this.searchProductsWithSQL(query);
    }
  }

  /**
   * Search products using SQL (fallback)
   */
  private async searchProductsWithSQL(query: SearchQuery): Promise<SearchResult<ProductSearchHit>> {
    const startTime = Date.now();
    const { q, page = 1, limit = 20, sort = 'created_at DESC' } = query;
    const offset = (page - 1) * limit;

    let whereClause = `WHERE p.status = 'published'`;
    const params: any[] = [];

    if (q && q.trim()) {
      whereClause += ` AND (p.title LIKE ? OR p.description LIKE ? OR p.handle LIKE ?)`;
      const searchTerm = `%${q.trim()}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Apply filters
    if (query.filters) {
      if (query.filters.collection_id) {
        whereClause += ` AND p.collection_id = ?`;
        params.push(query.filters.collection_id);
      }
      
      if (query.filters.status) {
        whereClause = whereClause.replace(`p.status = 'published'`, `p.status = ?`);
        params[0] = query.filters.status; // Replace the first status filter
      }
    }

    // Count total results
    const countQuery = `
      SELECT COUNT(*) as total
      FROM products p
      ${whereClause}
    `;
    
    const countResult = await this.env.DB.prepare(countQuery).bind(...params).first();
    const total = countResult?.total || 0;

    // Get products
    const productsQuery = `
      SELECT p.*, 
             pc.title as collection_title,
             pc.handle as collection_handle
      FROM products p
      LEFT JOIN product_collections pc ON p.collection_id = pc.id
      ${whereClause}
      ORDER BY ${sort}
      LIMIT ? OFFSET ?
    `;

    const productsResult = await this.env.DB.prepare(productsQuery)
      .bind(...params, limit, offset)
      .all();

    const products = productsResult.results;

    // Get variants and images for each product
    const hits: ProductSearchHit[] = [];
    for (const product of products) {
      // Get variants
      const variantsQuery = `
        SELECT id, title, sku, inventory_quantity
        FROM product_variants
        WHERE product_id = ?
        ORDER BY variant_rank ASC
      `;
      const variantsResult = await this.env.DB.prepare(variantsQuery).bind(product.id).all();

      // Get images
      const imagesQuery = `
        SELECT url, alt_text
        FROM product_images
        WHERE product_id = ?
        ORDER BY sort_order ASC
      `;
      const imagesResult = await this.env.DB.prepare(imagesQuery).bind(product.id).all();

      hits.push({
        id: product.id,
        title: product.title,
        description: product.description,
        handle: product.handle,
        thumbnail: product.thumbnail,
        status: product.status,
        collection_id: product.collection_id,
        variants: variantsResult.results,
        images: imagesResult.results,
        tags: product.metadata ? JSON.parse(product.metadata).tags || [] : [],
        metadata: product.metadata ? JSON.parse(product.metadata) : {},
      });
    }

    const searchTime = Date.now() - startTime;

    return {
      hits,
      found: total,
      page,
      search_time_ms: searchTime,
    };
  }

  /**
   * Search products using Typesense (when available)
   */
  private async searchProductsWithTypesense(query: SearchQuery): Promise<SearchResult<ProductSearchHit>> {
    // TODO: Implement Typesense integration
    // For now, fall back to SQL search
    return await this.searchProductsWithSQL(query);
  }

  /**
   * Get search suggestions/autocomplete
   */
  async getSearchSuggestions(query: string, limit: number = 5): Promise<string[]> {
    if (!query || query.trim().length < 2) {
      return [];
    }

    const searchTerm = `%${query.trim()}%`;
    const suggestionsQuery = `
      SELECT DISTINCT title
      FROM products
      WHERE status = 'published' AND title LIKE ?
      ORDER BY title ASC
      LIMIT ?
    `;

    const result = await this.env.DB.prepare(suggestionsQuery)
      .bind(searchTerm, limit)
      .all();

    return result.results.map((row: any) => row.title);
  }

  /**
   * Get popular search terms
   */
  async getPopularSearchTerms(limit: number = 10): Promise<Array<{ term: string; count: number }>> {
    // This would require a search analytics table to track search terms
    // For now, return some static popular terms
    return [
      { term: 'handmade', count: 100 },
      { term: 'traditional', count: 85 },
      { term: 'ceramic', count: 70 },
      { term: 'wood', count: 65 },
      { term: 'textile', count: 60 },
    ];
  }

  /**
   * Index a product for search (for Typesense)
   */
  async indexProduct(productId: string): Promise<void> {
    if (!this.typesenseEnabled) {
      return; // No indexing needed for SQL search
    }

    // TODO: Implement Typesense product indexing
    console.log(`Indexing product ${productId} for search`);
  }

  /**
   * Remove a product from search index
   */
  async removeProductFromIndex(productId: string): Promise<void> {
    if (!this.typesenseEnabled) {
      return; // No indexing needed for SQL search
    }

    // TODO: Implement Typesense product removal
    console.log(`Removing product ${productId} from search index`);
  }

  /**
   * Reindex all products
   */
  async reindexAllProducts(): Promise<void> {
    if (!this.typesenseEnabled) {
      return; // No indexing needed for SQL search
    }

    // TODO: Implement full reindexing for Typesense
    console.log('Reindexing all products for search');
  }

  /**
   * Get search facets/filters
   */
  async getSearchFacets(): Promise<Record<string, any>> {
    // Get available collections
    const collectionsQuery = `
      SELECT id, title, handle
      FROM product_collections
      ORDER BY title ASC
    `;
    const collectionsResult = await this.env.DB.prepare(collectionsQuery).all();

    // Get available statuses
    const statusesQuery = `
      SELECT DISTINCT status
      FROM products
      ORDER BY status ASC
    `;
    const statusesResult = await this.env.DB.prepare(statusesQuery).all();

    return {
      collections: collectionsResult.results,
      statuses: statusesResult.results.map((row: any) => row.status),
    };
  }
} 