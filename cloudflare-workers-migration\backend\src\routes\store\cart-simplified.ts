import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { Context } from 'hono';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Utility functions
function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error(error);
  return c.json({ success: false, error: message, details: error.message }, 500);
}

function safeParseJson(value: any): any {
  if (!value) return null;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }
  return value;
}

// Helper function to enrich cart with related data
async function enrichCart(cart: any, db: D1Database): Promise<any> {
  const enriched = { ...cart };

  // Parse metadata
  enriched.metadata = safeParseJson(enriched.metadata) || {};

  // Get line items with product and variant details
  const itemsQuery = `
    SELECT 
      cli.id, cli.cart_id, cli.variant_id, cli.quantity, cli.unit_price, cli.total_price,
      cli.title as item_title, cli.thumbnail as item_thumbnail, cli.metadata,
      pv.id as variant_id, pv.title as variant_title, pv.sku, pv.barcode,
      pv.option_values, pv.manage_inventory, pv.allow_backorder, pv.inventory_quantity,
      p.id as product_id, p.handle as product_handle, p.thumbnail as product_thumbnail,
      pt.title as product_title, pt.subtitle as product_subtitle, pt.description as product_description
    FROM cart_line_items cli
    LEFT JOIN product_variants pv ON cli.variant_id = pv.id
    LEFT JOIN products p ON pv.product_id = p.id
    LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
    WHERE cli.cart_id = ? AND cli.deleted_at IS NULL
    ORDER BY cli.created_at ASC
  `;

  const items = await db.prepare(itemsQuery).bind(cart.id).all();

  enriched.items = (items.results || []).map((item: any) => {
    const enrichedItem = { ...item };
    
    // Parse metadata and option values
    enrichedItem.metadata = safeParseJson(enrichedItem.metadata) || {};
    enrichedItem.option_values = safeParseJson(enrichedItem.option_values) || [];
    
    // Structure variant data for frontend compatibility
    enrichedItem.variant = {
      id: item.variant_id,
      title: item.variant_title,
      sku: item.sku,
      barcode: item.barcode,
      option_values: enrichedItem.option_values,
      manage_inventory: item.manage_inventory,
      allow_backorder: item.allow_backorder,
      inventory_quantity: item.inventory_quantity,
      product: {
        id: item.product_id,
        handle: item.product_handle,
        title: item.product_title,
        subtitle: item.product_subtitle,
        description: item.product_description,
        thumbnail: item.product_thumbnail
      }
    };

    return enrichedItem;
  });

  // Get billing address if exists
  if (cart.billing_address_id) {
    const billingQuery = `SELECT * FROM customer_addresses WHERE id = ?`;
    const billingAddress = await db.prepare(billingQuery).bind(cart.billing_address_id).first();
    if (billingAddress) {
      enriched.billing_address = billingAddress;
    }
  }

  // Get shipping address if exists
  if (cart.shipping_address_id) {
    const shippingQuery = `SELECT * FROM customer_addresses WHERE id = ?`;
    const shippingAddress = await db.prepare(shippingQuery).bind(cart.shipping_address_id).first();
    if (shippingAddress) {
      enriched.shipping_address = shippingAddress;
    }
  }

  // Get region details
  if (cart.region_id) {
    const regionQuery = `SELECT * FROM regions WHERE id = ?`;
    const region = await db.prepare(regionQuery).bind(cart.region_id).first();
    if (region) {
      enriched.region = region;
    }
  }

  // Calculate totals
  enriched.subtotal = enriched.items.reduce((sum: number, item: any) => {
    return sum + (item.unit_price * item.quantity);
  }, 0);

  enriched.shipping_total = 0; // TODO: Calculate shipping
  enriched.tax_total = 0; // TODO: Calculate tax
  enriched.total = enriched.subtotal + enriched.shipping_total + enriched.tax_total;

  return enriched;
}

// POST /store/carts - Create a new cart
app.post('/', async (c) => {
  try {
    const body = await c.req.json().catch(() => ({}));
    const { region_id, customer_id, email, currency_code = 'RON', metadata } = body;

    // Get default region if none provided
    let effectiveRegionId = region_id;
    if (!effectiveRegionId) {
      const regionsQuery = `SELECT id FROM regions WHERE deleted_at IS NULL ORDER BY created_at ASC LIMIT 1`;
      const defaultRegion = await c.env.DB.prepare(regionsQuery).first();
      if (defaultRegion) {
        effectiveRegionId = defaultRegion.id;
      }
    }

    if (!effectiveRegionId) {
      return c.json({
        success: false,
        error: 'No region available',
      }, 400);
    }

    // Create cart
    const cartId = crypto.randomUUID();
    const now = new Date().toISOString();
    
    const createQuery = `
      INSERT INTO carts (
        id, region_id, customer_id, email, currency_code, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await c.env.DB.prepare(createQuery).bind(
      cartId,
      effectiveRegionId,
      customer_id || null,
      email || null,
      currency_code,
      metadata ? JSON.stringify(metadata) : null,
      now,
      now
    ).run();

    // Get created cart
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    
    // Enrich cart with related data
    const enrichedCart = await enrichCart(cart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create cart');
  }
});

// GET /store/carts/:id - Get a specific cart
app.get('/:id', async (c) => {
  try {
    const cartId = c.req.param('id');

    // Get cart from database
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ? AND deleted_at IS NULL`).bind(cartId).first();
    
    if (!cart) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    const enrichedCart = await enrichCart(cart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch cart');
  }
});

// POST /store/carts/:id/line-items - Add item to cart
app.post('/:id/line-items', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = await c.req.json();
    const { variant_id, quantity = 1, metadata } = body;

    if (!variant_id) {
      return c.json({ success: false, error: 'Variant ID is required' }, 400);
    }

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ? AND deleted_at IS NULL`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Verify variant exists and get product details
    const variantQuery = `
      SELECT
        pv.id, pv.product_id, pv.title, pv.sku, pv.manage_inventory, pv.inventory_quantity,
        p.title as product_title, p.thumbnail, p.status,
        vp.price, vp.currency_code
      FROM product_variants pv
      LEFT JOIN products p ON pv.product_id = p.id
      LEFT JOIN variant_prices vp ON pv.id = vp.variant_id AND vp.currency_code = ?
      WHERE pv.id = ? AND pv.deleted_at IS NULL AND p.deleted_at IS NULL AND p.status = 'published'
    `;

    const variant = await c.env.DB.prepare(variantQuery).bind(cart.currency_code || 'RON', variant_id).first();

    if (!variant) {
      return c.json({ success: false, error: 'Product variant not found or not available' }, 404);
    }

    // Check inventory if managed
    if (variant.manage_inventory && variant.inventory_quantity < quantity) {
      return c.json({ success: false, error: 'Insufficient inventory' }, 400);
    }

    const unitPrice = variant.price || 0;
    const totalPrice = unitPrice * quantity;

    // Check if item already exists in cart
    const existingItemQuery = `
      SELECT * FROM cart_line_items
      WHERE cart_id = ? AND variant_id = ? AND deleted_at IS NULL
    `;
    const existingItem = await c.env.DB.prepare(existingItemQuery).bind(cartId, variant_id).first();

    const now = new Date().toISOString();

    if (existingItem) {
      // Update existing item
      const newQuantity = existingItem.quantity + quantity;
      const newTotalPrice = unitPrice * newQuantity;

      // Check inventory again for new total
      if (variant.manage_inventory && variant.inventory_quantity < newQuantity) {
        return c.json({ success: false, error: 'Insufficient inventory' }, 400);
      }

      const updateQuery = `
        UPDATE cart_line_items
        SET quantity = ?, total_price = ?, metadata = ?, updated_at = ?
        WHERE id = ?
      `;

      await c.env.DB.prepare(updateQuery).bind(
        newQuantity,
        newTotalPrice,
        metadata ? JSON.stringify(metadata) : existingItem.metadata,
        now,
        existingItem.id
      ).run();
    } else {
      // Create new line item
      const lineItemId = crypto.randomUUID();

      const insertQuery = `
        INSERT INTO cart_line_items (
          id, cart_id, variant_id, quantity, unit_price, total_price,
          title, thumbnail, metadata, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await c.env.DB.prepare(insertQuery).bind(
        lineItemId,
        cartId,
        variant_id,
        quantity,
        unitPrice,
        totalPrice,
        variant.product_title,
        variant.thumbnail,
        metadata ? JSON.stringify(metadata) : null,
        now,
        now
      ).run();
    }

    // Update cart timestamp
    await c.env.DB.prepare(`UPDATE carts SET updated_at = ? WHERE id = ?`).bind(now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to add item to cart');
  }
});

// POST /store/carts/:id/line-items/:line_id - Update line item
app.post('/:id/line-items/:line_id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const lineId = c.req.param('line_id');
    const body = await c.req.json();
    const { quantity, metadata } = body;

    if (quantity === undefined || quantity < 0) {
      return c.json({ success: false, error: 'Valid quantity is required' }, 400);
    }

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ? AND deleted_at IS NULL`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Verify line item exists and belongs to cart
    const lineItem = await c.env.DB.prepare(`
      SELECT * FROM cart_line_items
      WHERE id = ? AND cart_id = ? AND deleted_at IS NULL
    `).bind(lineId, cartId).first();

    if (!lineItem) {
      return c.json({ success: false, error: 'Line item not found' }, 404);
    }

    const now = new Date().toISOString();

    if (quantity === 0) {
      // Delete the item
      await c.env.DB.prepare(`
        UPDATE cart_line_items SET deleted_at = ? WHERE id = ?
      `).bind(now, lineId).run();
    } else {
      // Update the item
      const newTotalPrice = lineItem.unit_price * quantity;

      const updateQuery = `
        UPDATE cart_line_items
        SET quantity = ?, total_price = ?, metadata = ?, updated_at = ?
        WHERE id = ?
      `;

      await c.env.DB.prepare(updateQuery).bind(
        quantity,
        newTotalPrice,
        metadata ? JSON.stringify(metadata) : lineItem.metadata,
        now,
        lineId
      ).run();
    }

    // Update cart timestamp
    await c.env.DB.prepare(`UPDATE carts SET updated_at = ? WHERE id = ?`).bind(now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update line item');
  }
});

// DELETE /store/carts/:id/line-items/:line_id - Remove line item
app.delete('/:id/line-items/:line_id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const lineId = c.req.param('line_id');

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ? AND deleted_at IS NULL`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Verify line item exists and belongs to cart
    const lineItem = await c.env.DB.prepare(`
      SELECT * FROM cart_line_items
      WHERE id = ? AND cart_id = ? AND deleted_at IS NULL
    `).bind(lineId, cartId).first();

    if (!lineItem) {
      return c.json({ success: false, error: 'Line item not found' }, 404);
    }

    const now = new Date().toISOString();

    // Delete the item
    await c.env.DB.prepare(`
      UPDATE cart_line_items SET deleted_at = ? WHERE id = ?
    `).bind(now, lineId).run();

    // Update cart timestamp
    await c.env.DB.prepare(`UPDATE carts SET updated_at = ? WHERE id = ?`).bind(now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to remove line item');
  }
});

// POST /store/carts/:id - Update cart
app.post('/:id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = await c.req.json();
    const { email, billing_address, shipping_address, metadata } = body;

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ? AND deleted_at IS NULL`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    const now = new Date().toISOString();
    const updateData: any = { updated_at: now };

    // Update email
    if (email) {
      updateData.email = email;
    }

    // Update metadata
    if (metadata) {
      updateData.metadata = JSON.stringify(metadata);
    }

    // Handle billing address
    if (billing_address) {
      let billingAddressId = cart.billing_address_id;

      if (billingAddressId) {
        // Update existing address
        const updateAddressQuery = `
          UPDATE customer_addresses
          SET first_name = ?, last_name = ?, address_1 = ?, address_2 = ?,
              city = ?, country_code = ?, province = ?, postal_code = ?,
              phone = ?, company = ?, updated_at = ?
          WHERE id = ?
        `;
        await c.env.DB.prepare(updateAddressQuery).bind(
          billing_address.first_name, billing_address.last_name,
          billing_address.address_1, billing_address.address_2,
          billing_address.city, billing_address.country_code,
          billing_address.province, billing_address.postal_code,
          billing_address.phone, billing_address.company,
          now, billingAddressId
        ).run();
      } else {
        // Create new address
        billingAddressId = crypto.randomUUID();
        const createAddressQuery = `
          INSERT INTO customer_addresses (
            id, first_name, last_name, address_1, address_2, city,
            country_code, province, postal_code, phone, company, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        await c.env.DB.prepare(createAddressQuery).bind(
          billingAddressId, billing_address.first_name, billing_address.last_name,
          billing_address.address_1, billing_address.address_2, billing_address.city,
          billing_address.country_code, billing_address.province, billing_address.postal_code,
          billing_address.phone, billing_address.company, now, now
        ).run();
      }

      updateData.billing_address_id = billingAddressId;
    }

    // Handle shipping address
    if (shipping_address) {
      let shippingAddressId = cart.shipping_address_id;

      if (shippingAddressId) {
        // Update existing address
        const updateAddressQuery = `
          UPDATE customer_addresses
          SET first_name = ?, last_name = ?, address_1 = ?, address_2 = ?,
              city = ?, country_code = ?, province = ?, postal_code = ?,
              phone = ?, company = ?, updated_at = ?
          WHERE id = ?
        `;
        await c.env.DB.prepare(updateAddressQuery).bind(
          shipping_address.first_name, shipping_address.last_name,
          shipping_address.address_1, shipping_address.address_2,
          shipping_address.city, shipping_address.country_code,
          shipping_address.province, shipping_address.postal_code,
          shipping_address.phone, shipping_address.company,
          now, shippingAddressId
        ).run();
      } else {
        // Create new address
        shippingAddressId = crypto.randomUUID();
        const createAddressQuery = `
          INSERT INTO customer_addresses (
            id, first_name, last_name, address_1, address_2, city,
            country_code, province, postal_code, phone, company, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        await c.env.DB.prepare(createAddressQuery).bind(
          shippingAddressId, shipping_address.first_name, shipping_address.last_name,
          shipping_address.address_1, shipping_address.address_2, shipping_address.city,
          shipping_address.country_code, shipping_address.province, shipping_address.postal_code,
          shipping_address.phone, shipping_address.company, now, now
        ).run();
      }

      updateData.shipping_address_id = shippingAddressId;
    }

    // Update cart
    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const updateValues = Object.values(updateData);

    const updateQuery = `UPDATE carts SET ${updateFields} WHERE id = ?`;
    await c.env.DB.prepare(updateQuery).bind(...updateValues, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update cart');
  }
});

// GET /store/carts/:id/shipping-options - Get shipping options for cart
app.get('/:id/shipping-options', async (c) => {
  try {
    const cartId = c.req.param('id');

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ? AND deleted_at IS NULL`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Get shipping options for the cart's region
    const shippingOptionsQuery = `
      SELECT
        so.id, so.name, so.description, so.is_return, so.admin_only,
        so.metadata, so.created_at, so.updated_at,
        sp.price, sp.currency_code
      FROM shipping_options so
      LEFT JOIN shipping_option_prices sp ON so.id = sp.shipping_option_id
        AND sp.currency_code = ?
      WHERE so.deleted_at IS NULL
        AND so.is_return = 0
        AND so.admin_only = 0
        AND (so.region_id = ? OR so.region_id IS NULL)
      ORDER BY sp.price ASC, so.created_at ASC
    `;

    const shippingOptions = await c.env.DB.prepare(shippingOptionsQuery)
      .bind(cart.currency_code || 'RON', cart.region_id)
      .all();

    const processedOptions = (shippingOptions.results || []).map((option: any) => {
      option.metadata = safeParseJson(option.metadata) || {};
      option.price = option.price || 0;
      return option;
    });

    return c.json({
      success: true,
      data: processedOptions,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch shipping options');
  }
});

// POST /store/carts/:id/shipping-methods - Add shipping method to cart
app.post('/:id/shipping-methods', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = await c.req.json();
    const { option_id, data } = body;

    if (!option_id) {
      return c.json({ success: false, error: 'Shipping option ID is required' }, 400);
    }

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ? AND deleted_at IS NULL`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Verify shipping option exists
    const shippingOption = await c.env.DB.prepare(`
      SELECT so.*, sp.price, sp.currency_code
      FROM shipping_options so
      LEFT JOIN shipping_option_prices sp ON so.id = sp.shipping_option_id
        AND sp.currency_code = ?
      WHERE so.id = ? AND so.deleted_at IS NULL
    `).bind(cart.currency_code || 'RON', option_id).first();

    if (!shippingOption) {
      return c.json({ success: false, error: 'Shipping option not found' }, 404);
    }

    const now = new Date().toISOString();

    // Remove existing shipping methods for this cart
    await c.env.DB.prepare(`
      UPDATE shipping_methods SET deleted_at = ? WHERE cart_id = ? AND deleted_at IS NULL
    `).bind(now, cartId).run();

    // Add new shipping method
    const shippingMethodId = crypto.randomUUID();
    const insertQuery = `
      INSERT INTO shipping_methods (
        id, cart_id, shipping_option_id, price, data, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    await c.env.DB.prepare(insertQuery).bind(
      shippingMethodId,
      cartId,
      option_id,
      shippingOption.price || 0,
      data ? JSON.stringify(data) : null,
      now,
      now
    ).run();

    // Update cart timestamp
    await c.env.DB.prepare(`UPDATE carts SET updated_at = ? WHERE id = ?`).bind(now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to add shipping method');
  }
});

// GET /store/regions - Get available regions
app.get('/regions', async (c) => {
  try {
    const query = `
      SELECT
        r.id, r.name, r.currency_code, r.tax_rate, r.metadata, r.created_at, r.updated_at,
        GROUP_CONCAT(rc.country_code) as countries
      FROM regions r
      LEFT JOIN region_countries rc ON r.id = rc.region_id
      WHERE r.deleted_at IS NULL
      GROUP BY r.id, r.name, r.currency_code, r.tax_rate, r.metadata, r.created_at, r.updated_at
      ORDER BY r.created_at ASC
    `;

    const regions = await c.env.DB.prepare(query).all();

    const processedRegions = (regions.results || []).map((region: any) => {
      region.metadata = safeParseJson(region.metadata) || {};
      region.countries = region.countries ? region.countries.split(',') : [];
      return region;
    });

    return c.json({
      success: true,
      data: processedRegions
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch regions');
  }
});

export const cartSimplifiedRoutes = app;
