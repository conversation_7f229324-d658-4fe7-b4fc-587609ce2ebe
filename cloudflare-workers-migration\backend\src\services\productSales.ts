import { DatabaseService } from './database';

interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

export interface ProductSales {
  id: string;
  product_id: string;
  total_sales: number;
  quantity_sold: number;
  revenue: number; // in cents
  last_sale_at?: string;
  created_at: string;
  updated_at: string;
}

export interface SalesUpdate {
  product_id: string;
  quantity: number;
  amount: number; // in cents
  sale_date?: string;
}

export class ProductSalesService {
  private db: DatabaseService;
  private env: WorkerEnv;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.db = new DatabaseService(env);
  }

  /**
   * Get product sales data by product ID
   */
  async getProductSales(productId: string): Promise<ProductSales | null> {
    // Use raw SQL query for now to avoid drizzle-orm import issues
    const query = `SELECT * FROM product_sales WHERE product_id = ? LIMIT 1`;
    const result = await this.env.DB.prepare(query).bind(productId).first();
    
    if (!result) return null;

    return {
      id: result.id,
      product_id: result.product_id,
      total_sales: result.total_sales || 0,
      quantity_sold: result.quantity_sold || 0,
      revenue: result.revenue || 0,
      last_sale_at: result.last_sale_at,
      created_at: result.created_at,
      updated_at: result.updated_at,
    };
  }

  /**
   * Get sales data for multiple products
   */
  async getMultipleProductSales(productIds: string[]): Promise<ProductSales[]> {
    // For simplicity, we'll fetch each product's sales individually
    // In a real implementation, this could be optimized with a single query
    const salesData: ProductSales[] = [];
    
    for (const productId of productIds) {
      const sales = await this.getProductSales(productId);
      if (sales) {
        salesData.push(sales);
      }
    }

    return salesData;
  }

  /**
   * Get top selling products
   */
  async getTopSellingProducts(options: {
    limit?: number;
    sortBy?: 'quantity_sold' | 'revenue' | 'total_sales';
    timeframe?: 'all' | '30d' | '7d' | '1d';
  } = {}): Promise<ProductSales[]> {
    const { limit = 10, sortBy = 'quantity_sold' } = options;

    const query = `SELECT * FROM product_sales ORDER BY ${sortBy} DESC LIMIT ?`;
    const results = await this.env.DB.prepare(query).bind(limit).all();
    
    return results.results.map((sales: any) => ({
      id: sales.id,
      product_id: sales.product_id,
      total_sales: sales.total_sales || 0,
      quantity_sold: sales.quantity_sold || 0,
      revenue: sales.revenue || 0,
      last_sale_at: sales.last_sale_at,
      created_at: sales.created_at,
      updated_at: sales.updated_at,
    }));
  }

  /**
   * Update product sales when an order is completed
   */
  async updateProductSales(updates: SalesUpdate[]): Promise<void> {
    for (const update of updates) {
      await this.updateSingleProductSales(update);
    }
  }

  /**
   * Update sales for a single product
   */
  private async updateSingleProductSales(update: SalesUpdate): Promise<void> {
    const existingSales = await this.getProductSales(update.product_id);
    const saleDate = update.sale_date || new Date().toISOString();

    if (existingSales) {
      // Update existing sales record
      const newTotalSales = existingSales.total_sales + 1;
      const newQuantitySold = existingSales.quantity_sold + update.quantity;
      const newRevenue = existingSales.revenue + update.amount;

      const updateQuery = `
        UPDATE product_sales 
        SET total_sales = ?, quantity_sold = ?, revenue = ?, last_sale_at = ?, updated_at = ?
        WHERE id = ?
      `;
      
      await this.env.DB.prepare(updateQuery).bind(
        newTotalSales,
        newQuantitySold,
        newRevenue,
        saleDate,
        new Date().toISOString(),
        existingSales.id
      ).run();
    } else {
      // Create new sales record
      const salesId = `ps_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const insertQuery = `
        INSERT INTO product_sales (id, product_id, total_sales, quantity_sold, revenue, last_sale_at, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      await this.env.DB.prepare(insertQuery).bind(
        salesId,
        update.product_id,
        1,
        update.quantity,
        update.amount,
        saleDate,
        new Date().toISOString(),
        new Date().toISOString()
      ).run();
    }
  }

  /**
   * Handle order completion event
   */
  async handleOrderCompleted(orderData: {
    id: string;
    items: Array<{
      product_id?: string;
      variant_id?: string;
      quantity: number;
      unit_price: number;
    }>;
    completed_at?: string;
  }): Promise<void> {
    const updates: SalesUpdate[] = [];

    for (const item of orderData.items) {
      if (item.product_id) {
        updates.push({
          product_id: item.product_id,
          quantity: item.quantity,
          amount: item.unit_price * item.quantity,
          sale_date: orderData.completed_at,
        });
      }
    }

    if (updates.length > 0) {
      await this.updateProductSales(updates);
    }
  }

  /**
   * Get sales analytics for a specific time period
   */
  async getSalesAnalytics(options: {
    productId?: string;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<{
    totalRevenue: number;
    totalQuantity: number;
    totalOrders: number;
    averageOrderValue: number;
  }> {
    let query = `SELECT * FROM product_sales`;
    const params: any[] = [];

    if (options.productId) {
      query += ` WHERE product_id = ?`;
      params.push(options.productId);
    }

    const results = await this.env.DB.prepare(query).bind(...params).all();
    const salesData = results.results;

    const totalRevenue = salesData.reduce((sum: number, sales: any) => sum + (sales.revenue || 0), 0);
    const totalQuantity = salesData.reduce((sum: number, sales: any) => sum + (sales.quantity_sold || 0), 0);
    const totalOrders = salesData.reduce((sum: number, sales: any) => sum + (sales.total_sales || 0), 0);
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    return {
      totalRevenue,
      totalQuantity,
      totalOrders,
      averageOrderValue,
    };
  }

  /**
   * Reset sales data for a product (admin function)
   */
  async resetProductSales(productId: string): Promise<void> {
    const existingSales = await this.getProductSales(productId);
    if (existingSales) {
      const query = `
        UPDATE product_sales 
        SET total_sales = 0, quantity_sold = 0, revenue = 0, last_sale_at = NULL, updated_at = ?
        WHERE id = ?
      `;
      
      await this.env.DB.prepare(query).bind(
        new Date().toISOString(),
        existingSales.id
      ).run();
    }
  }

  /**
   * Delete sales data for a product
   */
  async deleteProductSales(productId: string): Promise<void> {
    const query = `DELETE FROM product_sales WHERE product_id = ?`;
    await this.env.DB.prepare(query).bind(productId).run();
  }
} 