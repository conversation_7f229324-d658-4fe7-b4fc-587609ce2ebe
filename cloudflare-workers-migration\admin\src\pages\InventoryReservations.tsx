import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  MagnifyingGlassIcon,
  CubeIcon,
  ClockIcon,
  XMarkIcon,
  BuildingStorefrontIcon,
  ShoppingCartIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { api, endpoints } from '../lib/api';

interface InventoryReservation {
  id: string;
  line_item_id: string;
  inventory_item_id: string;
  location_id: string;
  quantity: number;
  description?: string;
  created_by?: string;
  metadata?: any;
  item?: {
    id: string;
    sku: string;
    title: string;
    thumbnail?: string;
  };
  location?: {
    id: string;
    name: string;
  };
  line_item?: {
    id: string;
    order_id: string;
    order_display_id?: string;
    quantity: number;
  };
  created_at: string;
  updated_at: string;
}

const InventoryReservations: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  const queryClient = useQueryClient();

  // Fetch inventory reservations
  const { data: reservationsData, isLoading, error } = useQuery({
    queryKey: ['inventory-reservations', currentPage, searchTerm, locationFilter, statusFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
      });
      
      if (searchTerm) params.append('search', searchTerm);
      if (locationFilter !== 'all') params.append('location_id', locationFilter);
      if (statusFilter !== 'all') params.append('status', statusFilter);

      const response = await api.get(`${endpoints.inventory.reservations}?${params}`);
      return response.data;
    },
  });

  // Fetch locations for filter
  const { data: locationsData } = useQuery({
    queryKey: ['stock-locations-filter'],
    queryFn: async () => {
      const response = await api.get(`${endpoints.inventory.locations}?limit=100`);
      return response.data;
    },
  });

  // Cancel reservation mutation
  const cancelReservationMutation = useMutation({
    mutationFn: async (reservationId: string) => {
      return api.delete(endpoints.inventory.deleteReservation(reservationId));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory-reservations'] });
      toast.success('Reservation cancelled successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to cancel reservation');
    },
  });

  const handleCancelReservation = (reservation: InventoryReservation) => {
    if (confirm(`Are you sure you want to cancel this reservation for ${reservation.quantity} units of ${reservation.item?.title}?`)) {
      cancelReservationMutation.mutate(reservation.id);
    }
  };

  const getReservationStatus = (reservation: InventoryReservation) => {
    // This would normally check if the order is still active, paid, etc.
    // For now, we'll assume all reservations are active
    return 'active';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const reservations = reservationsData?.reservations || [];
  const locations = locationsData?.locations || [];
  const totalPages = Math.ceil((reservationsData?.total || 0) / 20);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">
          Failed to load inventory reservations. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Inventory Reservations</h1>
          <p className="text-gray-600">Monitor and manage inventory reservations from orders</p>
        </div>
        <div className="text-sm text-gray-500">
          Total Reserved: {reservations.reduce((total: number, res: InventoryReservation) => total + res.quantity, 0)} units
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search by SKU, item title, or order ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <select
          value={locationFilter}
          onChange={(e) => setLocationFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Locations</option>
          {locations.map((location: any) => (
            <option key={location.id} value={location.id}>{location.name}</option>
          ))}
        </select>
        
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Statuses</option>
          <option value="active">Active</option>
          <option value="fulfilled">Fulfilled</option>
          <option value="cancelled">Cancelled</option>
        </select>
      </div>

      {/* Reservations List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Inventory Reservations ({reservationsData?.total || 0})
          </h3>
        </div>

        {reservations.length === 0 ? (
          <div className="text-center py-12">
            <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No inventory reservations found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || locationFilter !== 'all' ? 'No reservations match your search criteria.' : 'No inventory is currently reserved.'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {reservations.map((reservation: InventoryReservation) => {
              const status = getReservationStatus(reservation);
              
              return (
                <div key={reservation.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-start space-x-4">
                      {reservation.item?.thumbnail ? (
                        <img
                          src={reservation.item.thumbnail}
                          alt={reservation.item.title}
                          className="w-12 h-12 object-cover rounded-md"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                          <CubeIcon className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="font-medium text-gray-900">{reservation.item?.title}</h4>
                          <span className="text-sm text-gray-500">SKU: {reservation.item?.sku}</span>
                          {status === 'active' && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <ClockIcon className="h-3 w-3 mr-1" />
                              Reserved
                            </span>
                          )}
                          {status === 'fulfilled' && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <CheckCircleIcon className="h-3 w-3 mr-1" />
                              Fulfilled
                            </span>
                          )}
                          {status === 'cancelled' && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <XMarkIcon className="h-3 w-3 mr-1" />
                              Cancelled
                            </span>
                          )}
                        </div>
                        
                        <div className="flex items-center space-x-4 mt-1 text-sm">
                          <div className="flex items-center space-x-1">
                            <BuildingStorefrontIcon className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-500">{reservation.location?.name}</span>
                          </div>
                          
                          {reservation.line_item?.order_display_id && (
                            <div className="flex items-center space-x-1">
                              <ShoppingCartIcon className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-500">Order #{reservation.line_item.order_display_id}</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-3 text-sm">
                          <div>
                            <span className="text-gray-500">Reserved Quantity:</span>
                            <span className="font-medium text-gray-900 ml-2">{reservation.quantity}</span>
                          </div>
                          
                          {reservation.line_item && (
                            <div>
                              <span className="text-gray-500">Order Quantity:</span>
                              <span className="font-medium text-gray-900 ml-2">{reservation.line_item.quantity}</span>
                            </div>
                          )}
                          
                          <div>
                            <span className="text-gray-500">Reserved:</span>
                            <span className="text-gray-500 ml-2">{formatDate(reservation.created_at)}</span>
                          </div>
                        </div>
                        
                        {reservation.description && (
                          <div className="mt-2">
                            <span className="text-gray-500 text-sm">Description:</span>
                            <p className="text-sm text-gray-600">{reservation.description}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {status === 'active' && (
                        <button
                          onClick={() => handleCancelReservation(reservation)}
                          disabled={cancelReservationMutation.isPending}
                          className="flex items-center text-red-600 hover:text-red-800 text-sm bg-red-50 px-3 py-1 rounded-md"
                          title="Cancel reservation"
                        >
                          <XMarkIcon className="h-4 w-4 mr-1" />
                          Cancel
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {(currentPage - 1) * 20 + 1} to {Math.min(currentPage * 20, reservationsData?.total || 0)} of {reservationsData?.total || 0} reservations
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClockIcon className="h-8 w-8 text-blue-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Active Reservations
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reservations.filter((r: InventoryReservation) => getReservationStatus(r) === 'active').length}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CubeIcon className="h-8 w-8 text-yellow-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Total Reserved Items
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reservations.reduce((total: number, res: InventoryReservation) => total + res.quantity, 0)}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BuildingStorefrontIcon className="h-8 w-8 text-green-400" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Locations with Reservations
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {new Set(reservations.map((r: InventoryReservation) => r.location_id)).size}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InventoryReservations; 