import { WorkerEnv } from 'handmadein-shared';

export class DatabaseService {
  private db: any;

  constructor(env: WorkerEnv) {
    this.db = env.DB;
  }

  // Generic CRUD operations with direct SQL
  async findById(table: string, id: string): Promise<any | null> {
    try {
      const query = `SELECT * FROM "${table}" WHERE id = ? AND (deleted_at IS NULL OR deleted_at = '') LIMIT 1`;
      const result = await this.db.prepare(query).bind(id).first();
      return result || null;
    } catch (error) {
      console.error(`Error finding ${table} by id ${id}:`, error);
      return null;
    }
  }

  async findMany(table: string, options: {
    where?: string;
    limit?: number;
    offset?: number;
    orderBy?: string;
  } = {}): Promise<any[]> {
    try {
      let query = `SELECT * FROM "${table}"`;
      
      // Add WHERE clause for non-deleted items
      const conditions = ['(deleted_at IS NULL OR deleted_at = \'\')'];
      
      if (options.where) {
        conditions.push(`(${options.where})`);
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      if (options.orderBy) {
        query += ` ORDER BY ${options.orderBy}`;
      } else {
        // Default ordering by updated_at DESC
        query += ` ORDER BY updated_at DESC`;
      }

      if (options.limit) {
        query += ` LIMIT ${options.limit}`;
      }

      if (options.offset) {
        query += ` OFFSET ${options.offset}`;
      }

      console.log(`Executing query: ${query}`);
      const result = await this.db.prepare(query).all();
      console.log(`Query returned ${result.results?.length || 0} results`);
      return result.results || [];
    } catch (error) {
      console.error(`Error finding ${table}:`, error);
      return [];
    }
  }

  async create(table: string, data: any): Promise<any> {
    try {
      const id = data.id || `${table.slice(0, 4)}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date().toISOString();
      
      const finalData = {
        ...data,
        id,
        created_at: now,
        updated_at: now,
      };

      // Remove undefined values
      Object.keys(finalData).forEach(key => {
        if (finalData[key] === undefined) {
          delete finalData[key];
        }
      });

      const columns = Object.keys(finalData);
      const placeholders = columns.map(() => '?').join(', ');
      const values = columns.map(col => finalData[col]);

      const query = `INSERT INTO "${table}" (${columns.map(c => `"${c}"`).join(', ')}) VALUES (${placeholders})`;
      
      console.log(`Executing insert: ${query}`, values);
      await this.db.prepare(query).bind(...values).run();
      
      return await this.findById(table, id);
    } catch (error) {
      console.error(`Error creating ${table}:`, error);
      throw error;
    }
  }

  async update(table: string, id: string, data: any): Promise<any> {
    try {
      const now = new Date().toISOString();
      const updateData = {
        ...data,
        updated_at: now,
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      const columns = Object.keys(updateData);
      const setClause = columns.map(col => `"${col}" = ?`).join(', ');
      const values = columns.map(col => updateData[col]);

      const query = `UPDATE "${table}" SET ${setClause} WHERE id = ?`;
      
      console.log(`Executing update: ${query}`, values);
      await this.db.prepare(query).bind(...values, id).run();
      
      return await this.findById(table, id);
    } catch (error) {
      console.error(`Error updating ${table} with id ${id}:`, error);
      throw error;
    }
  }

  async delete(table: string, id: string): Promise<boolean> {
    try {
      const query = `DELETE FROM "${table}" WHERE id = ?`;
      const result = await this.db.prepare(query).bind(id).run();
      return result.changes > 0;
    } catch (error) {
      console.error(`Error deleting ${table} with id ${id}:`, error);
      return false;
    }
  }

  async softDelete(table: string, id: string): Promise<any> {
    try {
      const now = new Date().toISOString();
      const query = `UPDATE "${table}" SET deleted_at = ?, updated_at = ? WHERE id = ?`;
      
      await this.db.prepare(query).bind(now, now, id).run();
      
      return await this.findById(table, id);
    } catch (error) {
      console.error(`Error soft deleting ${table} with id ${id}:`, error);
      throw error;
    }
  }

  async count(table: string, where?: string): Promise<number> {
    try {
      let query = `SELECT COUNT(*) as count FROM "${table}"`;
      
      const conditions = ['(deleted_at IS NULL OR deleted_at = \'\')'];
      if (where) {
        conditions.push(where);
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      const result = await this.db.prepare(query).first();
      return result?.count || 0;
    } catch (error) {
      console.error(`Error counting ${table}:`, error);
      return 0;
    }
  }

  // Product-specific operations
  async findProductByHandle(handle: string) {
    try {
      const query = `SELECT * FROM "product" WHERE handle = ? AND (deleted_at IS NULL OR deleted_at = '') LIMIT 1`;
      const result = await this.db.prepare(query).bind(handle).first();
      return result || null;
    } catch (error) {
      console.error(`Error finding product by handle ${handle}:`, error);
      return null;
    }
  }

  async findProductsWithVariants(options: {
    where?: string;
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      console.log('findProductsWithVariants called with options:', options);
      const products = await this.findMany('product', options);
      console.log(`Found ${products.length} products`);
      
      for (const product of products) {
        try {
          // Get variants
          const variantQuery = `SELECT * FROM "product_variant" WHERE product_id = ? AND (deleted_at IS NULL OR deleted_at = '')`;
          const variantResult = await this.db.prepare(variantQuery).bind(product.id).all();
          product.variants = variantResult.results || [];

          // Get images - using 'rank' field from the actual schema
          const imageQuery = `SELECT * FROM "image" WHERE product_id = ? AND (deleted_at IS NULL OR deleted_at = '') ORDER BY rank ASC`;
          const imageResult = await this.db.prepare(imageQuery).bind(product.id).all();
          product.images = imageResult.results || [];

          // Ensure product has required fields for admin interface
          product.price = product.price || 0;
          product.compare_at_price = product.compare_at_price || null;
          product.inventory_quantity = product.inventory_quantity || 0;
          product.weight = product.weight || 0;
          product.discountable = product.discountable !== undefined ? product.discountable : true;
          
        } catch (productError) {
          console.error(`Error processing product ${product.id}:`, productError);
          product.variants = [];
          product.images = [];
        }
      }

      return products;
    } catch (error) {
      console.error('Error finding products with variants:', error);
      return [];
    }
  }

  // Customer-specific operations
  async findCustomerByEmail(email: string) {
    try {
      const query = `SELECT * FROM "customer" WHERE email = ? AND (deleted_at IS NULL OR deleted_at = '') ORDER BY has_account DESC LIMIT 1`;
      const result = await this.db.prepare(query).bind(email).first();
      return result || null;
    } catch (error) {
      console.error(`Error finding customer by email ${email}:`, error);
      return null;
    }
  }

  // Order-specific operations
  async findOrdersWithItems(customerId?: string) {
    try {
      let query = `SELECT * FROM "orders"`;
      const bindings: any[] = [];
      
      const conditions = ['(deleted_at IS NULL OR deleted_at = \'\')'];
      
      if (customerId) {
        conditions.push('customer_id = ?');
        bindings.push(customerId);
      }

      query += ` WHERE ${conditions.join(' AND ')} ORDER BY created_at DESC`;

      const result = await this.db.prepare(query).bind(...bindings).all();
      const orders = result.results || [];

      for (const order of orders) {
        const itemsQuery = `SELECT * FROM "order_items" WHERE order_id = ?`;
        const itemsResult = await this.db.prepare(itemsQuery).bind(order.id).all();
        order.items = itemsResult.results || [];
      }

      return orders;
    } catch (error) {
      console.error('Error finding orders with items:', error);
      return [];
    }
  }

  // Cart-specific operations
  async findCartWithItems(cartId: string) {
    try {
      const cart = await this.findById('cart', cartId);
      if (!cart) return null;

      const itemsQuery = `SELECT * FROM "cart_line_item" WHERE cart_id = ?`;
      const itemsResult = await this.db.prepare(itemsQuery).bind(cartId).all();
      cart.items = itemsResult.results || [];

      return cart;
    } catch (error) {
      console.error(`Error finding cart with items ${cartId}:`, error);
      return null;
    }
  }

  // Search operations
  async searchProducts(searchTerm: string, options: {
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      const { limit = 20, offset = 0 } = options;
      
      const query = `
        SELECT * FROM "product" 
        WHERE (title LIKE ? OR description LIKE ? OR handle LIKE ?) 
        AND (deleted_at IS NULL OR deleted_at = '') 
        ORDER BY title ASC 
        LIMIT ? OFFSET ?
      `;
      
      const searchPattern = `%${searchTerm}%`;
      const result = await this.db.prepare(query)
        .bind(searchPattern, searchPattern, searchPattern, limit, offset)
        .all();
      
      return result.results || [];
    } catch (error) {
      console.error(`Error searching products with term ${searchTerm}:`, error);
      return [];
    }
  }

  // Journal entries
  async findPublishedJournalEntries(options: {
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      const { limit = 10, offset = 0 } = options;
      
      const query = `
        SELECT * FROM "journal_entry" 
        WHERE published = 1 AND (deleted_at IS NULL OR deleted_at = '') 
        ORDER BY published_at DESC 
        LIMIT ? OFFSET ?
      `;
      
      const result = await this.db.prepare(query).bind(limit, offset).all();
      return result.results || [];
    } catch (error) {
      console.error('Error finding published journal entries:', error);
      return [];
    }
  }

  async findJournalEntryBySlug(slug: string) {
    try {
      const query = `SELECT * FROM "journal_entry" WHERE slug = ? AND (deleted_at IS NULL OR deleted_at = '') LIMIT 1`;
      const result = await this.db.prepare(query).bind(slug).first();
      return result || null;
    } catch (error) {
      console.error(`Error finding journal entry by slug ${slug}:`, error);
      return null;
    }
  }

  // Dashboard analytics
  async getDashboardStats() {
    try {
      const stats: any = {};

      // Get order stats
      const orderStatsQuery = `
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
          SUM(CASE WHEN status = 'canceled' THEN 1 ELSE 0 END) as canceled,
          SUM(total) as revenue
        FROM "order" 
        WHERE deleted_at IS NULL OR deleted_at = ''
      `;
      const orderStats = await this.db.prepare(orderStatsQuery).first();
      stats.orders = orderStats;

      // Get customer stats
      const customerStatsQuery = `
        SELECT COUNT(*) as total 
        FROM "customer" 
        WHERE deleted_at IS NULL OR deleted_at = ''
      `;
      const customerStats = await this.db.prepare(customerStatsQuery).first();
      stats.customers = customerStats;

      // Get recent orders
      const recentOrdersQuery = `
        SELECT * FROM "order" 
        WHERE deleted_at IS NULL OR deleted_at = '' 
        ORDER BY created_at DESC 
        LIMIT 5
      `;
      const recentOrdersResult = await this.db.prepare(recentOrdersQuery).all();
      stats.recent_orders = recentOrdersResult.results || [];

      return stats;
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      return {
        orders: { total: 0, pending: 0, completed: 0, canceled: 0, revenue: 0 },
        customers: { total: 0 },
        recent_orders: []
      };
    }
  }

  // Raw query execution for complex operations
  async executeRawQuery(query: string, params: any[] = []) {
    try {
      if (query.trim().toLowerCase().startsWith('select')) {
        const result = await this.db.prepare(query).bind(...params).all();
        return result.results || [];
      } else {
        const result = await this.db.prepare(query).bind(...params).run();
        return result;
      }
    } catch (error) {
      console.error('Error executing raw query:', error);
      throw error;
    }
  }

  // Transaction support (basic implementation)
  async transaction<T>(callback: (db: DatabaseService) => Promise<T>): Promise<T> {
    // D1 doesn't support explicit transactions yet, so we'll just execute the callback
    // In a future version, this could be enhanced with batch operations
    return await callback(this);
  }
} 