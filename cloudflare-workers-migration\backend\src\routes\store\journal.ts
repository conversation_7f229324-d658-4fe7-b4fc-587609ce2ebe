import { Hono } from 'hono';
import { WorkerEnv } from 'handmadein-shared';
import { DatabaseService } from '../../services/database';

export const journalRoutes = new Hono<{ Bindings: WorkerEnv }>();

// Get all published journal entries
journalRoutes.get('/', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;

    const entries = await db.findPublishedJournalEntries({
      limit,
      offset,
    });

    const total = await db.count('journalEntries', { published: true });

    return c.json({
      success: true,
      data: entries,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching journal entries:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch journal entries',
    }, 500);
  }
});

// Get journal entry by slug
journalRoutes.get('/:slug', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const slug = c.req.param('slug');

    const entry = await db.findJournalEntryBySlug(slug);
    if (!entry || !entry.published) {
      return c.json({
        success: false,
        error: 'Journal entry not found',
      }, 404);
    }

    return c.json({
      success: true,
      data: entry,
    });
  } catch (error) {
    console.error('Error fetching journal entry:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch journal entry',
    }, 500);
  }
});

// Get journal entries by tag
journalRoutes.get('/tag/:tag', async (c) => {
  try {
    const db = new DatabaseService(c.env);
    const tag = c.req.param('tag');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;

    // This would need a more complex query to search in JSON tags
    // For now, we'll return all published entries
    const entries = await db.findPublishedJournalEntries({
      limit,
      offset,
    });

    // Filter by tag (this is a simplified implementation)
    const filteredEntries = entries.filter(entry => {
      if (!entry.tags) return false;
      const tags = typeof entry.tags === 'string' ? JSON.parse(entry.tags) : entry.tags;
      return tags.includes(tag);
    });

    return c.json({
      success: true,
      data: filteredEntries,
      pagination: {
        page,
        limit,
        total: filteredEntries.length,
        pages: Math.ceil(filteredEntries.length / limit),
        hasNext: page * limit < filteredEntries.length,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching journal entries by tag:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch journal entries',
    }, 500);
  }
}); 