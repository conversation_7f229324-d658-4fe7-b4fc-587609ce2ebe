import { WorkerEnv } from 'handmadein-shared';

export interface TrustedShopProduct {
  name: string;
  id?: string;
}

export interface TrustedShopOptions {
  apiKey: string;
}

/**
 * Trusted Shop service for Cloudflare Workers
 * Handles integration with Compari.ro's trusted shop program
 * Based on the original PHP implementation
 */
export class TrustedShopService {
  protected readonly VERSION = "2.0/CloudflareWorkers";
  protected readonly SERVICE_URL_SEND = "https://www.compari.ro/";
  protected readonly SERVICE_URL_AKU = "https://assets.arukereso.com/aku.min.js";
  protected readonly SERVICE_TOKEN_REQUEST = "t2/TokenRequest.php";
  protected readonly SERVICE_TOKEN_PROCESS = "t2/TrustedShop.php";

  protected readonly options: TrustedShopOptions;
  protected email: string | null = null;
  protected products: TrustedShopProduct[] = [];

  constructor(env: WorkerEnv) {
    this.options = {
      apiKey: env.TRUSTED_SHOP_API_KEY || "efd6680cd35ff626650e900b92b5d463",
    };

    if (!this.options.apiKey) {
      throw new Error("WebApiKey is empty.");
    }
  }

  /**
   * Sets the customer's email address
   */
  setEmail(email: string): void {
    this.email = email;
  }

  /**
   * Adds a product to send. Can be called multiple times.
   */
  addProduct(productName: string, productId?: string): void {
    const product: TrustedShopProduct = {
      name: productName,
    };

    if (productId) {
      product.id = productId;
    }

    this.products.push(product);
  }

  /**
   * Prepares the Trusted code, which provides data sending from the customer's browser
   */
  async prepare(): Promise<string> {
    // Validate required fields
    if (!this.options.apiKey) {
      throw new Error("Partner WebApiKey is empty.");
    }

    if (!this.email) {
      throw new Error("Customer e-mail address is empty.");
    }

    if (this.email === "<EMAIL>") {
      throw new Error("Customer e-mail address has been not changed yet.");
    }

    // Check for example products
    const exampleProducts = ["Name of first purchased product", "Name of second purchased product"];
    for (const example of exampleProducts) {
      for (const product of this.products) {
        if (product.name === example) {
          throw new Error("Product name has been not changed yet.");
        }
      }
    }

    // Prepare parameters for token request
    const formattedProducts = this.products.map(product => {
      const formatted: Record<string, any> = {
        Name: product.name
      };
      if (product.id) {
        formatted.Id = product.id;
      }
      return formatted;
    });

    const params = {
      Version: this.VERSION,
      WebApiKey: this.options.apiKey,
      Email: this.email,
      Products: JSON.stringify(formattedProducts),
    };

    try {
      // Get token from the service
      const token = await this.getToken(params);
      const random = this.generateRandom();

      // Build the HTML output
      let output = '<script type="text/javascript">window.aku_request_done = function(w, c) {';
      output += `var I = new Image(); I.src="${this.SERVICE_URL_SEND}${this.SERVICE_TOKEN_PROCESS}?Token=${token}&WebApiKey=${this.options.apiKey}&C=" + c;`;
      output += '};</script>';
      
      // Include the script
      output += '<script type="text/javascript"> (function() {';
      output += `var a=document.createElement("script"); a.type="text/javascript"; a.src="${this.SERVICE_URL_AKU}"; a.async=true;`;
      output += '(document.getElementsByTagName("head")[0]||document.getElementsByTagName("body")[0]).appendChild(a);';
      output += '})();</script>';
      
      // Fallback for noscript
      output += '<noscript>';
      output += `<img src="${this.SERVICE_URL_SEND}${this.SERVICE_TOKEN_PROCESS}?Token=${token}&WebApiKey=${this.options.apiKey}&C=${random}" />`;
      output += '</noscript>';

      return output;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("Token request failed.");
    }
  }

  /**
   * Generates a random string for the fallback image URL
   */
  private generateRandom(): string {
    // Use crypto.subtle for generating random values in Cloudflare Workers
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Performs a request to get a token
   */
  private async getToken(params: Record<string, any>): Promise<string> {
    try {
      // Convert params to form-urlencoded format
      const formData = new URLSearchParams();
      for (const key in params) {
        formData.append(key, params[key]);
      }

      const response = await fetch(
        `${this.SERVICE_URL_SEND}${this.SERVICE_TOKEN_REQUEST}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formData,
          // Set a timeout using AbortController
          signal: AbortSignal.timeout(5000), // 5 second timeout
        }
      );

      if (response.ok) {
        const data = await response.json();
        return data.Token;
      } else if (response.status === 400) {
        const errorData = await response.json();
        throw new Error(`Bad request: ${errorData.ErrorCode} - ${errorData.ErrorMessage}`);
      } else {
        throw new Error("Token request failed.");
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'TimeoutError') {
        throw new Error("Token request timed out.");
      }
      throw error;
    }
  }

  /**
   * Reset the service state
   */
  reset(): void {
    this.email = null;
    this.products = [];
  }

  /**
   * Get current products
   */
  getProducts(): TrustedShopProduct[] {
    return [...this.products];
  }

  /**
   * Get current email
   */
  getEmail(): string | null {
    return this.email;
  }

  /**
   * Validate if service is ready to prepare
   */
  isReady(): boolean {
    return !!(this.email && this.products.length > 0);
  }
} 