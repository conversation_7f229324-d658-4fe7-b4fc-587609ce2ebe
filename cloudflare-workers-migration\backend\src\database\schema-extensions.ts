import { sqliteTable, text, integer, real, blob } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// Base fields for all entities
const baseFields = {
  id: text('id').primaryKey(),
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  deleted_at: text('deleted_at'),
};

// =======================
// PRODUCT MODULE EXTENSIONS
// =======================

// Product Categories
export const productCategories = sqliteTable('product_categories', {
  ...baseFields,
  name: text('name').notNull(),
  description: text('description'),
  handle: text('handle').unique().notNull(),
  is_active: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  is_internal: integer('is_internal', { mode: 'boolean' }).default(false).notNull(),
  rank: integer('rank').default(0),
  parent_category_id: text('parent_category_id'),
  metadata: text('metadata'), // JSON string
});

// Product Category Product junction table
export const productCategoryProducts = sqliteTable('product_category_products', {
  id: text('id').primaryKey(),
  product_category_id: text('product_category_id').notNull(),
  product_id: text('product_id').notNull(),
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Product Types
export const productTypes = sqliteTable('product_types', {
  ...baseFields,
  value: text('value').notNull(),
  metadata: text('metadata'), // JSON string
});

// Product Options
export const productOptions = sqliteTable('product_options', {
  ...baseFields,
  title: text('title').notNull(),
  product_id: text('product_id').notNull(),
  metadata: text('metadata'), // JSON string
});

// Product Option Values
export const productOptionValues = sqliteTable('product_option_values', {
  ...baseFields,
  value: text('value').notNull(),
  option_id: text('option_id').notNull(),
  variant_id: text('variant_id').notNull(),
  metadata: text('metadata'), // JSON string
});

// =======================
// PRICING MODULE
// =======================

// Price Sets
export const priceSets = sqliteTable('price_sets', {
  ...baseFields,
  metadata: text('metadata'), // JSON string
});

// Price Lists
export const priceLists = sqliteTable('price_lists', {
  ...baseFields,
  title: text('title').notNull(),
  description: text('description'),
  type: text('type').default('sale').notNull(), // sale, override
  status: text('status').default('draft').notNull(), // draft, active
  starts_at: text('starts_at'),
  ends_at: text('ends_at'),
  metadata: text('metadata'), // JSON string
});

// Prices
export const prices = sqliteTable('prices', {
  ...baseFields,
  currency_code: text('currency_code').notNull(),
  amount: real('amount').notNull(),
  min_quantity: integer('min_quantity'),
  max_quantity: integer('max_quantity'),
  price_set_id: text('price_set_id'),
  price_list_id: text('price_list_id'),
  metadata: text('metadata'), // JSON string
});

// Product Variant Price Set junction table
export const productVariantPriceSets = sqliteTable('product_variant_price_sets', {
  id: text('id').primaryKey(),
  variant_id: text('variant_id').notNull(),
  price_set_id: text('price_set_id').notNull(),
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// =======================
// SALES CHANNEL MODULE
// =======================

// Sales Channels
export const salesChannels = sqliteTable('sales_channels', {
  ...baseFields,
  name: text('name').notNull(),
  description: text('description'),
  is_disabled: integer('is_disabled', { mode: 'boolean' }).default(false).notNull(),
  metadata: text('metadata'), // JSON string
});

// Product Sales Channel junction table
export const productSalesChannels = sqliteTable('product_sales_channels', {
  id: text('id').primaryKey(),
  product_id: text('product_id').notNull(),
  sales_channel_id: text('sales_channel_id').notNull(),
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// =======================
// CUSTOMER MODULE EXTENSIONS
// =======================

// Customer Groups
export const customerGroups = sqliteTable('customer_groups', {
  ...baseFields,
  name: text('name').notNull(),
  metadata: text('metadata'), // JSON string
});

// Customer Group Customer junction table  
export const customerGroupCustomers = sqliteTable('customer_group_customers', {
  id: text('id').primaryKey(),
  customer_group_id: text('customer_group_id').notNull(),
  customer_id: text('customer_id').notNull(),
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// =======================
// REGION MODULE
// =======================

// Currencies
export const currencies = sqliteTable('currencies', {
  code: text('code').primaryKey(), // ISO 3 letter code
  symbol: text('symbol').notNull(),
  symbol_native: text('symbol_native').notNull(),
  name: text('name').notNull(),
  includes_tax: integer('includes_tax', { mode: 'boolean' }).default(false).notNull(),
});

// =======================
// PAYMENT MODULE EXTENSIONS
// =======================

// Payment Collections
export const paymentCollections = sqliteTable('payment_collections', {
  ...baseFields,
  currency_code: text('currency_code').notNull(),
  region_id: text('region_id').notNull(),
  amount: integer('amount').notNull(), // in cents
  metadata: text('metadata'), // JSON string
});

// =======================
// FULFILLMENT MODULE
// =======================

// Fulfillment Sets
export const fulfillmentSets = sqliteTable('fulfillment_sets', {
  ...baseFields,
  name: text('name').notNull(),
  type: text('type').notNull(),
  metadata: text('metadata'), // JSON string
});

// =======================
// INVENTORY MODULE
// =======================

// Inventory Items
export const inventoryItems = sqliteTable('inventory_items', {
  ...baseFields,
  sku: text('sku'),
  origin_country: text('origin_country'),
  hs_code: text('hs_code'),
  mid_code: text('mid_code'),
  material: text('material'),
  weight: real('weight'),
  length: real('length'),
  height: real('height'),
  width: real('width'),
  requires_shipping: integer('requires_shipping', { mode: 'boolean' }).default(true).notNull(),
  metadata: text('metadata'), // JSON string
});

// Stock Locations
export const stockLocations = sqliteTable('stock_locations', {
  ...baseFields,
  name: text('name').notNull(),
  address_id: text('address_id'),
  metadata: text('metadata'), // JSON string
});

// Inventory Levels
export const inventoryLevels = sqliteTable('inventory_levels', {
  ...baseFields,
  inventory_item_id: text('inventory_item_id').notNull(),
  location_id: text('location_id').notNull(),
  stocked_quantity: integer('stocked_quantity').default(0).notNull(),
  reserved_quantity: integer('reserved_quantity').default(0).notNull(),
  incoming_quantity: integer('incoming_quantity').default(0).notNull(),
  metadata: text('metadata'), // JSON string
});

// Reservation Items
export const reservationItems = sqliteTable('reservation_items', {
  ...baseFields,
  inventory_item_id: text('inventory_item_id').notNull(),
  location_id: text('location_id').notNull(),
  line_item_id: text('line_item_id'),
  quantity: integer('quantity').notNull(),
  description: text('description'),
  metadata: text('metadata'), // JSON string
});

// =======================
// TAX MODULE
// =======================

// Tax Rates
export const taxRates = sqliteTable('tax_rates', {
  ...baseFields,
  rate: real('rate'),
  code: text('code'),
  name: text('name').notNull(),
  region_id: text('region_id').notNull(),
  metadata: text('metadata'), // JSON string
});

// Tax Regions
export const taxRegions = sqliteTable('tax_regions', {
  ...baseFields,
  rate: real('rate').notNull(),
  code: text('code'),
  tax_rate_id: text('tax_rate_id'),
  metadata: text('metadata'), // JSON string
});

// =======================
// PROMOTION MODULE
// =======================

// Promotions
export const promotions = sqliteTable('promotions', {
  ...baseFields,
  code: text('code').unique(),
  type: text('type').notNull(), // standard, buyget
  is_automatic: integer('is_automatic', { mode: 'boolean' }).default(false).notNull(),
  campaign_id: text('campaign_id'),
  metadata: text('metadata'), // JSON string
});

// Promotion Rules
export const promotionRules = sqliteTable('promotion_rules', {
  ...baseFields,
  promotion_id: text('promotion_id').notNull(),
  operator: text('operator').notNull(), // gte, lte, gt, lt, eq, ne, in, nin
  attribute: text('attribute').notNull(),
  values: text('values'), // JSON string
  metadata: text('metadata'), // JSON string
});

// =======================
// USER MODULE
// =======================

// Users
export const users = sqliteTable('users', {
  ...baseFields,
  email: text('email').unique().notNull(),
  first_name: text('first_name'),
  last_name: text('last_name'),
  avatar_url: text('avatar_url'),
  metadata: text('metadata'), // JSON string
});

// =======================
// AUTH MODULE
// =======================

// Auth Identities
export const authIdentities = sqliteTable('auth_identities', {
  id: text('id').primaryKey(),
  provider_id: text('provider_id').notNull(),
  entity_id: text('entity_id').notNull(),
  user_metadata: text('user_metadata'), // JSON string
  provider_metadata: text('provider_metadata'), // JSON string
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Invites
export const invites = sqliteTable('invites', {
  ...baseFields,
  user_email: text('user_email').notNull(),
  role: text('role'),
  accepted: integer('accepted', { mode: 'boolean' }).default(false).notNull(),
  token: text('token').notNull(),
  expires_at: text('expires_at').notNull(),
  metadata: text('metadata'), // JSON string
});

// =======================
// FILE MODULE
// =======================

// Files
export const files = sqliteTable('files', {
  ...baseFields,
  url: text('url').notNull(),
  metadata: text('metadata'), // JSON string
});

// =======================
// NOTIFICATION MODULE
// =======================

// Notifications
export const notifications = sqliteTable('notifications', {
  id: text('id').primaryKey(),
  to: text('to').notNull(),
  channel: text('channel').notNull(),
  template: text('template'),
  data: text('data'), // JSON string
  trigger_type: text('trigger_type'),
  resource_type: text('resource_type'),
  resource_id: text('resource_id'),
  provider_id: text('provider_id'),
  idempotency_key: text('idempotency_key'),
  external_id: text('external_id'),
  metadata: text('metadata'), // JSON string
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// =======================
// WORKFLOW MODULE
// =======================

// Workflow Executions
export const workflowExecutions = sqliteTable('workflow_executions', {
  ...baseFields,
  workflow_id: text('workflow_id').notNull(),
  transaction_id: text('transaction_id'),
  execution: text('execution'), // JSON string
  context: text('context'), // JSON string
  state: text('state'),
});

// =======================
// ADDITIONAL ECOMMERCE TABLES
// =======================

// Returns
export const returns = sqliteTable('returns', {
  ...baseFields,
  status: text('status').default('requested').notNull(), // requested, received, requires_action, canceled
  order_id: text('order_id').notNull(),
  swap_id: text('swap_id'),
  claim_order_id: text('claim_order_id'),
  shipping_data: text('shipping_data'), // JSON string
  refund_amount: integer('refund_amount').notNull(),
  received_at: text('received_at'),
  metadata: text('metadata'), // JSON string
});

// Refunds
export const refunds = sqliteTable('refunds', {
  ...baseFields,
  order_id: text('order_id'),
  payment_id: text('payment_id').notNull(),
  amount: integer('amount').notNull(), // in cents
  note: text('note'),
  reason: text('reason').notNull(), // discount, return, swap, claim, other
  metadata: text('metadata'), // JSON string
  idempotency_key: text('idempotency_key'),
});

// Swaps
export const swaps = sqliteTable('swaps', {
  ...baseFields,
  fulfillment_status: text('fulfillment_status').notNull(),
  payment_status: text('payment_status').notNull(),
  order_id: text('order_id').notNull(),
  difference_due: integer('difference_due'),
  shipping_address_id: text('shipping_address_id'),
  cart_id: text('cart_id'),
  confirmed_at: text('confirmed_at'),
  canceled_at: text('canceled_at'),
  no_notification: integer('no_notification', { mode: 'boolean' }),
  allow_backorder: integer('allow_backorder', { mode: 'boolean' }).default(false),
  idempotency_key: text('idempotency_key'),
  metadata: text('metadata'), // JSON string
});

// Claims
export const claims = sqliteTable('claims', {
  ...baseFields,
  payment_status: text('payment_status').default('na').notNull(),
  fulfillment_status: text('fulfillment_status').default('not_fulfilled').notNull(),
  type: text('type').notNull(), // refund, replace
  order_id: text('order_id').notNull(),
  shipping_address_id: text('shipping_address_id'),
  refund_amount: integer('refund_amount'),
  canceled_at: text('canceled_at'),
  no_notification: integer('no_notification', { mode: 'boolean' }),
  idempotency_key: text('idempotency_key'),
  metadata: text('metadata'), // JSON string
});

// Discount Conditions
export const discountConditions = sqliteTable('discount_conditions', {
  ...baseFields,
  type: text('type').notNull(),
  operator: text('operator').notNull(),
  discount_rule_id: text('discount_rule_id').notNull(),
  metadata: text('metadata'), // JSON string
});

// Draft Orders
export const draftOrders = sqliteTable('draft_orders', {
  ...baseFields,
  status: text('status').default('open').notNull(), // open, completed
  display_id: integer('display_id').notNull(),
  cart_id: text('cart_id'),
  order_id: text('order_id'),
  canceled_at: text('canceled_at'),
  completed_at: text('completed_at'),
  no_notification_order: integer('no_notification_order', { mode: 'boolean' }),
  idempotency_key: text('idempotency_key'),
  metadata: text('metadata'), // JSON string
});

// Gift Cards
export const giftCards = sqliteTable('gift_cards', {
  ...baseFields,
  code: text('code').unique().notNull(),
  value: integer('value').notNull(),
  balance: integer('balance').notNull(),
  region_id: text('region_id').notNull(),
  order_id: text('order_id'),
  is_disabled: integer('is_disabled', { mode: 'boolean' }).default(false).notNull(),
  ends_at: text('ends_at'),
  tax_rate: real('tax_rate'),
  metadata: text('metadata'), // JSON string
});

// Gift Card Transactions
export const giftCardTransactions = sqliteTable('gift_card_transactions', {
  ...baseFields,
  gift_card_id: text('gift_card_id').notNull(),
  order_id: text('order_id').notNull(),
  amount: integer('amount').notNull(),
  is_taxable: integer('is_taxable', { mode: 'boolean' }),
  tax_rate: real('tax_rate'),
});

// Custom Shipping Options
export const customShippingOptions = sqliteTable('custom_shipping_options', {
  id: text('id').primaryKey(),
  shipping_option_id: text('shipping_option_id').notNull(),
  cart_id: text('cart_id'),
  price: integer('price').notNull(),
  metadata: text('metadata'), // JSON string
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Shipping Profiles
export const shippingProfiles = sqliteTable('shipping_profiles', {
  ...baseFields,
  name: text('name').notNull(),
  type: text('type').notNull(), // default, gift_card, custom
  metadata: text('metadata'), // JSON string
});

// Product Shipping Profile junction table
export const productShippingProfiles = sqliteTable('product_shipping_profiles', {
  product_id: text('product_id').notNull(),
  profile_id: text('profile_id').notNull(),
});

// Shipping Option Requirements
export const shippingOptionRequirements = sqliteTable('shipping_option_requirements', {
  id: text('id').primaryKey(),
  shipping_option_id: text('shipping_option_id').notNull(),
  type: text('type').notNull(), // min_subtotal, max_subtotal
  amount: integer('amount').notNull(),
  created_at: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  deleted_at: text('deleted_at'),
}); 