import { Hono } from 'hono';
import { updateProductShippingProfiles, dryRunProductShippingProfiles } from '../../../scripts/update-product-shipping-profiles';
import { restoreShippingProfile, restoreShippingOption, restoreShippingEntities } from '../../../scripts/restore-shipping-profile';
import { fixShippingOptionRules, dryRunFixShippingOptionRules } from '../../../scripts/fix-shipping-option-rules';

interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

interface AdminUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

type Variables = {
  user: AdminUser;
  userId: string;
};

const app = new Hono<{ Bindings: WorkerEnv; Variables: Variables }>();

// POST /admin/product-scripts/update-shipping-profiles - Run shipping profile update script
app.post('/update-shipping-profiles', async (c) => {
  try {
    const body = await c.req.json().catch(() => ({}));
    const dryRun = body.dry_run === true;

    if (dryRun) {
      console.log('Running shipping profile update script in DRY RUN mode');
      const result = await dryRunProductShippingProfiles(c.env);
      
      return c.json({
        success: true,
        message: 'Dry run completed successfully',
        data: result,
      });
    } else {
      console.log('Running shipping profile update script in LIVE mode');
      const result = await updateProductShippingProfiles(c.env);
      
      return c.json({
        success: true,
        message: 'Script completed successfully',
        data: result,
      });
    }

  } catch (error) {
    console.error('Error running shipping profile update script:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to run script',
    }, 500);
  }
});

// GET /admin/product-scripts/update-shipping-profiles/status - Get script configuration
app.get('/update-shipping-profiles/status', async (c) => {
  try {
    const scriptModule = await import('../../../scripts/update-product-shipping-profiles');
    const { TARGET_PRODUCT_TYPE, NEW_SHIPPING_PROFILE } = scriptModule.default;
    
    return c.json({
      success: true,
      data: {
        target_product_type: TARGET_PRODUCT_TYPE,
        new_shipping_profile: NEW_SHIPPING_PROFILE,
        description: 'Updates products with specified product_type to use new shipping_profile',
        endpoints: {
          dry_run: 'POST /admin/product-scripts/update-shipping-profiles with {"dry_run": true}',
          live_run: 'POST /admin/product-scripts/update-shipping-profiles with {"dry_run": false}',
        }
      },
    });

  } catch (error) {
    console.error('Error getting script status:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get script status',
    }, 500);
  }
});

// POST /admin/product-scripts/restore-shipping-profile - Restore deleted shipping profile
app.post('/restore-shipping-profile', async (c) => {
  try {
    console.log('Running shipping profile restore script');
    const result = await restoreShippingProfile(c.env);
    
    return c.json({
      success: true,
      message: result.message,
      data: result,
    });

  } catch (error) {
    console.error('Error running shipping profile restore script:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to restore shipping profile',
    }, 500);
  }
});

// POST /admin/product-scripts/restore-shipping-option - Restore deleted shipping option
app.post('/restore-shipping-option', async (c) => {
  try {
    console.log('Running shipping option restore script');
    const result = await restoreShippingOption(c.env);
    
    return c.json({
      success: true,
      message: result.message,
      data: result,
    });

  } catch (error) {
    console.error('Error running shipping option restore script:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to restore shipping option',
    }, 500);
  }
});

// POST /admin/product-scripts/restore-all-shipping - Restore both profile and option
app.post('/restore-all-shipping', async (c) => {
  try {
    console.log('Running restore script for all shipping entities');
    const result = await restoreShippingEntities(c.env);
    
    return c.json({
      success: true,
      message: result.message,
      data: result,
    });

  } catch (error) {
    console.error('Error running shipping entities restore script:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to restore shipping entities',
    }, 500);
  }
});

// GET /admin/product-scripts/debug-shipping/:productIds - Debug shipping options for specific products
app.get('/debug-shipping/:productIds', async (c) => {
  try {
    const productIds = c.req.param('productIds').split(',');
    console.log(`Debugging shipping options for products: ${productIds.join(', ')}`);
    
    const debugData: {
      products: any[];
      shippingProfiles: any[];
      shippingOptions: any[];
      serviceZones: any[];
      geoZones: any[];
      summary: {
        totalProducts: number;
        productsWithProfiles: number;
        availableShippingOptions: number;
        issues: string[];
      };
    } = {
      products: [],
      shippingProfiles: [],
      shippingOptions: [],
      serviceZones: [],
      geoZones: [],
      summary: {
        totalProducts: productIds.length,
        productsWithProfiles: 0,
        availableShippingOptions: 0,
        issues: []
      }
    };

    // Check each product
    for (const productId of productIds) {
      try {
        // Get product details
        const product = await c.env.DB.prepare(`
          SELECT * FROM product WHERE id = ? AND (deleted_at IS NULL OR deleted_at = '')
        `).bind(productId.trim()).first();

        if (!product) {
          debugData.summary.issues.push(`Product ${productId} not found`);
          continue;
        }

        // Get product shipping profiles
        const productProfiles = await c.env.DB.prepare(`
          SELECT psp.*, sp.name as profile_name, sp.type as profile_type, sp.deleted_at as profile_deleted_at
          FROM product_shipping_profile psp
          JOIN shipping_profile sp ON psp.shipping_profile_id = sp.id
          WHERE psp.product_id = ? AND (psp.deleted_at IS NULL OR psp.deleted_at = '')
        `).bind(productId.trim()).all();

        const activeProfiles = productProfiles.results?.filter((p: any) => !p.profile_deleted_at) || [];

        debugData.products.push({
          id: productId.trim(),
          title: product.title,
          type_id: product.type_id,
          profiles: productProfiles.results || [],
          activeProfiles: activeProfiles,
          hasActiveProfiles: activeProfiles.length > 0
        });

        if (activeProfiles.length > 0) {
          debugData.summary.productsWithProfiles++;
        } else {
          debugData.summary.issues.push(`Product ${productId} has no active shipping profiles`);
        }

        // Collect unique shipping profiles
        for (const profile of activeProfiles) {
          if (!debugData.shippingProfiles.find((p: any) => p.id === profile.shipping_profile_id)) {
            debugData.shippingProfiles.push({
              id: profile.shipping_profile_id,
              name: profile.profile_name,
              type: profile.profile_type,
              deleted_at: profile.profile_deleted_at
            });
          }
        }

      } catch (error) {
        debugData.summary.issues.push(`Error processing product ${productId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Get shipping options for the collected profiles
    if (debugData.shippingProfiles.length > 0) {
      const profileIds = debugData.shippingProfiles.map((p: any) => p.id);
      const placeholders = profileIds.map(() => '?').join(',');
      
      const shippingOptionsQuery = `
        SELECT DISTINCT so.*, 
               sp.name as profile_name,
               sz.name as service_zone_name,
               EXISTS(
                 SELECT 1 FROM shipping_option_rule sor1 
                 WHERE sor1.shipping_option_id = so.id 
                   AND sor1.attribute = 'is_return' 
                   AND sor1.value = 'false'
               ) as has_is_return_false,
               EXISTS(
                 SELECT 1 FROM shipping_option_rule sor2 
                 WHERE sor2.shipping_option_id = so.id 
                   AND sor2.attribute = 'enabled_in_store' 
                   AND sor2.value = 'true'
               ) as has_enabled_in_store_true
        FROM shipping_option so
        JOIN shipping_profile sp ON so.shipping_profile_id = sp.id
        LEFT JOIN service_zone sz ON so.service_zone_id = sz.id
        WHERE so.shipping_profile_id IN (${placeholders})
          AND (so.deleted_at IS NULL OR so.deleted_at = '')
          AND (sp.deleted_at IS NULL OR sp.deleted_at = '')
      `;

      const shippingOptionsResult = await c.env.DB.prepare(shippingOptionsQuery).bind(...profileIds).all();
      
      // Filter shipping options with proper rules
      const validShippingOptions = (shippingOptionsResult.results || []).filter((option: any) => {
        return option.has_is_return_false && option.has_enabled_in_store_true;
      });

      debugData.shippingOptions = shippingOptionsResult.results || [];
      debugData.summary.availableShippingOptions = validShippingOptions.length;

      if (validShippingOptions.length === 0) {
        debugData.summary.issues.push('No valid shipping options found (missing proper rules)');
      }

      // Get service zones
      const serviceZoneIds = [...new Set(debugData.shippingOptions.map((so: any) => so.service_zone_id).filter(Boolean))];
      if (serviceZoneIds.length > 0) {
        const serviceZonesQuery = `
          SELECT sz.*, COUNT(DISTINCT gz.id) as geo_zone_count
          FROM service_zone sz
          LEFT JOIN geo_zone gz ON sz.id = gz.service_zone_id AND (gz.deleted_at IS NULL OR gz.deleted_at = '')
          WHERE sz.id IN (${serviceZoneIds.map(() => '?').join(',')})
            AND (sz.deleted_at IS NULL OR sz.deleted_at = '')
          GROUP BY sz.id
        `;
        const serviceZonesResult = await c.env.DB.prepare(serviceZonesQuery).bind(...serviceZoneIds).all();
        debugData.serviceZones = serviceZonesResult.results || [];
      }

      // Get geo zones for service zones
      if (serviceZoneIds.length > 0) {
        const geoZonesQuery = `
          SELECT gz.*, sz.name as service_zone_name
          FROM geo_zone gz
          JOIN service_zone sz ON gz.service_zone_id = sz.id
          WHERE gz.service_zone_id IN (${serviceZoneIds.map(() => '?').join(',')})
            AND (gz.deleted_at IS NULL OR gz.deleted_at = '')
        `;
        const geoZonesResult = await c.env.DB.prepare(geoZonesQuery).bind(...serviceZoneIds).all();
        debugData.geoZones = geoZonesResult.results || [];
      }
    } else {
      debugData.summary.issues.push('No shipping profiles found for any product');
    }

    return c.json({
      success: true,
      data: debugData,
    });

  } catch (error) {
    console.error('Error debugging shipping options:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to debug shipping options',
    }, 500);
  }
});

// GET /admin/product-scripts/debug-cart-shipping/:cartId - Debug cart shipping options logic step by step
app.get('/debug-cart-shipping/:cartId', async (c) => {
  try {
    const cartId = c.req.param('cartId');
    console.log(`Debugging cart shipping logic for: ${cartId}`);
    
    const debugData: any = {
      cart: null,
      cartItems: [],
      shippingProfiles: [],
      shippingAddress: null,
      geoZones: [],
      serviceZones: [],
      shippingOptionsBeforeFiltering: [],
      shippingOptionsAfterFiltering: [],
      filteringSteps: [],
      issues: []
    };

    // Step 1: Get cart
    const cart = await c.env.DB.prepare(`SELECT * FROM cart WHERE id = ?`).bind(cartId).first();
    if (!cart || cart.deleted_at) {
      return c.json({
        success: false,
        error: 'Cart not found'
      }, 404);
    }
    debugData.cart = cart;
    debugData.filteringSteps.push('✅ Cart found');

    // Step 2: Get cart items
    const cartItems = await c.env.DB.prepare(`SELECT * FROM cart_line_item WHERE cart_id = ?`).bind(cartId).all();
    debugData.cartItems = cartItems.results || [];
    debugData.filteringSteps.push(`✅ Found ${cartItems.results?.length || 0} cart items`);

    if (cartItems.results?.length === 0) {
      debugData.issues.push('Cart has no items');
      return c.json({ success: true, data: debugData });
    }

    // Step 3: Get shipping profiles for cart items
    const shippingProfileIds = new Set<string>();
    for (const item of cartItems.results || []) {
      if (item.variant_id) {
        const variant = await c.env.DB.prepare(`SELECT * FROM product_variant WHERE id = ?`).bind(item.variant_id).first();
        if (variant && variant.product_id) {
          const productProfiles = await c.env.DB.prepare(`
            SELECT psp.*, sp.name as profile_name
            FROM product_shipping_profile psp
            JOIN shipping_profile sp ON psp.shipping_profile_id = sp.id
            WHERE psp.product_id = ? AND (psp.deleted_at IS NULL OR psp.deleted_at = '')
            AND (sp.deleted_at IS NULL OR sp.deleted_at = '')
          `).bind(variant.product_id).all();

          for (const profile of productProfiles.results || []) {
            shippingProfileIds.add(profile.shipping_profile_id);
            debugData.shippingProfiles.push({
              product_id: variant.product_id,
              shipping_profile_id: profile.shipping_profile_id,
              profile_name: profile.profile_name
            });
          }
        }
      }
    }

    const uniqueShippingProfiles = Array.from(shippingProfileIds);
    debugData.filteringSteps.push(`✅ Found ${uniqueShippingProfiles.length} unique shipping profiles: ${uniqueShippingProfiles.join(', ')}`);

    if (uniqueShippingProfiles.length === 0) {
      debugData.issues.push('No shipping profiles found for cart items');
      return c.json({ success: true, data: debugData });
    }

    // Step 4: Get shipping address and geo zones
    if (cart.shipping_address_id) {
      const shippingAddress = await c.env.DB.prepare(`SELECT * FROM address WHERE id = ?`).bind(cart.shipping_address_id).first();
      debugData.shippingAddress = shippingAddress;
      debugData.filteringSteps.push(`✅ Found shipping address: ${shippingAddress?.city}, ${shippingAddress?.country_code}`);

      if (shippingAddress?.country_code) {
        const geoZones = await c.env.DB.prepare(`
          SELECT gz.*, sz.id as service_zone_id, sz.name as service_zone_name
          FROM geo_zone gz
          JOIN service_zone sz ON gz.service_zone_id = sz.id
          WHERE gz.country_code = ? AND (gz.deleted_at IS NULL OR gz.deleted_at = '')
          AND (sz.deleted_at IS NULL OR sz.deleted_at = '')
        `).bind(shippingAddress.country_code).all();

        debugData.geoZones = geoZones.results || [];
        const uniqueServiceZoneIds = [...new Set(debugData.geoZones.map((gz: any) => gz.service_zone_id))];
        debugData.serviceZones = uniqueServiceZoneIds;
        debugData.filteringSteps.push(`✅ Found ${debugData.geoZones.length} geo zones covering ${uniqueServiceZoneIds.length} service zones: ${uniqueServiceZoneIds.join(', ')}`);
      }
    } else {
      debugData.filteringSteps.push('⚠️ No shipping address set on cart');
    }

    // Step 5: Get all shipping options that support the shipping profiles (before service zone filtering)
    let baseQuery = `
      SELECT DISTINCT so.*, sp.name as profile_name
      FROM shipping_option so
      JOIN shipping_profile sp ON so.shipping_profile_id = sp.id
      WHERE so.deleted_at IS NULL
      AND EXISTS (
        SELECT 1 FROM shipping_option_rule sor1 
        WHERE sor1.shipping_option_id = so.id 
        AND sor1.attribute = 'is_return' 
        AND sor1.value = '"false"'
        AND (sor1.deleted_at IS NULL OR sor1.deleted_at = '')
      )
      AND EXISTS (
        SELECT 1 FROM shipping_option_rule sor2 
        WHERE sor2.shipping_option_id = so.id 
        AND sor2.attribute = 'enabled_in_store' 
        AND sor2.value = '"true"'
        AND (sor2.deleted_at IS NULL OR sor2.deleted_at = '')
      )
    `;

    // Add shipping profile constraints
    for (const profileId of uniqueShippingProfiles) {
      baseQuery += `
        AND so.shipping_profile_id = '${profileId}'
      `;
    }

    const optionsBeforeServiceZone = await c.env.DB.prepare(baseQuery).all();
    debugData.shippingOptionsBeforeFiltering = optionsBeforeServiceZone.results || [];
    debugData.filteringSteps.push(`✅ Found ${debugData.shippingOptionsBeforeFiltering.length} shipping options before service zone filtering`);

    // Step 6: Apply service zone filtering
    if (debugData.serviceZones.length > 0) {
      const serviceZoneFilteredQuery = baseQuery + ` AND so.service_zone_id IN (${debugData.serviceZones.map((id: string) => `'${id}'`).join(',')})`;
      const optionsAfterServiceZone = await c.env.DB.prepare(serviceZoneFilteredQuery).all();
      debugData.shippingOptionsAfterFiltering = optionsAfterServiceZone.results || [];
      debugData.filteringSteps.push(`✅ Found ${debugData.shippingOptionsAfterFiltering.length} shipping options after service zone filtering`);
    } else {
      debugData.shippingOptionsAfterFiltering = debugData.shippingOptionsBeforeFiltering;
      debugData.filteringSteps.push('⚠️ No service zones found, using all options');
    }

    // Step 7: Analysis
    if (debugData.shippingOptionsAfterFiltering.length === 0 && debugData.shippingOptionsBeforeFiltering.length > 0) {
      debugData.issues.push('Shipping options were filtered out by service zone restrictions');
      debugData.issues.push(`Available service zones: ${debugData.serviceZones.join(', ')}`);
      debugData.shippingOptionsBeforeFiltering.forEach((option: any) => {
        debugData.issues.push(`Option ${option.id} (${option.name}) requires service zone: ${option.service_zone_id}`);
      });
    }

    return c.json({
      success: true,
      data: debugData,
    });

  } catch (error) {
    console.error('Error debugging cart shipping logic:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to debug cart shipping logic',
    }, 500);
  }
});

// GET /admin/product-scripts/debug-all-rules - Show all shipping option rules for debugging
app.get('/debug-all-rules', async (c) => {
  try {
    console.log('Debugging all shipping option rules...');
    
    // Get all shipping option rules
    const allRules = await c.env.DB.prepare(`
      SELECT sor.*, so.name as shipping_option_name, so.deleted_at as option_deleted_at
      FROM shipping_option_rule sor
      LEFT JOIN shipping_option so ON sor.shipping_option_id = so.id
      ORDER BY sor.shipping_option_id, sor.attribute
    `).all();
    
    // Get specific rules for our target shipping option
    const targetRules = await c.env.DB.prepare(`
      SELECT * FROM shipping_option_rule 
      WHERE shipping_option_id = 'so_01JTXBA1J91ZM38P55CM6MQM2F'
      ORDER BY attribute
    `).all();
    
    // Get the shipping option details
    const shippingOption = await c.env.DB.prepare(`
      SELECT * FROM shipping_option 
      WHERE id = 'so_01JTXBA1J91ZM38P55CM6MQM2F'
    `).first();
    
    return c.json({
      success: true,
      data: {
        target_shipping_option: shippingOption,
        target_rules: targetRules.results || [],
        target_rules_count: targetRules.results?.length || 0,
        all_rules: allRules.results || [],
        all_rules_count: allRules.results?.length || 0,
        analysis: {
          target_has_is_return_false: (targetRules.results || []).some((r: any) => 
            r.attribute === 'is_return' && 
            r.value === '"false"' && 
            (!r.deleted_at || r.deleted_at === '')
          ),
          target_has_enabled_in_store_true: (targetRules.results || []).some((r: any) => 
            r.attribute === 'enabled_in_store' && 
            r.value === '"true"' && 
            (!r.deleted_at || r.deleted_at === '')
          )
        }
      },
    });

  } catch (error) {
    console.error('Error debugging shipping option rules:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to debug rules',
    }, 500);
  }
});

// POST /admin/product-scripts/fix-shipping-rules - Fix shipping option rules
app.post('/fix-shipping-rules', async (c) => {
  try {
    const body = await c.req.json().catch(() => ({}));
    const dryRun = body.dry_run === true;

    if (dryRun) {
      console.log('Running shipping rules fix in DRY RUN mode');
      const result = await dryRunFixShippingOptionRules(c.env);
      
      return c.json({
        success: true,
        message: 'Dry run completed successfully',
        data: result,
      });
    } else {
      console.log('Running shipping rules fix in LIVE mode');
      const result = await fixShippingOptionRules(c.env);
      
      return c.json({
        success: true,
        message: 'Rules fixed successfully',
        data: result,
      });
    }

  } catch (error) {
    console.error('Error fixing shipping option rules:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fix shipping rules',
    }, 500);
  }
});

export { app as productScriptRoutes }; 