import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api, endpoints } from '../lib/api';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  MagnifyingGlassIcon,
  TagIcon
} from '@heroicons/react/24/outline';

interface ProductType {
  id: string;
  value: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  products_count?: number;
}

interface ProductTypeFormData {
  value: string;
  metadata?: any;
}

const ProductTypes: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingType, setEditingType] = useState<ProductType | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<Set<string>>(new Set());

  const queryClient = useQueryClient();

  // Fetch product types
  const { data: typesResponse, isLoading, error } = useQuery({
    queryKey: ['product-types'],
    queryFn: async () => {
      const response = await api.get(endpoints.productTypes.list);
      return response.data;
    },
  });

  const productTypes = typesResponse?.data || [];

  // Filter types based on search
  const filteredTypes = searchTerm 
    ? productTypes.filter((type: ProductType) => 
        type.value.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : productTypes;

  // Create product type mutation
  const createTypeMutation = useMutation({
    mutationFn: async (typeData: ProductTypeFormData) => {
      const response = await api.post(endpoints.productTypes.create, typeData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-types'] });
      setShowForm(false);
      setEditingType(null);
    },
  });

  // Update product type mutation
  const updateTypeMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: ProductTypeFormData }) => {
      const response = await api.put(endpoints.productTypes.update(id), data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-types'] });
      setShowForm(false);
      setEditingType(null);
    },
  });

  // Delete product type mutation
  const deleteTypeMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await api.delete(endpoints.productTypes.delete(id));
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-types'] });
      setSelectedTypes(new Set());
    },
  });

  const handleEdit = (type: ProductType) => {
    setEditingType(type);
    setShowForm(true);
  };

  const handleDelete = (type: ProductType) => {
    if (window.confirm(`Are you sure you want to delete "${type.value}"? This action cannot be undone.`)) {
      deleteTypeMutation.mutate(type.id);
    }
  };

  const handleBulkDelete = () => {
    if (selectedTypes.size === 0) return;
    
    if (window.confirm(`Are you sure you want to delete ${selectedTypes.size} product type(s)? This action cannot be undone.`)) {
      Promise.all(
        Array.from(selectedTypes).map(id => deleteTypeMutation.mutateAsync(id))
      ).then(() => {
        setSelectedTypes(new Set());
      });
    }
  };

  const handleSelectAll = () => {
    if (selectedTypes.size === filteredTypes.length) {
      setSelectedTypes(new Set());
    } else {
      setSelectedTypes(new Set(filteredTypes.map((type: ProductType) => type.id)));
    }
  };

  const handleSelectType = (typeId: string) => {
    const newSelected = new Set(selectedTypes);
    if (newSelected.has(typeId)) {
      newSelected.delete(typeId);
    } else {
      newSelected.add(typeId);
    }
    setSelectedTypes(newSelected);
  };

  const ProductTypeForm: React.FC = () => {
    const [formData, setFormData] = useState<ProductTypeFormData>({
      value: editingType?.value || '',
      metadata: editingType?.metadata || {},
    });

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      
      if (editingType) {
        updateTypeMutation.mutate({ id: editingType.id, data: formData });
      } else {
        createTypeMutation.mutate(formData);
      }
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-md">
          <h3 className="text-lg font-medium mb-4">
            {editingType ? 'Edit Product Type' : 'Create New Product Type'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Value *
              </label>
              <input
                type="text"
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Electronics, Clothing, Books"
                required
              />
              <p className="mt-1 text-xs text-gray-500">
                The display name for this product type
              </p>
            </div>
            
            <div className="flex justify-end space-x-2 pt-4">
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setEditingType(null);
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createTypeMutation.isPending || updateTypeMutation.isPending}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {editingType ? 'Update' : 'Create'}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-64 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-96 mb-8"></div>
          <div className="bg-white shadow rounded-lg p-6">
            <div className="space-y-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-100 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800">Error loading product types. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Product Types
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Categorize your products by type to organize your inventory and improve navigation.
            </p>
          </div>
          <button
            onClick={() => {
              setEditingType(null);
              setShowForm(true);
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Create Product Type</span>
          </button>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search product types..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              {selectedTypes.size > 0 && (
                <button
                  onClick={handleBulkDelete}
                  className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm flex items-center space-x-1"
                >
                  <TrashIcon className="h-4 w-4" />
                  <span>Delete ({selectedTypes.size})</span>
                </button>
              )}
            </div>
            <div className="text-sm text-gray-500">
              {filteredTypes.length} product type{filteredTypes.length !== 1 ? 's' : ''}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {filteredTypes.length === 0 ? (
            <div className="text-center py-12">
              <TagIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {searchTerm ? 'No product types found' : 'No product types'}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm 
                  ? 'Try adjusting your search to find what you\'re looking for.'
                  : 'Get started by creating a new product type.'
                }
              </p>
              {!searchTerm && (
                <div className="mt-6">
                  <button
                    onClick={() => {
                      setEditingType(null);
                      setShowForm(true);
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 mx-auto"
                  >
                    <PlusIcon className="h-5 w-5" />
                    <span>Create Product Type</span>
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {/* Bulk actions header */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedTypes.size === filteredTypes.length && filteredTypes.length > 0}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300"
                />
                <label className="ml-3 text-sm text-gray-900">
                  Select all
                </label>
              </div>

              {/* Product types list */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTypes.map((type: ProductType) => (
                  <div
                    key={type.id}
                    className={`p-4 border rounded-lg hover:shadow-md transition-shadow ${
                      selectedTypes.has(type.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <input
                          type="checkbox"
                          checked={selectedTypes.has(type.id)}
                          onChange={() => handleSelectType(type.id)}
                          className="mt-1 h-4 w-4 text-blue-600 rounded border-gray-300"
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <TagIcon className="h-5 w-5 text-gray-400" />
                            <h3 className="text-sm font-medium text-gray-900">
                              {type.value}
                            </h3>
                          </div>
                          <p className="mt-1 text-xs text-gray-500">
                            {type.products_count || 0} product{(type.products_count || 0) !== 1 ? 's' : ''}
                          </p>
                          <p className="mt-1 text-xs text-gray-400">
                            Created {new Date(type.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handleEdit(type)}
                          className="p-1 text-gray-400 hover:text-blue-600"
                          title="Edit"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(type)}
                          className="p-1 text-gray-400 hover:text-red-600"
                          title="Delete"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {showForm && <ProductTypeForm />}
    </div>
  );
};

export default ProductTypes; 