interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

export interface CollectionImage {
  id: string;
  collection_id: string;
  image_url: string;
  alt_text?: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export class CollectionImagesService {
  private env: WorkerEnv;

  constructor(env: WorkerEnv) {
    this.env = env;
  }

  /**
   * Get all images for a collection
   */
  async getCollectionImages(collectionId: string): Promise<CollectionImage[]> {
    const query = `
      SELECT * FROM collection_images 
      WHERE collection_id = ? 
      ORDER BY sort_order ASC, created_at ASC
    `;
    
    const results = await this.env.DB.prepare(query).bind(collectionId).all();
    
    return results.results.map((image: any) => ({
      id: image.id,
      collection_id: image.collection_id,
      image_url: image.image_url,
      alt_text: image.alt_text,
      sort_order: image.sort_order || 0,
      created_at: image.created_at,
      updated_at: image.updated_at,
    }));
  }

  /**
   * Get a single collection image by ID
   */
  async getCollectionImage(imageId: string): Promise<CollectionImage | null> {
    const query = `SELECT * FROM collection_images WHERE id = ? LIMIT 1`;
    const result = await this.env.DB.prepare(query).bind(imageId).first();
    
    if (!result) return null;

    return {
      id: result.id,
      collection_id: result.collection_id,
      image_url: result.image_url,
      alt_text: result.alt_text,
      sort_order: result.sort_order || 0,
      created_at: result.created_at,
      updated_at: result.updated_at,
    };
  }

  /**
   * Add an image to a collection
   */
  async addCollectionImage(data: {
    collection_id: string;
    image_url: string;
    alt_text?: string;
    sort_order?: number;
  }): Promise<CollectionImage> {
    const imageId = `ci_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();

    // If no sort order provided, get the next available order
    let sortOrder = data.sort_order;
    if (sortOrder === undefined) {
      const maxOrderQuery = `
        SELECT MAX(sort_order) as max_order 
        FROM collection_images 
        WHERE collection_id = ?
      `;
      const maxResult = await this.env.DB.prepare(maxOrderQuery).bind(data.collection_id).first();
      sortOrder = (maxResult?.max_order || 0) + 1;
    }

    const insertQuery = `
      INSERT INTO collection_images (id, collection_id, image_url, alt_text, sort_order, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    await this.env.DB.prepare(insertQuery).bind(
      imageId,
      data.collection_id,
      data.image_url,
      data.alt_text || '',
      sortOrder!,
      now,
      now
    ).run();

    return {
      id: imageId,
      collection_id: data.collection_id,
      image_url: data.image_url,
      alt_text: data.alt_text || '',
      sort_order: sortOrder!,
      created_at: now,
      updated_at: now,
    };
  }

  /**
   * Update a collection image
   */
  async updateCollectionImage(
    imageId: string,
    data: {
      image_url?: string;
      alt_text?: string;
      sort_order?: number;
    }
  ): Promise<CollectionImage | null> {
    const existingImage = await this.getCollectionImage(imageId);
    if (!existingImage) return null;

    const updateFields: string[] = [];
    const params: any[] = [];

    if (data.image_url !== undefined) {
      updateFields.push('image_url = ?');
      params.push(data.image_url);
    }

    if (data.alt_text !== undefined) {
      updateFields.push('alt_text = ?');
      params.push(data.alt_text);
    }

    if (data.sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      params.push(data.sort_order);
    }

    if (updateFields.length === 0) {
      return existingImage;
    }

    updateFields.push('updated_at = ?');
    params.push(new Date().toISOString());
    params.push(imageId);

    const updateQuery = `
      UPDATE collection_images 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    await this.env.DB.prepare(updateQuery).bind(...params).run();

    return await this.getCollectionImage(imageId);
  }

  /**
   * Delete a collection image
   */
  async deleteCollectionImage(imageId: string): Promise<boolean> {
    const query = `DELETE FROM collection_images WHERE id = ?`;
    const result = await this.env.DB.prepare(query).bind(imageId).run();
    
    return result.changes > 0;
  }

  /**
   * Reorder collection images
   */
  async reorderCollectionImages(
    collectionId: string,
    imageOrders: Array<{ id: string; sort_order: number }>
  ): Promise<void> {
    // Update each image's sort order
    for (const { id, sort_order } of imageOrders) {
      const updateQuery = `
        UPDATE collection_images 
        SET sort_order = ?, updated_at = ?
        WHERE id = ? AND collection_id = ?
      `;
      
      await this.env.DB.prepare(updateQuery).bind(
        sort_order,
        new Date().toISOString(),
        id,
        collectionId
      ).run();
    }
  }

  /**
   * Get the primary image for a collection (first in sort order)
   */
  async getPrimaryCollectionImage(collectionId: string): Promise<CollectionImage | null> {
    const query = `
      SELECT * FROM collection_images 
      WHERE collection_id = ? 
      ORDER BY sort_order ASC, created_at ASC 
      LIMIT 1
    `;
    
    const result = await this.env.DB.prepare(query).bind(collectionId).first();
    
    if (!result) return null;

    return {
      id: result.id,
      collection_id: result.collection_id,
      image_url: result.image_url,
      alt_text: result.alt_text,
      sort_order: result.sort_order || 0,
      created_at: result.created_at,
      updated_at: result.updated_at,
    };
  }

  /**
   * Delete all images for a collection
   */
  async deleteAllCollectionImages(collectionId: string): Promise<number> {
    const query = `DELETE FROM collection_images WHERE collection_id = ?`;
    const result = await this.env.DB.prepare(query).bind(collectionId).run();
    
    return result.changes;
  }

  /**
   * Get collections with their primary images
   */
  async getCollectionsWithPrimaryImages(): Promise<Array<{
    collection_id: string;
    primary_image?: CollectionImage;
  }>> {
    // Get all unique collection IDs that have images
    const collectionsQuery = `
      SELECT DISTINCT collection_id 
      FROM collection_images 
      ORDER BY collection_id
    `;
    
    const collectionsResult = await this.env.DB.prepare(collectionsQuery).all();
    const collections = collectionsResult.results;

    const result: Array<{
      collection_id: string;
      primary_image?: CollectionImage;
    }> = [];
    
    for (const collection of collections) {
      const primaryImage = await this.getPrimaryCollectionImage(collection.collection_id);
      result.push({
        collection_id: collection.collection_id,
        primary_image: primaryImage || undefined,
      });
    }

    return result;
  }
} 