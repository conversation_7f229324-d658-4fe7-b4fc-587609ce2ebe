import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import { 
  ChevronLeftIcon,
  TruckIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CurrencyDollarIcon,
  CheckIcon,
  XMarkIcon,
  CogIcon,
  ClockIcon,
  MapPinIcon
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';

interface ShippingProfile {
  id: string;
  name: string;
  type: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface ShippingOption {
  id: string;
  name: string;
  price_type: string;
  service_zone_id: string;
  shipping_profile_id?: string;
  provider_id?: string;
  data?: any;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface PriceSet {
  id: string;
  created_at: string;
  updated_at: string;
}

interface Price {
  id: string;
  title?: string;
  price_set_id: string;
  currency_code: string;
  amount: number;
  raw_amount: string;
  min_quantity?: number;
  max_quantity?: number;
  price_list_id?: string;
  created_at: string;
  updated_at: string;
}

interface ShippingOptionPricing {
  shipping_option: ShippingOption;
  price_set?: PriceSet;
  prices: Price[];
}

const ShippingPricing: React.FC = () => {
  const queryClient = useQueryClient();
  const [selectedProfile, setSelectedProfile] = useState<string>('');
  const [editingPrice, setEditingPrice] = useState<{
    shippingOptionId: string;
    priceId?: string;
    currency: string;
    amount: string;
  } | null>(null);
  const [isAddingOption, setIsAddingOption] = useState(false);
  const [newOption, setNewOption] = useState({
    name: '',
    price_type: 'flat_rate',
    service_zone_id: '',
    shipping_profile_id: '',
  });

  // Fetch shipping profiles
  const { data: profilesData, isLoading: profilesLoading } = useQuery({
    queryKey: ['admin-shipping-profiles'],
    queryFn: () => api.get('/admin/api/shipping-zones').then(res => res.data),
  });

  // Fetch shipping options with pricing
  const { data: pricingData, isLoading: pricingLoading } = useQuery({
    queryKey: ['admin-shipping-pricing', selectedProfile],
    queryFn: () => api.get(`/admin/api/shipping-pricing?profile=${selectedProfile}`).then(res => res.data),
    enabled: !!selectedProfile,
  });

  // Fetch service zones for new options
  const { data: serviceZonesData } = useQuery({
    queryKey: ['admin-service-zones'],
    queryFn: () => api.get('/admin/api/shipping-zones').then(res => res.data),
  });

  // Create shipping option mutation
  const createOptionMutation = useMutation({
    mutationFn: (data: any) => api.post('/admin/api/shipping-options', data),
    onSuccess: () => {
      toast.success('Shipping option created successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-shipping-pricing'] });
      setIsAddingOption(false);
      setNewOption({ name: '', price_type: 'flat_rate', service_zone_id: '', shipping_profile_id: '' });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create shipping option');
    },
  });

  // Update price mutation
  const updatePriceMutation = useMutation({
    mutationFn: ({ priceId, data }: { priceId?: string; data: any }) => 
      priceId 
        ? api.put(`/admin/api/shipping-prices/${priceId}`, data)
        : api.post('/admin/api/shipping-prices', data),
    onSuccess: () => {
      toast.success('Price updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-shipping-pricing'] });
      setEditingPrice(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update price');
    },
  });

  // Delete shipping option mutation
  const deleteOptionMutation = useMutation({
    mutationFn: (optionId: string) => api.delete(`/admin/api/shipping-options/${optionId}`),
    onSuccess: () => {
      toast.success('Shipping option deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-shipping-pricing'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete shipping option');
    },
  });

  const handleCreateOption = () => {
    if (!newOption.name.trim() || !newOption.service_zone_id || !selectedProfile) {
      toast.error('Please fill in all required fields');
      return;
    }

    createOptionMutation.mutate({
      ...newOption,
      shipping_profile_id: selectedProfile,
    });
  };

  const handleUpdatePrice = () => {
    if (!editingPrice || !editingPrice.amount.trim()) {
      toast.error('Please enter a valid price');
      return;
    }

    const amount = parseFloat(editingPrice.amount);
    if (isNaN(amount) || amount < 0) {
      toast.error('Please enter a valid positive number');
      return;
    }

    updatePriceMutation.mutate({
      priceId: editingPrice.priceId,
      data: {
        shipping_option_id: editingPrice.shippingOptionId,
        currency_code: editingPrice.currency,
        amount: amount, // Convert to cents
        raw_amount: (amount).toString(),
      },
    });
  };

  const handleDeleteOption = (optionId: string) => {
    if (window.confirm('Are you sure you want to delete this shipping option? This will also delete all associated prices.')) {
      deleteOptionMutation.mutate(optionId);
    }
  };

  const formatPrice = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const getPriceTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      flat_rate: 'Flat Rate',
      calculated: 'Calculated',
      free: 'Free Shipping',
    };
    return types[type] || type;
  };

  if (profilesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const shippingProfiles = profilesData?.data?.shippingProfiles || [];
  const shippingPricing = pricingData?.data?.shippingPricing || [];
  const serviceZones = serviceZonesData?.data?.serviceZones || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/admin/settings/shipping"
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Shipping Settings
          </Link>
          <span className="text-gray-400">/</span>
          <div className="flex items-center space-x-2">
            <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
            <h1 className="text-2xl font-semibold text-gray-900">Shipping Pricing</h1>
          </div>
        </div>
        {selectedProfile && (
          <button
            onClick={() => setIsAddingOption(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Add Shipping Option</span>
          </button>
        )}
      </div>

      {/* Description */}
      <div className="bg-green-50 border border-green-200 rounded-md p-4">
        <p className="text-green-800 text-sm">
          Configure pricing for shipping options across different shipping profiles. Set specific prices for each currency and shipping method.
        </p>
      </div>

      {/* Profile Selection */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Select Shipping Profile</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {shippingProfiles.map((profile: ShippingProfile) => (
            <button
              key={profile.id}
              onClick={() => setSelectedProfile(profile.id)}
              className={`p-4 rounded-lg border-2 text-left transition-all duration-200 ${
                selectedProfile === profile.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{profile.name}</h4>
                {selectedProfile === profile.id && (
                  <CheckIcon className="h-5 w-5 text-blue-600" />
                )}
              </div>
              <p className="text-sm text-gray-600 capitalize">{profile.type}</p>
              <p className="text-xs text-gray-500 mt-1">
                Created {new Date(profile.created_at).toLocaleDateString()}
              </p>
            </button>
          ))}
        </div>
      </div>

      {/* Add Option Form */}
      {isAddingOption && selectedProfile && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Shipping Option</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Option Name *
              </label>
              <input
                type="text"
                value={newOption.name}
                onChange={(e) => setNewOption({ ...newOption, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Standard Delivery, Express Shipping"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Type *
              </label>
              <select
                value={newOption.price_type}
                onChange={(e) => setNewOption({ ...newOption, price_type: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="flat_rate">Flat Rate</option>
                <option value="calculated">Calculated</option>
                <option value="free">Free Shipping</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Zone *
              </label>
              <select
                value={newOption.service_zone_id}
                onChange={(e) => setNewOption({ ...newOption, service_zone_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a service zone</option>
                {serviceZones.map((zone: any) => (
                  <option key={zone.id} value={zone.id}>
                    {zone.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setIsAddingOption(false)}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateOption}
              disabled={createOptionMutation.isPending}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200"
            >
              {createOptionMutation.isPending ? 'Creating...' : 'Create Option'}
            </button>
          </div>
        </div>
      )}

      {/* Shipping Options & Pricing */}
      {selectedProfile && (
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Shipping Options & Pricing</h3>
            <p className="text-sm text-gray-600 mt-1">
              {shippingPricing.length} option{shippingPricing.length !== 1 ? 's' : ''} configured
            </p>
          </div>

          {pricingLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading pricing data...</p>
            </div>
          ) : shippingPricing.length === 0 ? (
            <div className="p-6 text-center">
              <TruckIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No shipping options configured</h3>
              <p className="mt-1 text-sm text-gray-500">
                Add shipping options to this profile to set pricing.
              </p>
              <button
                onClick={() => setIsAddingOption(true)}
                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors duration-200"
              >
                Add Shipping Option
              </button>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {shippingPricing.map((item: ShippingOptionPricing) => (
                <div key={item.shipping_option.id} className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h4 className="text-lg font-medium text-gray-900">{item.shipping_option.name}</h4>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getPriceTypeLabel(item.shipping_option.price_type)}
                        </span>
                      </div>
                      
                      <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <MapPinIcon className="h-4 w-4" />
                          <span>Service Zone: {item.shipping_option.service_zone_id}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <ClockIcon className="h-4 w-4" />
                          <span>Created {new Date(item.shipping_option.created_at).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CogIcon className="h-4 w-4" />
                          <span>{item.prices.length} price{item.prices.length !== 1 ? 's' : ''} set</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => setEditingPrice({
                          shippingOptionId: item.shipping_option.id,
                          currency: 'EUR',
                          amount: '',
                        })}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors duration-200"
                        title="Add price"
                      >
                        <PlusIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteOption(item.shipping_option.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors duration-200"
                        title="Delete option"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {/* Prices List */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h5 className="text-sm font-medium text-gray-900 mb-3">Pricing by Currency</h5>
                    {item.prices.length === 0 ? (
                      <div className="text-center py-4">
                        <CurrencyDollarIcon className="mx-auto h-8 w-8 text-gray-400" />
                        <p className="text-sm text-gray-500 mt-1">No prices configured</p>
                        <button
                          onClick={() => setEditingPrice({
                            shippingOptionId: item.shipping_option.id,
                            currency: 'EUR',
                            amount: '',
                          })}
                          className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                        >
                          Add first price
                        </button>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {item.prices.map((price: Price) => (
                          <div
                            key={price.id}
                            className="bg-white rounded border border-gray-200 p-3 flex items-center justify-between"
                          >
                            <div>
                              <div className="font-medium text-gray-900">
                                {formatPrice(price.amount, price.currency_code)}
                              </div>
                              <div className="text-xs text-gray-500 uppercase">
                                {price.currency_code}
                              </div>
                            </div>
                            <button
                              onClick={() => setEditingPrice({
                                shippingOptionId: item.shipping_option.id,
                                priceId: price.id,
                                currency: price.currency_code,
                                amount: (price.amount).toString(),
                              })}
                              className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
                              title="Edit price"
                            >
                              <PencilIcon className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Edit Price Modal */}
      {editingPrice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {editingPrice.priceId ? 'Edit Price' : 'Add Price'}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Currency
                </label>
                <select
                  value={editingPrice.currency}
                  onChange={(e) => setEditingPrice({ ...editingPrice, currency: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="EUR">EUR - Euro</option>
                  <option value="USD">USD - US Dollar</option>
                  <option value="RON">RON - Romanian Leu</option>
                  <option value="HUF">HUF - Hungarian Forint</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={editingPrice.amount}
                  onChange={(e) => setEditingPrice({ ...editingPrice, amount: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setEditingPrice(null)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleUpdatePrice}
                disabled={updatePriceMutation.isPending}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200"
              >
                {updatePriceMutation.isPending ? 'Saving...' : 'Save Price'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShippingPricing; 