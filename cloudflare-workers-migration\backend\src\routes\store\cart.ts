import { Hono } from 'hono';
import { WorkerEnv } from 'handmadein-shared';
import { DatabaseService } from '../../services/database';
import { CacheService } from '../../services/cache';
import { z } from 'zod';
import { eq, and, desc, asc, isNull } from 'drizzle-orm';
import { 
  carts, 
  lineItems, 
  productVariants, 
  products, 
  addresses, 
  shippingMethods,
  shippingOptions,
  paymentSessions,
  regions,
  countries
} from '../../database/schema';

const app = new Hono<{ Bindings: WorkerEnv }>();

// Validation schemas
const createCartSchema = z.object({
  region_id: z.string().optional(),
  customer_id: z.string().optional(),
  email: z.string().email().optional(),
  currency_code: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

const addLineItemSchema = z.object({
  variant_id: z.string(),
  quantity: z.number().min(1),
  metadata: z.record(z.any()).optional(),
});

const updateLineItemSchema = z.object({
  quantity: z.number().min(0),
  metadata: z.record(z.any()).optional(),
});

const updateCartSchema = z.object({
  email: z.string().email().optional(),
  billing_address: z.object({
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    address_1: z.string().optional(),
    address_2: z.string().optional(),
    city: z.string().optional(),
    country_code: z.string().optional(),
    province: z.string().optional(),
    postal_code: z.string().optional(),
    phone: z.string().optional(),
    company: z.string().optional(),
  }).optional(),
  shipping_address: z.object({
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    address_1: z.string().optional(),
    address_2: z.string().optional(),
    city: z.string().optional(),
    country_code: z.string().optional(),
    province: z.string().optional(),
    postal_code: z.string().optional(),
    phone: z.string().optional(),
    company: z.string().optional(),
  }).optional(),
  metadata: z.record(z.any()).optional(),
});

const addShippingMethodSchema = z.object({
  option_id: z.string(),
  data: z.record(z.any()).optional(),
});

// Helper function to enrich cart with related data
async function enrichCart(cart: any, db: DatabaseService): Promise<any> {
  const enriched = { ...cart };

  // Parse metadata
  if (enriched.metadata) {
    try {
      enriched.metadata = JSON.parse(enriched.metadata);
    } catch (e) {
      enriched.metadata = {};
    }
  }

  // Get line items with product and variant details
  const items = await db.findMany('cart_line_item', {
    where: `cart_id = '${cart.id}'`,
  });

  enriched.items = await Promise.all(
    items.map(async (item: any) => {
      const enrichedItem = { ...item };

      // Parse metadata
      if (enrichedItem.metadata) {
        try {
          enrichedItem.metadata = JSON.parse(enrichedItem.metadata);
        } catch (e) {
          enrichedItem.metadata = {};
        }
      }

      // Get variant details
      if (item.variant_id) {
        const variant = await db.findById('product_variant', item.variant_id);
        if (variant) {
          enrichedItem.variant = variant;
          // Set variant fields for frontend use
          enrichedItem.variant_title = variant.title;
          enrichedItem.variant_sku = variant.sku;

          // Get product details
          const product = await db.findById('product', variant.product_id);
          if (product) {
            enrichedItem.variant.product = product;
            // Set the product_id on the item for frontend use
            enrichedItem.product_id = product.id;
            // Set additional product fields for frontend use
            enrichedItem.product_title = product.title;
            enrichedItem.product_description = product.description;
            enrichedItem.product_handle = product.handle;
            enrichedItem.product_subtitle = product.subtitle;
          }
        }
      }

      return enrichedItem;
    })
  );

  // Get billing address
  if (cart.billing_address_id) {
    enriched.billing_address = await db.findById('address', cart.billing_address_id);
  }

  // Get shipping address
  if (cart.shipping_address_id) {
    enriched.shipping_address = await db.findById('address', cart.shipping_address_id);
  }

  // Get shipping methods
  const shippingMethodsData = await db.findMany('shipping_method', {
    where: `cart_id = '${cart.id}'`,
  });

  enriched.shipping_methods = await Promise.all(
    shippingMethodsData.map(async (method: any) => {
      const enrichedMethod = { ...method };
      
      // Get shipping option details
      const option = await db.findById('shipping_option', method.shipping_option_id);
      if (option) {
        enrichedMethod.shipping_option = option;
      }

      return enrichedMethod;
    })
  );

  // Get payment sessions
  const paymentSessionsData = await db.findMany('payment_session', {
    where: `cart_id = '${cart.id}'`,
  });

  enriched.payment_sessions = paymentSessionsData.map((session: any) => {
    const enrichedSession = { ...session };
    
    // Parse data
    if (enrichedSession.data) {
      try {
        enrichedSession.data = JSON.parse(enrichedSession.data);
      } catch (e) {
        enrichedSession.data = {};
      }
    }

    return enrichedSession;
  });

  // Get region details
  if (cart.region_id) {
    enriched.region = await db.findById('region', cart.region_id);
  }

  // Calculate totals
  enriched.subtotal = enriched.items.reduce((sum: number, item: any) => {
    return sum + (item.unit_price * item.quantity);
  }, 0);

  enriched.shipping_total = enriched.shipping_methods.reduce((sum: number, method: any) => {
    return sum + method.price;
  }, 0);

  enriched.tax_total = 0; // TODO: Implement tax calculation
  enriched.total = enriched.subtotal + enriched.shipping_total + enriched.tax_total;

  return enriched;
}

// POST /store/carts - Create a new cart
app.post('/', async (c) => {
  try {
    const body = createCartSchema.parse(await c.req.json());
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    console.log('[Cart] Creating new cart with data:', body);

    // Get default region if none provided
    let regionId = body.region_id;
    if (!regionId) {
      const regions = await db.findMany('region', { limit: 1 });
      if (regions.length > 0) {
        regionId = regions[0].id;
      }
    }

    if (!regionId) {
      return c.json({
        success: false,
        error: 'No region available',
      }, 400);
    }

    // Create cart
    const cartData = {
      id: crypto.randomUUID(),
      region_id: regionId,
      customer_id: body.customer_id || null,
      email: body.email || null,
      currency_code: body.currency_code || 'RON',
      metadata: body.metadata ? JSON.stringify(body.metadata) : null,
    };

    const cart = await db.create('cart', cartData);
    console.log('[Cart] Created cart:', cart.id);

    // Enrich cart with related data
    const enrichedCart = await enrichCart(cart, db);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    console.error('Error creating cart:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create cart',
    }, 500);
  }
});

// GET /store/carts/:id - Get a specific cart
app.get('/:id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    console.log('[Cart] Fetching cart:', cartId);

    // Try to get from cache first
    const cached = await cache.getCart(cartId);
    if (cached) {
      console.log('[Cart] Found cart in cache');
      return c.json({
        success: true,
        data: cached,
      });
    }

    // Get cart from database
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      console.log('[Cart] Cart not found in database:', cartId);
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    console.log('[Cart] Found cart in database:', cart.id);
    const enrichedCart = await enrichCart(cart, db);

    // Cache the result
    await cache.setCart(cartId, enrichedCart, 1800);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    console.error('Error fetching cart:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch cart',
    }, 500);
  }
});

// POST /store/carts/:id/line-items - Add item to cart
app.post('/:id/line-items', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = addLineItemSchema.parse(await c.req.json());
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    console.log(`[Cart] Adding item to cart ${cartId}, variant_id: ${body.variant_id}, quantity: ${body.quantity}`);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      console.log(`[Cart] Cart not found: ${cartId}`);
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    console.log(`[Cart] Cart found: ${cart.id}`);

    // Debug: Let's check what variants exist in the database
    console.log(`[Cart] Looking for variant: ${body.variant_id}`);
    
    // First, let's see what variants exist
    const allVariants = await db.findMany('product_variant', { limit: 5 });
    console.log(`[Cart] Sample variants in database:`, allVariants.map(v => ({ id: v.id, title: v.title, product_id: v.product_id })));

    // Verify variant exists and get product details
    const variant = await db.findById('product_variant', body.variant_id);
    console.log(`[Cart] Variant lookup result:`, variant ? { id: variant.id, title: variant.title, product_id: variant.product_id, deleted_at: variant.deleted_at } : 'NOT FOUND');
    
    if (!variant || variant.deleted_at) {
      console.log(`[Cart] Product variant not found or deleted: ${body.variant_id}`);
      return c.json({
        success: false,
        error: 'Product variant not found',
      }, 404);
    }

    const product = await db.findById('product', variant.product_id);
    console.log(`[Cart] Product lookup result:`, product ? { id: product.id, title: product.title, status: product.status, deleted_at: product.deleted_at } : 'NOT FOUND');
    
    if (!product || product.deleted_at || product.status !== 'published') {
      console.log(`[Cart] Product not available: ${variant.product_id}`);
      return c.json({
        success: false,
        error: 'Product not available',
      }, 404);
    }

    // Get variant pricing information
    const pricingQuery = `
      SELECT DISTINCT
        pr.amount as price,
        pr.currency_code,
        pr.raw_amount
      FROM product_variant_price_set pvps
      LEFT JOIN price_set ps ON pvps.price_set_id = ps.id AND (ps.deleted_at IS NULL OR ps.deleted_at = '')
      LEFT JOIN price pr ON ps.id = pr.price_set_id AND (pr.deleted_at IS NULL OR pr.deleted_at = '')
      WHERE pvps.variant_id = ? AND (pvps.deleted_at IS NULL OR pvps.deleted_at = '')
      LIMIT 1
    `;
    const pricingResult = await db.executeRawQuery(pricingQuery, [variant.id]);
    
    let unitPrice = 0;
    if (pricingResult && pricingResult.length > 0) {
      unitPrice = pricingResult[0].price || 0;
    }
    console.log(`[Cart] Unit price for variant ${variant.id}: ${unitPrice}`);

    // Get variant inventory information
    const inventoryQuery = `
      SELECT 
        SUM(il.stocked_quantity) as inventory_quantity
      FROM product_variant_inventory_item pvi
      LEFT JOIN inventory_level il ON pvi.inventory_item_id = il.inventory_item_id AND (il.deleted_at IS NULL OR il.deleted_at = '')
      WHERE pvi.variant_id = ? AND (pvi.deleted_at IS NULL OR pvi.deleted_at = '')
    `;
    const inventoryResult = await db.executeRawQuery(inventoryQuery, [variant.id]);
    
    let availableQuantity = 0;
    if (inventoryResult && inventoryResult.length > 0) {
      availableQuantity = inventoryResult[0].inventory_quantity || 0;
    }
    console.log(`[Cart] Available inventory for variant ${variant.id}: ${availableQuantity}`);

    // Check inventory
    if (variant.manage_inventory && availableQuantity < body.quantity) {
      console.log(`[Cart] Insufficient inventory: requested ${body.quantity}, available ${availableQuantity}`);
      return c.json({
        success: false,
        error: 'Insufficient inventory',
      }, 400);
    }

    // Check if item already exists in cart
    const existingItems = await db.findMany('cart_line_item', {
      where: `cart_id = '${cartId}' AND variant_id = '${body.variant_id}'`,
    });

    let lineItem;
    if (existingItems.length > 0 && existingItems[0].should_merge) {
      // Update existing item
      console.log(`[Cart] Updating existing line item`);
      const existingItem = existingItems[0];
      const newQuantity = existingItem.quantity + body.quantity;

      // Check inventory again for new total
      if (variant.manage_inventory && availableQuantity < newQuantity) {
        return c.json({
          success: false,
          error: 'Insufficient inventory',
        }, 400);
      }

      lineItem = await db.update('cart_line_item', existingItem.id, {
        quantity: newQuantity,
        metadata: body.metadata ? JSON.stringify(body.metadata) : existingItem.metadata,
      });
    } else {
      // Create new line item
      console.log(`[Cart] Creating new line item`);
      const lineItemData = {
        id: crypto.randomUUID(),
        cart_id: cartId,
        variant_id: body.variant_id,
        title: product.title,
        thumbnail: product.thumbnail,
        unit_price: unitPrice,
        raw_unit_price: unitPrice,
        quantity: body.quantity,
        metadata: body.metadata ? JSON.stringify(body.metadata) : null,
      };

      lineItem = await db.create('cart_line_item', lineItemData);
      console.log(`[Cart] Created line item: ${lineItem.id}`);
    }

    // Invalidate cart cache
    await cache.invalidateCart(cartId);

    // Get updated cart
    const updatedCart = await enrichCart(cart, db);
    console.log(`[Cart] Successfully added item to cart, cart now has ${updatedCart.items?.length || 0} items`);

    return c.json({
      success: true,
      data: updatedCart,
    });

  } catch (error) {
    console.error('Error adding item to cart:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to add item to cart',
    }, 500);
  }
});

// POST /store/carts/:id/line-items/:line_id - Update line item
app.post('/:id/line-items/:line_id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const lineId = c.req.param('line_id');
    const body = updateLineItemSchema.parse(await c.req.json());
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    // Verify line item exists and belongs to cart
    const lineItem = await db.findById('cart_line_item', lineId);
    if (!lineItem || lineItem.cart_id !== cartId) {
      return c.json({
        success: false,
        error: 'Line item not found',
      }, 404);
    }

    // If quantity is 0, delete the item
    if (body.quantity === 0) {
      await db.delete('cart_line_item', lineId);
    } else {
      // Check inventory for new quantity
      if (lineItem.variant_id) {
        const variant = await db.findById('product_variant', lineItem.variant_id);
        if (variant && variant.manage_inventory) {
          // Get current inventory
          const inventoryQuery = `
            SELECT 
              SUM(il.stocked_quantity) as inventory_quantity
            FROM product_variant_inventory_item pvi
            LEFT JOIN inventory_level il ON pvi.inventory_item_id = il.inventory_item_id AND (il.deleted_at IS NULL OR il.deleted_at = '')
            WHERE pvi.variant_id = ? AND (pvi.deleted_at IS NULL OR pvi.deleted_at = '')
          `;
          const inventoryResult = await db.executeRawQuery(inventoryQuery, [variant.id]);
          
          let availableQuantity = 0;
          if (inventoryResult && inventoryResult.length > 0) {
            availableQuantity = inventoryResult[0].inventory_quantity || 0;
          }

          const quantityDiff = body.quantity - lineItem.quantity;
          if (availableQuantity < quantityDiff) {
            return c.json({
              success: false,
              error: 'Insufficient inventory',
            }, 400);
          }
        }
      }

      // Update line item
      await db.update('cart_line_item', lineId, {
        quantity: body.quantity,
        metadata: body.metadata ? JSON.stringify(body.metadata) : lineItem.metadata,
      });
    }

    // Invalidate cart cache
    await cache.invalidateCart(cartId);

    // Get updated cart
    const updatedCart = await enrichCart(cart, db);

    return c.json({
      success: true,
      data: updatedCart,
    });

  } catch (error) {
    console.error('Error updating line item:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update line item',
    }, 500);
  }
});

// DELETE /store/carts/:id/line-items/:line_id - Remove line item
app.delete('/:id/line-items/:line_id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const lineId = c.req.param('line_id');
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    // Verify line item exists and belongs to cart
    const lineItem = await db.findById('cart_line_item', lineId);
    if (!lineItem || lineItem.cart_id !== cartId) {
      return c.json({
        success: false,
        error: 'Line item not found',
      }, 404);
    }

    // Delete the item
    await db.delete('cart_line_item', lineId);

    // Invalidate cart cache
    await cache.invalidateCart(cartId);

    // Get updated cart
    const updatedCart = await enrichCart(cart, db);

    return c.json({
      success: true,
      data: updatedCart,
    });

  } catch (error) {
    console.error('Error removing line item:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to remove line item',
    }, 500);
  }
});

// POST /store/carts/:id - Update cart
app.post('/:id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = updateCartSchema.parse(await c.req.json());
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    const updateData: any = {};

    // Update email
    if (body.email) {
      updateData.email = body.email;
    }

    // Update metadata
    if (body.metadata) {
      updateData.metadata = JSON.stringify(body.metadata);
    }

    // Handle billing address
    if (body.billing_address) {
      let billingAddressId = cart.billing_address_id;
      
      if (billingAddressId) {
        // Update existing address
        await db.update('customer_address', billingAddressId, body.billing_address);
      } else {
        // Create new address
        const addressData = {
          id: crypto.randomUUID(),
          ...body.billing_address,
        };
        const address = await db.create('customer_address', addressData);
        billingAddressId = address.id;
      }
      
      updateData.billing_address_id = billingAddressId;
    }

    // Handle shipping address
    if (body.shipping_address) {
      let shippingAddressId = cart.shipping_address_id;
      
      if (shippingAddressId) {
        // Update existing address
        await db.update('customer_address', shippingAddressId, body.shipping_address);
      } else {
        // Create new address
        const addressData = {
          id: crypto.randomUUID(),
          ...body.shipping_address,
        };
        const address = await db.create('customer_address', addressData);
        shippingAddressId = address.id;
      }
      
      updateData.shipping_address_id = shippingAddressId;
    }

    // Update cart
    if (Object.keys(updateData).length > 0) {
      await db.update('cart', cartId, updateData);
    }

    // Invalidate cart cache
    await cache.invalidateCart(cartId);

    // Get updated cart
    const updatedCart = await db.findById('cart', cartId);
    const enrichedCart = await enrichCart(updatedCart, db);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    console.error('Error updating cart:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update cart',
    }, 500);
  }
});

// GET /store/carts/:id/shipping-options - Get shipping options for cart
app.get('/:id/shipping-options', async (c) => {
  try {
    const cartId = c.req.param('id');
    const db = new DatabaseService(c.env);

    console.log(`🔍 [DEBUG] Starting shipping options lookup for cart: ${cartId}`);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      console.log(`❌ [DEBUG] Cart not found or deleted: ${cartId}`);
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    console.log(`✅ [DEBUG] Cart found: ${cart.id}, shipping_address_id: ${cart.shipping_address_id}`);

    // Get all cart items
    const cartItems = await db.findMany('cart_line_item', {
      where: `cart_id = '${cartId}'`,
    });

    console.log(`📦 [DEBUG] Found ${cartItems.length} cart items:`, cartItems.map(item => ({
      id: item.id,
      variant_id: item.variant_id,
      quantity: item.quantity
    })));

    if (cartItems.length === 0) {
      console.log(`⚠️ [DEBUG] Cart has no items, returning empty array`);
      return c.json({
        success: true,
        data: [],
      });
    }

    // Get shipping profiles for all products in the cart
    const shippingProfileIds = new Set<string>();
    
    console.log(`🔍 [DEBUG] Extracting shipping profiles from cart items...`);
    
    for (const item of cartItems) {
      console.log(`🔍 [DEBUG] Processing cart item ${item.id} with variant_id: ${item.variant_id}`);
      
      if (item.variant_id) {
        // Get variant and then product
        const variant = await db.findById('product_variant', item.variant_id);
        console.log(`🔍 [DEBUG] Variant lookup result:`, variant ? {
          id: variant.id,
          product_id: variant.product_id,
          deleted_at: variant.deleted_at
        } : 'NOT FOUND');
        
        if (variant && variant.product_id) {
          const product = await db.findById('product', variant.product_id);
          console.log(`🔍 [DEBUG] Product lookup result:`, product ? {
            id: product.id,
            title: product.title,
            deleted_at: product.deleted_at
          } : 'NOT FOUND');
          
          if (product) {
            // Get shipping profiles from the relationship table
            const productProfiles = await db.findMany('product_shipping_profile', {
              where: `product_id = '${product.id}' AND (deleted_at IS NULL OR deleted_at = '')`,
            });
            
            console.log(`🔍 [DEBUG] Product shipping profiles lookup result: ${productProfiles.length} profiles found`, productProfiles.map(p => ({
              id: p.id,
              shipping_profile_id: p.shipping_profile_id,
              deleted_at: p.deleted_at
            })));
            
            for (const profile of productProfiles) {
              shippingProfileIds.add(profile.shipping_profile_id);
              console.log(`✅ [DEBUG] Added shipping profile: ${profile.shipping_profile_id}`);
            }
            
            if (productProfiles.length === 0) {
              console.log(`⚠️ [DEBUG] Product has no shipping profiles in relationship table`);
            }
          }
        }
      }
    }

    // Convert to array for easier handling
    const uniqueShippingProfiles = Array.from(shippingProfileIds);
    
    console.log(`📋 [DEBUG] Unique shipping profiles found: ${uniqueShippingProfiles.length}`, uniqueShippingProfiles);
    
    if (uniqueShippingProfiles.length === 0) {
      // No shipping profiles found, return empty array
      console.log(`❌ [DEBUG] No shipping profiles found for any cart items, returning empty array`);
      return c.json({
        success: true,
        data: [],
      });
    }

    // Get cart's shipping address to determine applicable geo zones
    let applicableGeoZones: any[] = [];
    let shippingAddress = null;
    
    console.log(`🔍 [DEBUG] Looking up shipping address...`);
    
    // Get shipping address from the addresses table if cart has shipping_address_id
    if (cart.shipping_address_id) {
      shippingAddress = await db.findById('customer_address', cart.shipping_address_id);
      console.log(`🔍 [DEBUG] Shipping address lookup result:`, shippingAddress ? {
        id: shippingAddress.id,
        city: shippingAddress.city,
        country_code: shippingAddress.country_code,
        province: shippingAddress.province,
        deleted_at: shippingAddress.deleted_at
      } : 'NOT FOUND');
    } else {
      console.log(`⚠️ [DEBUG] Cart has no shipping_address_id`);
    }
    
    if (shippingAddress) {
      console.log(`🔍 [DEBUG] Processing geo zones for address...`);
      
      // Find geo zones that match the shipping address
      // Check for country, province/state, and city matches
      const geoZoneQueries = [];
      
      // Country-level geo zones
      if (shippingAddress.country_code) {
        geoZoneQueries.push(`country_code = '${shippingAddress.country_code}'`);
        console.log(`🔍 [DEBUG] Added country query: country_code = '${shippingAddress.country_code}'`);
      }
      
      // Province/state-level geo zones
      if (shippingAddress.province && shippingAddress.country_code) {
        geoZoneQueries.push(`country_code = '${shippingAddress.country_code}' AND province_code = '${shippingAddress.province.toLowerCase()}'`);
        console.log(`🔍 [DEBUG] Added province query: country_code = '${shippingAddress.country_code}' AND province_code = '${shippingAddress.province.toLowerCase()}'`);
      }
      
      // City-level geo zones
      if (shippingAddress.city && shippingAddress.country_code) {
        geoZoneQueries.push(`country_code = '${shippingAddress.country_code}' AND city = '${shippingAddress.city}'`);
        console.log(`🔍 [DEBUG] Added city query: country_code = '${shippingAddress.country_code}' AND city = '${shippingAddress.city}'`);
      }
      
      console.log(`🔍 [DEBUG] Executing ${geoZoneQueries.length} geo zone queries...`);
      
      // Execute all geo zone queries
      for (const query of geoZoneQueries) {
        console.log(`🔍 [DEBUG] Executing geo zone query: ${query}`);
        const zones = await db.findMany('geo_zone', {
          where: query,
        });
        console.log(`🔍 [DEBUG] Geo zone query result: ${zones.length} zones found`);
        applicableGeoZones.push(...zones);
      }
      
      // Remove duplicates
      applicableGeoZones = applicableGeoZones.filter((zone, index, self) => 
        self.findIndex(z => z.id === zone.id) === index
      );
      
      console.log(`📍 [DEBUG] Final applicable geo zones: ${applicableGeoZones.length}`, applicableGeoZones.map(z => ({
        id: z.id,
        service_zone_id: z.service_zone_id,
        country_code: z.country_code,
        city: z.city
      })));
    } else {
      console.log(`⚠️ [DEBUG] No shipping address available for geo zone lookup`);
    }

    // Find service zones that contain the applicable geo zones
    const serviceZoneIds: string[] = [];
    for (const geoZone of applicableGeoZones) {
      if (geoZone.service_zone_id) {
        serviceZoneIds.push(geoZone.service_zone_id);
      }
    }

    // Remove duplicate service zone IDs
    const uniqueServiceZoneIds = [...new Set(serviceZoneIds)];
    
    console.log(`🌐 [DEBUG] Service zones from geo zones: ${uniqueServiceZoneIds.length}`, uniqueServiceZoneIds);
    
    // Build query to get shipping options that support ALL shipping profiles in the cart
    // We need shipping options that:
    // 1. Have is_return = false rule
    // 2. Have enabled_in_store = true rule
    // 3. Support ALL shipping profiles in the cart
    // 4. Optionally match service zones
    
    console.log(`🔍 [DEBUG] Building shipping options query...`);
    
    let shippingOptionsQuery = `
      SELECT DISTINCT 
        so.*,
        p.amount as price,
        p.currency_code as price_currency_code,
        p.raw_amount as raw_price
      FROM shipping_option so
      LEFT JOIN shipping_option_price_set sops ON so.id = sops.shipping_option_id AND (sops.deleted_at IS NULL OR sops.deleted_at = '')
      LEFT JOIN price_set ps ON sops.price_set_id = ps.id AND (ps.deleted_at IS NULL OR ps.deleted_at = '')
      LEFT JOIN price p ON ps.id = p.price_set_id AND (p.deleted_at IS NULL OR p.deleted_at = '')
      WHERE so.deleted_at IS NULL
      AND EXISTS (
        SELECT 1 FROM shipping_option_rule sor1 
        WHERE sor1.shipping_option_id = so.id 
        AND sor1.attribute = 'is_return' 
        AND sor1.value = '"false"'
        AND (sor1.deleted_at IS NULL OR sor1.deleted_at = '')
      )
      AND EXISTS (
        SELECT 1 FROM shipping_option_rule sor2 
        WHERE sor2.shipping_option_id = so.id 
        AND sor2.attribute = 'enabled_in_store' 
        AND sor2.value = '"true"'
        AND (sor2.deleted_at IS NULL OR sor2.deleted_at = '')
      )
    `;

    console.log(`🔍 [DEBUG] Base query constructed`);

    // Add shipping profile constraints - shipping option must support ALL profiles in cart
    const shippingProfilesList = uniqueShippingProfiles.map(id => `'${id}'`).join(',');
    
    console.log(`🔍 [DEBUG] Adding shipping profile constraints for profiles: ${shippingProfilesList}`);
    
    // For each shipping profile, ensure the shipping option supports it
    for (const profileId of uniqueShippingProfiles) {
      shippingOptionsQuery += `
        AND so.shipping_profile_id = '${profileId}'
      `;
      console.log(`🔍 [DEBUG] Added profile constraint: so.shipping_profile_id = '${profileId}'`);
    }
    
    if (uniqueServiceZoneIds.length > 0) {
      // Add service zone filter if we found applicable zones
      const serviceZoneIdsList = uniqueServiceZoneIds.map(id => `'${id}'`).join(',');
      shippingOptionsQuery += ` AND so.service_zone_id IN (${serviceZoneIdsList})`;
      console.log(`🔍 [DEBUG] Added service zone filter: so.service_zone_id IN (${serviceZoneIdsList})`);
    } else {
      console.log(`⚠️ [DEBUG] No service zones to filter by, query will match all service zones`);
    }
    
    shippingOptionsQuery += ` ORDER BY so.created_at ASC`;
    
    console.log(`🔍 [DEBUG] Final query:`, shippingOptionsQuery);
    
    console.log(`🔍 [DEBUG] Executing main shipping options query...`);
    const options = await db.executeRawQuery(shippingOptionsQuery);

    console.log(`📊 [DEBUG] Main query result: ${options.length} options found`, options.map((o: any) => ({
      id: o.id,
      name: o.name,
      shipping_profile_id: o.shipping_profile_id,
      service_zone_id: o.service_zone_id,
      price: o.price,
      currency_code: o.price_currency_code,
      deleted_at: o.deleted_at
    })));

    // If no options found with service zone restrictions, try without service zone filter
    if (options.length === 0 && uniqueServiceZoneIds.length > 0) {
      console.log(`🔍 [DEBUG] No options found with service zone filter, trying fallback query...`);
      
      let fallbackQuery = `
        SELECT DISTINCT 
          so.*,
          p.amount as price,
          p.currency_code as price_currency_code,
          p.raw_amount as raw_price
        FROM shipping_option so
        LEFT JOIN shipping_option_price_set sops ON so.id = sops.shipping_option_id AND (sops.deleted_at IS NULL OR sops.deleted_at = '')
        LEFT JOIN price_set ps ON sops.price_set_id = ps.id AND (ps.deleted_at IS NULL OR ps.deleted_at = '')
        LEFT JOIN price p ON ps.id = p.price_set_id AND (p.deleted_at IS NULL OR p.deleted_at = '')
        WHERE so.deleted_at IS NULL
        AND EXISTS (
          SELECT 1 FROM shipping_option_rule sor1 
          WHERE sor1.shipping_option_id = so.id 
          AND sor1.attribute = 'is_return' 
          AND sor1.value = '"false"'
          AND (sor1.deleted_at IS NULL OR sor1.deleted_at = '')
        )
        AND EXISTS (
          SELECT 1 FROM shipping_option_rule sor2 
          WHERE sor2.shipping_option_id = so.id 
          AND sor2.attribute = 'enabled_in_store' 
          AND sor2.value = '"true"'
          AND (sor2.deleted_at IS NULL OR sor2.deleted_at = '')
        )
      `;
      
      // Add shipping profile constraints for fallback as well
      for (const profileId of uniqueShippingProfiles) {
        fallbackQuery += `
          AND so.shipping_profile_id = '${profileId}'
        `;
        console.log(`🔍 [DEBUG] Added profile constraint to fallback: so.shipping_profile_id = '${profileId}'`);
      }
      
      fallbackQuery += ` ORDER BY so.created_at ASC`;
      
      console.log(`🔍 [DEBUG] Fallback query:`, fallbackQuery);
      console.log(`🔍 [DEBUG] Executing fallback query...`);
      
      const fallbackOptions = await db.executeRawQuery(fallbackQuery);
      
      console.log(`📊 [DEBUG] Fallback query result: ${fallbackOptions.length} options found`, fallbackOptions.map((o: any) => ({
        id: o.id,
        name: o.name,
        shipping_profile_id: o.shipping_profile_id,
        service_zone_id: o.service_zone_id,
        price: o.price,
        currency_code: o.price_currency_code,
        deleted_at: o.deleted_at
      })));
      
      console.log(`✅ [DEBUG] Returning fallback options: ${fallbackOptions.length} options`);
      
      return c.json({
        success: true,
        data: fallbackOptions,
      });
    }

    console.log(`✅ [DEBUG] Returning main options: ${options.length} options`);

    return c.json({
      success: true,
      data: options,
    });

  } catch (error) {
    console.error('❌ [DEBUG] Error fetching shipping options:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch shipping options',
    }, 500);
  }
});

// POST /store/carts/:id/shipping-methods - Add shipping method to cart
app.post('/:id/shipping-methods', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = addShippingMethodSchema.parse(await c.req.json());
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    // Verify shipping option exists
    const option = await db.findById('shipping_option', body.option_id);
    if (!option) {
      return c.json({
        success: false,
        error: 'Shipping option not found',
      }, 404);
    }

    // Remove existing shipping methods for this cart
    const existingMethods = await db.findMany('shipping_method', {
      where: `cart_id = '${cartId}'`,
    });

    for (const method of existingMethods) {
      await db.delete('shipping_method', method.id);
    }

    // Create new shipping method
    const shippingMethodData = {
      id: crypto.randomUUID(),
      shipping_option_id: body.option_id,
      cart_id: cartId,
      price: option.amount || 0,
      data: body.data ? JSON.stringify(body.data) : JSON.stringify({}),
      includes_tax: false,
      subtotal: option.amount || 0,
      total: option.amount || 0,
      tax_total: 0,
    };

    await db.create('shipping_method', shippingMethodData);

    // Invalidate cart cache
    await cache.invalidateCart(cartId);

    // Get updated cart
    const enrichedCart = await enrichCart(cart, db);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    console.error('Error adding shipping method:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to add shipping method',
    }, 500);
  }
});

// POST /store/carts/:id/payment-sessions - Initialize payment sessions
app.post('/:id/payment-sessions', async (c) => {
  try {
    const cartId = c.req.param('id');
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    // Get cart total
    const enrichedCart = await enrichCart(cart, db);
    const total = enrichedCart.total;

    // Remove existing payment sessions
    const existingSessions = await db.findMany('payment_session', {
      where: `cart_id = '${cartId}'`,
    });

    for (const session of existingSessions) {
      await db.delete('payment_session', session.id);
    }

    // Create payment sessions for available providers
    const providers = ['stripe', 'cash_on_delivery']; // TODO: Get from configuration

    const sessions: any[] = [];
    for (const providerId of providers) {
      const sessionData = {
        id: crypto.randomUUID(),
        cart_id: cartId,
        provider_id: providerId,
        is_selected: false,
        is_initiated: false,
        status: 'pending',
        data: JSON.stringify({}),
        amount: total,
      };

      const session = await db.create('payment_session', sessionData);
      sessions.push(session);
    }

    // Invalidate cart cache
    await cache.invalidateCart(cartId);

    return c.json({
      success: true,
      data: sessions,
    });

  } catch (error) {
    console.error('Error initializing payment sessions:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to initialize payment sessions',
    }, 500);
  }
});

// POST /store/carts/:id/payment-sessions/:provider_id - Select payment provider
app.post('/:id/payment-sessions/:provider_id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const providerId = c.req.param('provider_id');
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    // Find the payment session
    const sessions = await db.findMany('payment_session', {
      where: `cart_id = '${cartId}' AND provider_id = '${providerId}'`,
    });

    if (sessions.length === 0) {
      return c.json({
        success: false,
        error: 'Payment session not found',
      }, 404);
    }

    const session = sessions[0];

    // Deselect all other sessions
    const allSessions = await db.findMany('payment_session', {
      where: `cart_id = '${cartId}'`,
    });

    for (const s of allSessions) {
      await db.update('payment_session', s.id, {
        is_selected: s.id === session.id,
      });
    }

    // Invalidate cart cache
    await cache.invalidateCart(cartId);

    // Get updated session
    const updatedSession = await db.findById('payment_session', session.id);

    return c.json({
      success: true,
      data: updatedSession,
    });

  } catch (error) {
    console.error('Error selecting payment provider:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to select payment provider',
    }, 500);
  }
});

// POST /store/carts/:id/complete - Complete cart and create order
app.post('/:id/complete', async (c) => {
  try {
    const cartId = c.req.param('id');
    const db = new DatabaseService(c.env);
    const cache = new CacheService(c.env);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at || cart.completed_at) {
      return c.json({
        success: false,
        error: 'Cart not found or already completed',
      }, 404);
    }

    // Get enriched cart
    const enrichedCart = await enrichCart(cart, db);

    // Validate cart has required data
    if (!enrichedCart.items || enrichedCart.items.length === 0) {
      return c.json({
        success: false,
        error: 'Cart is empty',
      }, 400);
    }

    if (!enrichedCart.email) {
      return c.json({
        success: false,
        error: 'Email is required',
      }, 400);
    }

    if (!enrichedCart.shipping_address) {
      return c.json({
        success: false,
        error: 'Shipping address is required',
      }, 400);
    }

    // Check if payment is authorized
    const selectedPaymentSession = enrichedCart.payment_sessions.find((s: any) => s.is_selected);
    if (!selectedPaymentSession) {
      return c.json({
        success: false,
        error: 'No payment method selected',
      }, 400);
    }

    // Generate order display ID
    const orderCount = await db.count('order', '');
    const displayId = orderCount + 1;

    // Create order
    const orderData = {
      id: crypto.randomUUID(),
      status: 'pending',
      fulfillment_status: 'not_fulfilled',
      payment_status: selectedPaymentSession.provider_id === 'cash_on_delivery' ? 'not_paid' : 'awaiting',
      display_id: displayId,
      cart_id: cartId,
      customer_id: cart.customer_id,
      email: cart.email,
      billing_address_id: cart.billing_address_id,
      shipping_address_id: cart.shipping_address_id,
      region_id: cart.region_id,
      currency_code: enrichedCart.region?.currency_code || 'RON',
      sales_channel_id: null,
      metadata: cart.metadata,
    };

    const order = await db.create('order', orderData);

    // Move line items to order
    for (const item of enrichedCart.items) {
      await db.update('cart_line_item', item.id, {
        order_id: order.id,
        cart_id: null,
      });
    }

    // Move shipping methods to order
    for (const method of enrichedCart.shipping_methods) {
      await db.update('shipping_method', method.id, {
        order_id: order.id,
        cart_id: null,
      });
    }

    // Mark cart as completed
    await db.update('cart', cartId, {
      completed_at: new Date().toISOString(),
    });

    // Create payment if needed
    if (selectedPaymentSession.provider_id !== 'cash_on_delivery') {
      const paymentData = {
        id: crypto.randomUUID(),
        order_id: order.id,
        amount: enrichedCart.total,
        currency_code: orderData.currency_code,
        amount_refunded: 0,
        provider_id: selectedPaymentSession.provider_id,
        data: selectedPaymentSession.data,
      };

      await db.create('payment', paymentData);
    }

    // Invalidate caches
    await cache.invalidateCart(cartId);

    return c.json({
      success: true,
      data: {
        order_id: order.id,
        type: 'order',
      },
    });

  } catch (error) {
    console.error('Error completing cart:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to complete cart',
    }, 500);
  }
});

// POST /store/carts/:id/discounts - Apply discount code
app.post('/:id/discounts', async (c) => {
  try {
    const cartId = c.req.param('id');
    const { code } = await c.req.json();
    const db = new DatabaseService(c.env);

    if (!code) {
      return c.json({
        success: false,
        error: 'Discount code is required',
      }, 400);
    }

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    // Find promotion by code
    const promotions = await db.findMany('promotion', {
      where: `code = '${code.toUpperCase()}' AND (deleted_at IS NULL OR deleted_at = '') AND status = 'active'`,
      limit: 1
    });
    
    if (promotions.length === 0) {
      return c.json({
        success: false,
        error: 'Invalid discount code',
      }, 400);
    }

    const promotion = promotions[0];

    // Check if promotion is already applied to cart
    const existingCartPromotions = await db.findMany('cart_promotion', {
      where: `cart_id = '${cartId}' AND promotion_id = '${promotion.id}' AND (deleted_at IS NULL OR deleted_at = '')`,
      limit: 1
    });

    if (existingCartPromotions.length > 0) {
      return c.json({
        success: false,
        error: 'Discount code already applied',
      }, 400);
    }

    // Apply promotion to cart
    await db.create('cart_promotion', {
      id: crypto.randomUUID(),
      cart_id: cartId,
      promotion_id: promotion.id,
    });

    // Get enriched cart with new promotion
    const enrichedCart = await enrichCart(cart, db);

    return c.json({
      success: true,
      data: {
        cart: enrichedCart,
      },
    });
  } catch (error) {
    console.error('Apply discount error:', error);
    return c.json({
      success: false,
      error: 'Failed to apply discount code',
    }, 500);
  }
});

// DELETE /store/carts/:id/discounts/:code - Remove discount code
app.delete('/:id/discounts/:code', async (c) => {
  try {
    const cartId = c.req.param('id');
    const code = c.req.param('code');
    const db = new DatabaseService(c.env);

    // Verify cart exists
    const cart = await db.findById('cart', cartId);
    if (!cart || cart.deleted_at) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    // Find promotion by code
    const promotions = await db.findMany('promotion', {
      where: `code = '${code.toUpperCase()}' AND (deleted_at IS NULL OR deleted_at = '')`,
      limit: 1
    });
    
    if (promotions.length === 0) {
      return c.json({
        success: false,
        error: 'Discount code not found',
      }, 404);
    }

    const promotion = promotions[0];

    // Find and remove cart promotion
    const cartPromotions = await db.findMany('cart_promotion', {
      where: `cart_id = '${cartId}' AND promotion_id = '${promotion.id}' AND (deleted_at IS NULL OR deleted_at = '')`,
      limit: 1
    });

    if (cartPromotions.length === 0) {
      return c.json({
        success: false,
        error: 'Discount code not applied to this cart',
      }, 404);
    }

    const cartPromotion = cartPromotions[0];

    // Soft delete the cart promotion
    await db.update('cart_promotion', cartPromotion.id, {
      deleted_at: new Date().toISOString(),
    });

    // Remove any adjustments that were applied by this promotion
    const lineItemAdjustments = await db.findMany('cart_line_item_adjustment', {
      where: `promotion_id = '${promotion.id}' AND (deleted_at IS NULL OR deleted_at = '')`
    });

    for (const adjustment of lineItemAdjustments) {
      // Check if this adjustment belongs to an item in this cart
      const lineItem = await db.findById('cart_line_item', adjustment.item_id);
      if (lineItem && lineItem.cart_id === cartId) {
        await db.update('cart_line_item_adjustment', adjustment.id, {
          deleted_at: new Date().toISOString(),
        });
      }
    }

    // Remove shipping method adjustments
    const shippingMethodAdjustments = await db.findMany('cart_shipping_method_adjustment', {
      where: `promotion_id = '${promotion.id}' AND (deleted_at IS NULL OR deleted_at = '')`
    });

    for (const adjustment of shippingMethodAdjustments) {
      // Check if this adjustment belongs to a shipping method in this cart
      const shippingMethod = await db.findById('cart_shipping_method', adjustment.shipping_method_id);
      if (shippingMethod && shippingMethod.cart_id === cartId) {
        await db.update('cart_shipping_method_adjustment', adjustment.id, {
          deleted_at: new Date().toISOString(),
        });
      }
    }

    // Get enriched cart without the promotion
    const enrichedCart = await enrichCart(cart, db);

    return c.json({
      success: true,
      data: {
        cart: enrichedCart,
      },
    });
  } catch (error) {
    console.error('Remove discount error:', error);
    return c.json({
      success: false,
      error: 'Failed to remove discount code',
    }, 500);
  }
});

export { app as cartRoutes }; 