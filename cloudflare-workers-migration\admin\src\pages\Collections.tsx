import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  EllipsisVerticalIcon,
} from '@heroicons/react/24/outline';
import { collectionsApi } from '../lib/api';
import { toast } from 'react-hot-toast';
import clsx from 'clsx';

interface Collection {
  id: string;
  title: string;
  handle: string;
  description?: string;
  metadata: any;
  created_at: string;
  updated_at: string;
  primary_image?: {
    id: string;
    url: string;
    alt_text?: string;
  };
  products_count?: number;
}

const Collections: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCollections, setSelectedCollections] = useState<string[]>([]);

  const queryClient = useQueryClient();

  // Fetch collections
  const { data: collectionsData, isLoading, error } = useQuery({
    queryKey: ['collections', searchTerm],
    queryFn: async () => {
      const response = await collectionsApi.list();
      console.log('Collections response:', response.data);
      return response.data;
    },
  });

  // Delete collection mutation
  const deleteCollectionMutation = useMutation({
    mutationFn: (collectionId: string) => collectionsApi.delete(collectionId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['collections'] });
      toast.success('Collection deleted successfully');
    },
    onError: () => {
      toast.error('Failed to delete collection');
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: (collectionIds: string[]) => 
      collectionsApi.bulkDelete(collectionIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['collections'] });
      setSelectedCollections([]);
      toast.success('Collections deleted successfully');
    },
    onError: () => {
      toast.error('Failed to delete collections');
    },
  });

  const handleDeleteCollection = (collectionId: string) => {
    if (window.confirm('Are you sure you want to delete this collection?')) {
      deleteCollectionMutation.mutate(collectionId);
    }
  };

  const handleBulkDelete = () => {
    if (selectedCollections.length === 0) return;
    if (window.confirm(`Are you sure you want to delete ${selectedCollections.length} collections?`)) {
      bulkDeleteMutation.mutate(selectedCollections);
    }
  };

  const handleSelectAll = () => {
    if (selectedCollections.length === collectionsData?.collections?.length) {
      setSelectedCollections([]);
    } else {
      setSelectedCollections(collectionsData?.collections?.map((c: Collection) => c.id) || []);
    }
  };

  const handleSelectCollection = (collectionId: string) => {
    setSelectedCollections(prev => 
      prev.includes(collectionId) 
        ? prev.filter(id => id !== collectionId)
        : [...prev, collectionId]
    );
  };

  const collections = collectionsData?.collections || [];

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-800">
          Error loading collections. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-gray-900">Collections</h1>
          <p className="mt-2 text-sm text-gray-700">
            Organize your products into collections for better navigation and discovery
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Link
            to="/collections/new"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
            Create Collection
          </Link>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {/* Search */}
            <div className="col-span-1 sm:col-span-2">
              <label htmlFor="search" className="sr-only">Search collections</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="search"
                  placeholder="Search collections..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedCollections.length > 0 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-700">
                  {selectedCollections.length} collection{selectedCollections.length !== 1 ? 's' : ''} selected
                </span>
                <button
                  onClick={handleBulkDelete}
                  className="inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Delete
                </button>
              </div>
              <button
                onClick={() => setSelectedCollections([])}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Clear selection
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Collections Grid */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {isLoading ? (
          <div className="p-6">
            <div className="animate-pulse">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-gray-200 rounded-lg h-48"></div>
                ))}
              </div>
            </div>
          </div>
        ) : collections.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <h3 className="mt-2 text-sm font-medium text-gray-900">No collections</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by creating a new collection.</p>
              <div className="mt-6">
                <Link
                  to="/collections/new"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                  Create Collection
                </Link>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Select All Header */}
            <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  checked={selectedCollections.length === collections.length}
                  onChange={handleSelectAll}
                />
                <label className="ml-3 text-sm text-gray-700">
                  Select all collections
                </label>
              </div>
            </div>

            {/* Collections Grid */}
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {collections.map((collection: Collection) => (
                  <div key={collection.id} className="relative group">
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                      {/* Selection checkbox */}
                      <div className="absolute top-3 left-3 z-10">
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                          checked={selectedCollections.includes(collection.id)}
                          onChange={() => handleSelectCollection(collection.id)}
                        />
                      </div>

                      {/* Image */}
                      <div className="h-48 bg-gray-200 flex items-center justify-center">
                        {collection.primary_image ? (
                          <img
                            src={collection.primary_image.url}
                            alt={collection.primary_image.alt_text || collection.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="text-gray-400 text-center">
                            <div className="w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-lg flex items-center justify-center">
                              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <p className="text-sm">No image</p>
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="p-4">
                        <h3 className="text-lg font-medium text-gray-900 mb-1">
                          {collection.title}
                        </h3>
                        <p className="text-sm text-gray-500 mb-2">
                          {collection.handle}
                        </p>
                        {collection.description && (
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                            {collection.description}
                          </p>
                        )}
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500">
                            {collection.products_count || 0} products
                          </span>
                          <div className="flex items-center space-x-2">
                            <Link
                              to={`/collections/${collection.id}`}
                              className="text-gray-400 hover:text-gray-500"
                            >
                              <EyeIcon className="h-5 w-5" />
                            </Link>
                            <Link
                              to={`/collections/${collection.id}/edit`}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              <PencilIcon className="h-5 w-5" />
                            </Link>
                            <button
                              onClick={() => handleDeleteCollection(collection.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Collections; 