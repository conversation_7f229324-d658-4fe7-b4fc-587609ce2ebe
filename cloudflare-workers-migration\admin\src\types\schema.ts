// TypeScript interfaces for the simplified ecommerce schema
// This file defines all the data types used in the admin interface

// ===========================================
// CORE SYSTEM TYPES
// ===========================================

export interface User {
  id: string;
  email: string;
  password_hash?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  role: 'admin' | 'manager' | 'editor';
  is_active: boolean;
  last_login_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface ApiKey {
  id: string;
  name: string;
  key_hash: string;
  key_prefix: string;
  permissions: string[];
  expires_at?: string;
  last_used_at?: string;
  created_by: string;
  created_at: string;
  revoked_at?: string;
}

export interface Setting {
  key: string;
  value: any;
  category: string;
  is_public: boolean;
  updated_by?: string;
  updated_at: string;
}

// ===========================================
// LOCALIZATION & REGIONS
// ===========================================

export interface Currency {
  code: string;
  name: string;
  symbol: string;
  decimal_places: number;
  exchange_rate: number;
  is_default: boolean;
  is_active: boolean;
  updated_at: string;
}

export interface Language {
  code: string;
  name: string;
  native_name: string;
  is_default: boolean;
  is_active: boolean;
  direction: 'ltr' | 'rtl';
  created_at: string;
}

export interface Region {
  id: string;
  name: string;
  code: string;
  currency_code: string;
  language_code: string;
  tax_rate?: number;
  tax_inclusive: boolean;
  countries: string[];
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// ===========================================
// PRODUCTS & CATALOG
// ===========================================

export interface Collection {
  id: string;
  handle: string;
  sort_order: number;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  
  // Translations
  translations?: CollectionTranslation[];
}

export interface CollectionTranslation {
  collection_id: string;
  language_code: string;
  title: string;
  description?: string;
  seo_title?: string;
  seo_description?: string;
}

export interface Category {
  id: string;
  parent_id?: string;
  handle: string;
  sort_order: number;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  
  // Relations
  parent?: Category;
  children?: Category[];
  translations?: CategoryTranslation[];
}

export interface CategoryTranslation {
  category_id: string;
  language_code: string;
  name: string;
  description?: string;
  seo_title?: string;
  seo_description?: string;
}

export interface ProductType {
  id: string;
  name: string;
  metadata?: Record<string, any>;
  created_at: string;
}

export interface Product {
  id: string;
  handle: string;
  status: 'draft' | 'published' | 'archived';
  type_id?: string;
  collection_id?: string;
  thumbnail?: string;
  weight?: number;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
    unit?: string;
  };
  origin_country?: string;
  hs_code?: string;
  requires_shipping: boolean;
  is_giftcard: boolean;
  is_discountable: boolean;
  tags: string[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  
  // Relations
  type?: ProductType;
  collection?: Collection;
  translations?: ProductTranslation[];
  categories?: Category[];
  images?: ProductImage[];
  options?: ProductOption[];
  variants?: ProductVariant[];
}

export interface ProductTranslation {
  product_id: string;
  language_code: string;
  title: string;
  subtitle?: string;
  description?: string;
  seo_title?: string;
  seo_description?: string;
}

export interface ProductImage {
  id: string;
  product_id: string;
  url: string;
  alt_text?: string;
  sort_order: number;
  metadata?: Record<string, any>;
  created_at: string;
}

export interface ProductOption {
  id: string;
  product_id: string;
  name: string;
  type: 'text' | 'color' | 'image';
  sort_order: number;
  created_at: string;
  
  // Relations
  values?: ProductOptionValue[];
}

export interface ProductOptionValue {
  id: string;
  option_id: string;
  value: string;
  hex_color?: string;
  image_url?: string;
  sort_order: number;
  created_at: string;
}

export interface ProductVariant {
  id: string;
  product_id: string;
  title: string;
  sku?: string;
  barcode?: string;
  weight?: number;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
    unit?: string;
  };
  sort_order: number;
  option_values: string[];
  manage_inventory: boolean;
  allow_backorder: boolean;
  inventory_quantity: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  
  // Relations
  prices?: VariantPrice[];
}

export interface VariantPrice {
  id: string;
  variant_id: string;
  currency_code: string;
  region_id?: string;
  price: number;
  compare_at_price?: number;
  cost_price?: number;
  min_quantity: number;
  max_quantity?: number;
  starts_at?: string;
  ends_at?: string;
  created_at: string;
  updated_at: string;
}

// ===========================================
// CUSTOMERS & ACCOUNTS
// ===========================================

export interface CustomerGroup {
  id: string;
  name: string;
  description?: string;
  discount_percentage: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface Customer {
  id: string;
  email?: string;
  phone?: string;
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  group_id?: string;
  accepts_marketing: boolean;
  tax_exempt: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  
  // Relations
  group?: CustomerGroup;
  addresses?: CustomerAddress[];
  orders?: Order[];
}

export interface CustomerAddress {
  id: string;
  customer_id: string;
  type: 'shipping' | 'billing' | 'both';
  is_default: boolean;
  first_name?: string;
  last_name?: string;
  company?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state?: string;
  postal_code: string;
  country_code: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}

// ===========================================
// ORDERS & TRANSACTIONS
// ===========================================

export interface Cart {
  id: string;
  customer_id?: string;
  email?: string;
  currency_code: string;
  region_id: string;
  billing_address?: Record<string, any>;
  shipping_address?: Record<string, any>;
  notes?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  abandoned_at?: string;
  
  // Relations
  items?: CartItem[];
}

export interface CartItem {
  id: string;
  cart_id: string;
  variant_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;

  // Relations
  variant?: ProductVariant;
}

export interface Order {
  id: string;
  number: string;
  customer_id?: string;
  email: string;
  currency_code: string;
  region_id: string;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  financial_status: 'pending' | 'paid' | 'partially_paid' | 'refunded' | 'partially_refunded';
  fulfillment_status: 'unfulfilled' | 'partial' | 'fulfilled';
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total_amount: number;
  billing_address: Record<string, any>;
  shipping_address: Record<string, any>;
  notes?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  cancelled_at?: string;

  // Relations
  customer?: Customer;
  items?: OrderItem[];
  payments?: Payment[];
  fulfillments?: Fulfillment[];
}

export interface OrderItem {
  id: string;
  order_id: string;
  variant_id: string;
  product_title: string;
  variant_title: string;
  sku?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  fulfilled_quantity: number;
  refunded_quantity: number;
  metadata?: Record<string, any>;
  created_at: string;

  // Relations
  variant?: ProductVariant;
}

export interface Payment {
  id: string;
  order_id: string;
  provider: string;
  provider_transaction_id?: string;
  type: 'payment' | 'refund';
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  amount: number;
  currency_code: string;
  gateway_data?: Record<string, any>;
  processed_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

// ===========================================
// SHIPPING & FULFILLMENT
// ===========================================

export interface ShippingZone {
  id: string;
  name: string;
  countries: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;

  // Relations
  methods?: ShippingMethod[];
}

export interface ShippingMethod {
  id: string;
  zone_id: string;
  name: string;
  description?: string;
  provider?: string;
  price_type: 'fixed' | 'calculated' | 'free';
  price: number;
  currency_code: string;
  min_order_amount?: number;
  max_order_amount?: number;
  estimated_delivery_days?: number;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface Fulfillment {
  id: string;
  order_id: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  tracking_number?: string;
  tracking_url?: string;
  carrier?: string;
  shipping_method?: string;
  shipped_at?: string;
  delivered_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;

  // Relations
  items?: FulfillmentItem[];
}

export interface FulfillmentItem {
  fulfillment_id: string;
  order_item_id: string;
  quantity: number;
}

// ===========================================
// RETURNS & EXCHANGES
// ===========================================

export interface ReturnReason {
  id: string;
  label: string;
  description?: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface Return {
  id: string;
  order_id: string;
  status: 'requested' | 'approved' | 'received' | 'processing' | 'completed' | 'cancelled';
  return_number: string;
  customer_note?: string;
  admin_note?: string;
  refund_amount: number;
  shipping_cost: number;
  total_amount: number;
  currency_code: string;
  return_address?: Record<string, any>;
  tracking_number?: string;
  tracking_url?: string;
  received_at?: string;
  processed_at?: string;
  completed_at?: string;
  cancelled_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;

  // Relations
  order?: Order;
  items?: ReturnItem[];
  refunds?: ReturnRefund[];
}

export interface ReturnItem {
  id: string;
  return_id: string;
  order_item_id: string;
  reason_id: string;
  quantity: number;
  received_quantity: number;
  condition?: 'new' | 'used' | 'damaged' | 'defective';
  note?: string;
  refund_amount: number;
  created_at: string;
  updated_at: string;

  // Relations
  order_item?: OrderItem;
  reason?: ReturnReason;
}

export interface Exchange {
  id: string;
  order_id: string;
  return_id?: string;
  status: 'requested' | 'approved' | 'processing' | 'shipped' | 'completed' | 'cancelled';
  exchange_number: string;
  customer_note?: string;
  admin_note?: string;
  difference_amount: number;
  shipping_cost: number;
  currency_code: string;
  shipping_address?: Record<string, any>;
  tracking_number?: string;
  tracking_url?: string;
  shipped_at?: string;
  completed_at?: string;
  cancelled_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;

  // Relations
  order?: Order;
  return?: Return;
  return_items?: ExchangeReturnItem[];
  new_items?: ExchangeNewItem[];
}

export interface ExchangeReturnItem {
  id: string;
  exchange_id: string;
  order_item_id: string;
  reason_id?: string;
  quantity: number;
  condition?: string;
  note?: string;
  created_at: string;
}

export interface ExchangeNewItem {
  id: string;
  exchange_id: string;
  variant_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  note?: string;
  created_at: string;
}

export interface ReturnRefund {
  id: string;
  return_id?: string;
  exchange_id?: string;
  payment_id?: string;
  amount: number;
  reason: 'return' | 'exchange_difference' | 'shipping_refund';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  payment_method?: string;
  transaction_id?: string;
  processed_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// ===========================================
// PROMOTIONS & DISCOUNTS
// ===========================================

export interface DiscountCode {
  id: string;
  code: string;
  type: 'percentage' | 'fixed_amount' | 'free_shipping';
  value: number;
  currency_code?: string;
  min_order_amount?: number;
  max_discount_amount?: number;
  usage_limit?: number;
  usage_count: number;
  customer_usage_limit: number;
  applies_to: 'all' | 'specific_products' | 'specific_collections';
  applies_to_ids: string[];
  starts_at?: string;
  ends_at?: string;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;

  // Relations
  usage?: DiscountUsage[];
}

export interface DiscountUsage {
  id: string;
  discount_id: string;
  order_id: string;
  customer_id?: string;
  amount_saved: number;
  used_at: string;
}

// ===========================================
// CONTENT MANAGEMENT
// ===========================================

export interface Post {
  id: string;
  slug: string;
  status: 'draft' | 'published' | 'archived';
  author_id: string;
  featured_image?: string;
  excerpt?: string;
  tags: string[];
  metadata?: Record<string, any>;
  published_at?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;

  // Relations
  author?: User;
  translations?: PostTranslation[];
}

export interface PostTranslation {
  post_id: string;
  language_code: string;
  title: string;
  content: string;
  seo_title?: string;
  seo_description?: string;
}

export interface Page {
  id: string;
  slug: string;
  template: string;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string;

  // Relations
  translations?: PageTranslation[];
}

export interface PageTranslation {
  page_id: string;
  language_code: string;
  title: string;
  content: string;
  seo_title?: string;
  seo_description?: string;
}

// ===========================================
// FILE MANAGEMENT
// ===========================================

export interface File {
  id: string;
  original_name: string;
  filename: string;
  mime_type: string;
  size: number;
  folder: string;
  url: string;
  thumbnail_url?: string;
  alt_text?: string;
  metadata?: Record<string, any>;
  uploaded_by?: string;
  created_at: string;
}

// ===========================================
// ANALYTICS & TRACKING
// ===========================================

export interface AnalyticsEvent {
  id: string;
  event_type: string;
  customer_id?: string;
  session_id?: string;
  product_id?: string;
  variant_id?: string;
  order_id?: string;
  page_url?: string;
  user_agent?: string;
  ip_address?: string;
  country?: string;
  referrer?: string;
  event_data?: Record<string, any>;
  created_at: string;
}

// ===========================================
// WEBHOOKS & INTEGRATIONS
// ===========================================

export interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  is_active: boolean;
  secret?: string;
  last_error?: string;
  last_success_at?: string;
  created_at: string;
  updated_at: string;

  // Relations
  deliveries?: WebhookDelivery[];
}

export interface WebhookDelivery {
  id: string;
  webhook_id: string;
  event_type: string;
  payload: Record<string, any>;
  response_status?: number;
  response_body?: string;
  delivered_at?: string;
  attempts: number;
  created_at: string;
}

// ===========================================
// API RESPONSE TYPES
// ===========================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// ===========================================
// FORM DATA TYPES
// ===========================================

export interface ProductFormData {
  title: string;
  subtitle?: string;
  description?: string;
  handle: string;
  status: 'draft' | 'published' | 'archived';
  thumbnail?: string;
  collection_id?: string;
  type_id?: string;
  weight?: number;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
    unit?: string;
  };
  origin_country?: string;
  hs_code?: string;
  requires_shipping: boolean;
  is_giftcard: boolean;
  is_discountable: boolean;
  tags: string[];
  metadata?: Record<string, any>;

  // SEO
  seo_title?: string;
  seo_description?: string;

  // Categories
  category_ids?: string[];

  // Variants
  variants?: ProductVariantFormData[];

  // Images
  images?: ProductImageFormData[];

  // Options
  options?: ProductOptionFormData[];
}

export interface ProductVariantFormData {
  id?: string;
  title: string;
  sku?: string;
  barcode?: string;
  weight?: number;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
    unit?: string;
  };
  sort_order: number;
  option_values: string[];
  manage_inventory: boolean;
  allow_backorder: boolean;
  inventory_quantity: number;
  metadata?: Record<string, any>;

  // Pricing
  prices?: VariantPriceFormData[];
}

export interface VariantPriceFormData {
  id?: string;
  currency_code: string;
  region_id?: string;
  price: number;
  compare_at_price?: number;
  cost_price?: number;
  min_quantity: number;
  max_quantity?: number;
  starts_at?: string;
  ends_at?: string;
}

export interface ProductImageFormData {
  id?: string;
  url: string;
  alt_text?: string;
  sort_order: number;
  metadata?: Record<string, any>;
}

export interface ProductOptionFormData {
  id?: string;
  name: string;
  type: 'text' | 'color' | 'image';
  sort_order: number;
  values?: ProductOptionValueFormData[];
}

export interface ProductOptionValueFormData {
  id?: string;
  value: string;
  hex_color?: string;
  image_url?: string;
  sort_order: number;
}
