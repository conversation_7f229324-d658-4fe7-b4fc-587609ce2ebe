import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { Context } from 'hono';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Utility functions
function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error(error);
  return c.json({ error: message, details: error.message }, 500);
}

function safeParseJson(value: any): any {
  if (!value) return null;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }
  return value;
}

// GET /store/products - List products with filtering and search
app.get('/', async (c) => {
  try {
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = parseInt(c.req.query('offset') || '0');
    const page = Math.floor(offset / limit) + 1;
    const search = c.req.query('q') || '';
    const collection_id = c.req.query('collection_id') || '';
    const category_id = c.req.query('category_id') || '';
    const type_id = c.req.query('type_id') || '';
    const tags = c.req.query('tags') || '';
    const featured = c.req.query('featured');
    const sort = c.req.query('sort') || 'created_at';
    const order = c.req.query('order') || 'desc';
    const currency_code = c.req.query('currency_code') || 'RON';
    const region_id = c.req.query('region_id') || '';

    // Build query conditions
    let query = `
      SELECT
        p.id, p.handle, p.status, p.collection_id, p.type_id, p.thumbnail,
        p.weight, p.dimensions, p.origin_country, p.hs_code, p.requires_shipping,
        p.is_giftcard, p.is_discountable, p.tags, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description, pt.seo_title, pt.seo_description,
        c.handle as collection_handle,
        ct.title as collection_title,
        ptype.name as type_name,
        COUNT(DISTINCT pv.id) as variant_count
      FROM products p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      LEFT JOIN collections c ON p.collection_id = c.id
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'en'
      LEFT JOIN product_types ptype ON p.type_id = ptype.id
      LEFT JOIN product_variants pv ON p.id = pv.product_id AND pv.deleted_at IS NULL
      LEFT JOIN product_categories pc ON p.id = pc.product_id
      WHERE p.deleted_at IS NULL AND p.status = 'published'
    `;

    const params: any[] = [];

    // Add search filter
    if (search) {
      query += ` AND (pt.title LIKE ? OR pt.description LIKE ? OR p.handle LIKE ? OR p.tags LIKE ?)`;
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Add collection filter
    if (collection_id) {
      query += ` AND p.collection_id = ?`;
      params.push(collection_id);
    }

    // Add category filter
    if (category_id) {
      query += ` AND pc.category_id = ?`;
      params.push(category_id);
    }

    // Add type filter
    if (type_id) {
      query += ` AND p.type_id = ?`;
      params.push(type_id);
    }

    // Add tags filter
    if (tags) {
      query += ` AND p.tags LIKE ?`;
      params.push(`%${tags}%`);
    }

    // Add featured filter (if metadata contains featured flag)
    if (featured === 'true') {
      query += ` AND (p.metadata LIKE '%"featured":true%' OR p.metadata LIKE '%"featured":"true"%')`;
    }

    // Group by product
    query += ` GROUP BY p.id, pt.title, pt.subtitle, pt.description, pt.seo_title, pt.seo_description, c.handle, ct.title, ptype.name`;

    // Add sorting
    const validSorts = ['created_at', 'updated_at', 'title', 'price'];
    const validOrders = ['asc', 'desc'];
    const sortField = validSorts.includes(sort) ? sort : 'created_at';
    const sortOrder = validOrders.includes(order) ? order : 'desc';

    if (sortField === 'title') {
      query += ` ORDER BY pt.title ${sortOrder.toUpperCase()}`;
    } else if (sortField === 'price') {
      // For price sorting, we'll need to join with variant prices
      query += ` ORDER BY (
        SELECT MIN(vp.price)
        FROM product_variants pv2
        LEFT JOIN variant_prices vp ON pv2.id = vp.variant_id AND vp.currency_code = ?
        WHERE pv2.product_id = p.id AND pv2.deleted_at IS NULL
      ) ${sortOrder.toUpperCase()}`;
      params.push(currency_code);
    } else {
      query += ` ORDER BY p.${sortField} ${sortOrder.toUpperCase()}`;
    }

    // Add pagination
    query += ` LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    // Get products
    const products = await c.env.DB.prepare(query).bind(...params).all();

    // Get total count
    let countQuery = `
      SELECT COUNT(DISTINCT p.id) as total
      FROM products p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      WHERE p.deleted_at IS NULL AND p.status = 'published'
    `;

    const countParams: any[] = [];
    if (search) {
      countQuery += ` AND (pt.title LIKE ? OR pt.description LIKE ? OR p.handle LIKE ?)`;
      const searchTerm = `%${search}%`;
      countParams.push(searchTerm, searchTerm, searchTerm);
    }
    if (collection_id) {
      countQuery += ` AND p.collection_id = ?`;
      countParams.push(collection_id);
    }

    const countResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();
    const total = (countResult as any)?.total || 0;

    // Enrich products with variants, images, and pricing
    const enrichedProducts = [];
    for (const product of products.results || []) {
      const productData = product as any;

      // Get variants with pricing
      const variantsQuery = `
        SELECT 
          pv.id, pv.title, pv.sku, pv.barcode, pv.weight, pv.dimensions,
          pv.sort_order, pv.option_values, pv.manage_inventory, pv.allow_backorder,
          pv.inventory_quantity, pv.metadata, pv.created_at, pv.updated_at,
          vp.price, vp.compare_at_price, vp.cost_price, vp.currency_code
        FROM product_variants pv
        LEFT JOIN variant_prices vp ON pv.id = vp.variant_id 
          AND vp.currency_code = ? 
          AND (vp.region_id = ? OR vp.region_id IS NULL)
        WHERE pv.product_id = ? AND pv.deleted_at IS NULL
        ORDER BY vp.region_id DESC, pv.sort_order ASC, pv.created_at ASC
      `;
      
      const variants = await c.env.DB.prepare(variantsQuery)
        .bind(currency_code, region_id || null, productData.id)
        .all();

      // Get images
      const imagesQuery = `
        SELECT id, url, alt_text, sort_order, metadata
        FROM product_images
        WHERE product_id = ?
        ORDER BY sort_order ASC, created_at ASC
      `;
      const images = await c.env.DB.prepare(imagesQuery).bind(productData.id).all();

      // Parse JSON fields
      productData.dimensions = safeParseJson(productData.dimensions);
      productData.tags = safeParseJson(productData.tags) || [];
      productData.metadata = safeParseJson(productData.metadata) || {};

      // Process variants
      const processedVariants = (variants.results || []).map((variant: any) => {
        variant.dimensions = safeParseJson(variant.dimensions);
        variant.option_values = safeParseJson(variant.option_values) || [];
        variant.metadata = safeParseJson(variant.metadata) || {};
        
        // Ensure price is a number (convert from cents to currency units)
        variant.price = variant.price ? parseInt(variant.price) : 0;
        variant.compare_at_price = variant.compare_at_price ? parseInt(variant.compare_at_price) : null;
        variant.cost_price = variant.cost_price ? parseInt(variant.cost_price) : null;
        
        // Add calculated_price structure for frontend compatibility
        variant.calculated_price = {
          calculated_amount: variant.price,
          original_amount: variant.price,
          currency_code: variant.currency_code || currency_code,
          compare_at_price: variant.compare_at_price
        };

        return variant;
      });

      // Process images
      const processedImages = (images.results || []).map((image: any) => {
        image.metadata = safeParseJson(image.metadata) || {};
        return image;
      });

      // Add product-level pricing from first variant
      const firstVariant = processedVariants[0];
      if (firstVariant) {
        productData.price = firstVariant.price;
        productData.currency_code = firstVariant.currency_code || currency_code;
        productData.compare_at_price = firstVariant.compare_at_price;
      }

      enrichedProducts.push({
        ...productData,
        variants: processedVariants,
        images: processedImages
      });
    }

    return c.json({
      success: true,
      data: enrichedProducts,
      count: total,
      limit: limit,
      offset: offset,
      page: page,
      pages: Math.ceil(total / limit)
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch products');
  }
});

// GET /store/products/:handle - Get single product by handle or ID
app.get('/:handle', async (c) => {
  try {
    const handle = c.req.param('handle');
    const currency_code = c.req.query('currency_code') || 'USD';
    const region_id = c.req.query('region_id') || '';

    // Get product by handle or ID
    let productQuery = `
      SELECT 
        p.id, p.handle, p.status, p.collection_id, p.type_id, p.thumbnail,
        p.weight, p.dimensions, p.requires_shipping, p.is_giftcard, p.is_discountable,
        p.tags, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description, pt.seo_title, pt.seo_description,
        c.handle as collection_handle,
        ct.title as collection_title,
        ptype.name as type_name
      FROM products p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      LEFT JOIN collections c ON p.collection_id = c.id
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'en'
      LEFT JOIN product_types ptype ON p.type_id = ptype.id
      WHERE p.deleted_at IS NULL AND p.status = 'published'
      AND (p.handle = ? OR p.id = ?)
    `;

    const product = await c.env.DB.prepare(productQuery).bind(handle, handle).first();

    if (!product) {
      return c.json({ success: false, error: 'Product not found' }, 404);
    }

    const productData = product as any;

    // Get variants with pricing
    const variantsQuery = `
      SELECT 
        pv.id, pv.title, pv.sku, pv.barcode, pv.weight, pv.dimensions,
        pv.sort_order, pv.option_values, pv.manage_inventory, pv.allow_backorder,
        pv.inventory_quantity, pv.metadata, pv.created_at, pv.updated_at,
        vp.price, vp.compare_at_price, vp.cost_price, vp.currency_code
      FROM product_variants pv
      LEFT JOIN variant_prices vp ON pv.id = vp.variant_id 
        AND vp.currency_code = ? 
        AND (vp.region_id = ? OR vp.region_id IS NULL)
      WHERE pv.product_id = ? AND pv.deleted_at IS NULL
      ORDER BY vp.region_id DESC, pv.sort_order ASC, pv.created_at ASC
    `;
    
    const variants = await c.env.DB.prepare(variantsQuery)
      .bind(currency_code, region_id || null, productData.id)
      .all();

    // Get images
    const imagesQuery = `
      SELECT id, url, alt_text, sort_order, metadata
      FROM product_images
      WHERE product_id = ?
      ORDER BY sort_order ASC, created_at ASC
    `;
    const images = await c.env.DB.prepare(imagesQuery).bind(productData.id).all();

    // Get product options
    const optionsQuery = `
      SELECT po.id, po.name, po.type, po.sort_order,
             pov.id as value_id, pov.value, pov.hex_color, pov.image_url, pov.sort_order as value_sort_order
      FROM product_options po
      LEFT JOIN product_option_values pov ON po.id = pov.option_id
      WHERE po.product_id = ?
      ORDER BY po.sort_order ASC, pov.sort_order ASC
    `;
    const optionsResult = await c.env.DB.prepare(optionsQuery).bind(productData.id).all();

    // Process options
    const optionsMap = new Map();
    (optionsResult.results || []).forEach((row: any) => {
      if (!optionsMap.has(row.id)) {
        optionsMap.set(row.id, {
          id: row.id,
          name: row.name,
          type: row.type,
          sort_order: row.sort_order,
          values: []
        });
      }
      
      if (row.value_id) {
        optionsMap.get(row.id).values.push({
          id: row.value_id,
          value: row.value,
          hex_color: row.hex_color,
          image_url: row.image_url,
          sort_order: row.value_sort_order
        });
      }
    });

    const options = Array.from(optionsMap.values());

    // Parse JSON fields
    productData.dimensions = safeParseJson(productData.dimensions);
    productData.tags = safeParseJson(productData.tags) || [];
    productData.metadata = safeParseJson(productData.metadata) || {};

    // Process variants
    const processedVariants = (variants.results || []).map((variant: any) => {
      variant.dimensions = safeParseJson(variant.dimensions);
      variant.option_values = safeParseJson(variant.option_values) || [];
      variant.metadata = safeParseJson(variant.metadata) || {};
      
      // Ensure price is a number (convert from cents to currency units)
      variant.price = variant.price ? parseInt(variant.price) : 0;
      variant.compare_at_price = variant.compare_at_price ? parseInt(variant.compare_at_price) : null;
      variant.cost_price = variant.cost_price ? parseInt(variant.cost_price) : null;
      
      // Add calculated_price structure for frontend compatibility
      variant.calculated_price = {
        calculated_amount: variant.price,
        original_amount: variant.price,
        currency_code: variant.currency_code || currency_code,
        compare_at_price: variant.compare_at_price
      };

      return variant;
    });

    // Process images
    const processedImages = (images.results || []).map((image: any) => {
      image.metadata = safeParseJson(image.metadata) || {};
      return image;
    });

    // Add product-level pricing from first variant
    const firstVariant = processedVariants[0];
    if (firstVariant) {
      productData.price = firstVariant.price;
      productData.currency_code = firstVariant.currency_code || currency_code;
      productData.compare_at_price = firstVariant.compare_at_price;
    }

    return c.json({
      success: true,
      data: {
        ...productData,
        variants: processedVariants,
        images: processedImages,
        options: options
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product');
  }
});

// GET /store/products/variants/:variantId - Get single variant
app.get('/variants/:variantId', async (c) => {
  try {
    const variantId = c.req.param('variantId');
    const currency_code = c.req.query('currency_code') || 'USD';
    const region_id = c.req.query('region_id') || '';

    const variantQuery = `
      SELECT 
        pv.id, pv.product_id, pv.title, pv.sku, pv.barcode, pv.weight, pv.dimensions,
        pv.sort_order, pv.option_values, pv.manage_inventory, pv.allow_backorder,
        pv.inventory_quantity, pv.metadata, pv.created_at, pv.updated_at,
        vp.price, vp.compare_at_price, vp.cost_price, vp.currency_code,
        p.handle as product_handle,
        pt.title as product_title
      FROM product_variants pv
      LEFT JOIN variant_prices vp ON pv.id = vp.variant_id 
        AND vp.currency_code = ? 
        AND (vp.region_id = ? OR vp.region_id IS NULL)
      LEFT JOIN products p ON pv.product_id = p.id
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      WHERE pv.id = ? AND pv.deleted_at IS NULL
      ORDER BY vp.region_id DESC
      LIMIT 1
    `;

    const variant = await c.env.DB.prepare(variantQuery)
      .bind(currency_code, region_id || null, variantId)
      .first();

    if (!variant) {
      return c.json({ success: false, error: 'Variant not found' }, 404);
    }

    const variantData = variant as any;

    // Parse JSON fields
    variantData.dimensions = safeParseJson(variantData.dimensions);
    variantData.option_values = safeParseJson(variantData.option_values) || [];
    variantData.metadata = safeParseJson(variantData.metadata) || {};
    
    // Ensure price is a number
    variantData.price = variantData.price ? parseInt(variantData.price) : 0;
    variantData.compare_at_price = variantData.compare_at_price ? parseInt(variantData.compare_at_price) : null;
    variantData.cost_price = variantData.cost_price ? parseInt(variantData.cost_price) : null;
    
    // Add calculated_price structure for frontend compatibility
    variantData.calculated_price = {
      calculated_amount: variantData.price,
      original_amount: variantData.price,
      currency_code: variantData.currency_code || currency_code,
      compare_at_price: variantData.compare_at_price
    };

    return c.json({
      success: true,
      data: variantData
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch variant');
  }
});

// POST /store/products/batch - Get multiple products by IDs or handles
app.post('/batch', async (c) => {
  try {
    const body = await c.req.json();
    const { ids, region_id, currency_code = 'RON' } = body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return c.json({ success: false, error: 'Product IDs are required' }, 400);
    }

    // Build query for multiple products
    const placeholders = ids.map(() => '?').join(',');
    const query = `
      SELECT
        p.id, p.handle, p.status, p.collection_id, p.type_id, p.thumbnail,
        p.weight, p.dimensions, p.origin_country, p.hs_code, p.requires_shipping,
        p.is_giftcard, p.is_discountable, p.tags, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description, pt.seo_title, pt.seo_description,
        c.handle as collection_handle,
        ct.title as collection_title,
        ptype.name as type_name
      FROM products p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'en'
      LEFT JOIN collections c ON p.collection_id = c.id
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'en'
      LEFT JOIN product_types ptype ON p.type_id = ptype.id
      WHERE p.deleted_at IS NULL AND p.status = 'published'
      AND (p.id IN (${placeholders}) OR p.handle IN (${placeholders}))
    `;

    const queryParams = [...ids, ...ids]; // IDs for both id and handle matching
    const products = await c.env.DB.prepare(query).bind(...queryParams).all();

    // Enrich products with variants, images, and pricing
    const enrichedProducts = [];
    for (const product of products.results || []) {
      const productData = product as any;

      // Get variants with pricing
      const variantsQuery = `
        SELECT
          pv.id, pv.title, pv.sku, pv.barcode, pv.weight, pv.dimensions,
          pv.sort_order, pv.option_values, pv.manage_inventory, pv.allow_backorder,
          pv.inventory_quantity, pv.metadata, pv.created_at, pv.updated_at,
          vp.price, vp.compare_at_price, vp.cost_price, vp.currency_code
        FROM product_variants pv
        LEFT JOIN variant_prices vp ON pv.id = vp.variant_id
          AND vp.currency_code = ?
          AND (vp.region_id = ? OR vp.region_id IS NULL)
        WHERE pv.product_id = ? AND pv.deleted_at IS NULL
        ORDER BY vp.region_id DESC, pv.sort_order ASC, pv.created_at ASC
      `;

      const variants = await c.env.DB.prepare(variantsQuery)
        .bind(currency_code, region_id || null, productData.id)
        .all();

      // Get images
      const imagesQuery = `
        SELECT id, url, alt_text, sort_order, metadata
        FROM product_images
        WHERE product_id = ?
        ORDER BY sort_order ASC, created_at ASC
      `;
      const images = await c.env.DB.prepare(imagesQuery).bind(productData.id).all();

      // Parse JSON fields
      productData.dimensions = safeParseJson(productData.dimensions);
      productData.tags = safeParseJson(productData.tags) || [];
      productData.metadata = safeParseJson(productData.metadata) || {};

      // Process variants
      const processedVariants = (variants.results || []).map((variant: any) => {
        variant.dimensions = safeParseJson(variant.dimensions);
        variant.option_values = safeParseJson(variant.option_values) || [];
        variant.metadata = safeParseJson(variant.metadata) || {};

        // Ensure price is a number
        variant.price = variant.price ? parseInt(variant.price) : 0;
        variant.compare_at_price = variant.compare_at_price ? parseInt(variant.compare_at_price) : null;
        variant.cost_price = variant.cost_price ? parseInt(variant.cost_price) : null;

        // Add calculated_price structure for frontend compatibility
        variant.calculated_price = {
          calculated_amount: variant.price,
          original_amount: variant.price,
          currency_code: variant.currency_code || currency_code,
          compare_at_price: variant.compare_at_price
        };

        return variant;
      });

      // Process images
      const processedImages = (images.results || []).map((image: any) => {
        image.metadata = safeParseJson(image.metadata) || {};
        return image;
      });

      // Add product-level pricing from first variant
      const firstVariant = processedVariants[0];
      if (firstVariant) {
        productData.price = firstVariant.price;
        productData.currency_code = firstVariant.currency_code || currency_code;
        productData.compare_at_price = firstVariant.compare_at_price;
      }

      enrichedProducts.push({
        ...productData,
        variants: processedVariants,
        images: processedImages
      });
    }

    return c.json({
      success: true,
      data: enrichedProducts
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch products batch');
  }
});

// GET /store/products/regions - Get available regions
app.get('/regions', async (c) => {
  try {
    const query = `
      SELECT
        r.id, r.name, r.currency_code, r.tax_rate, r.metadata, r.created_at, r.updated_at,
        GROUP_CONCAT(rc.country_code) as countries
      FROM regions r
      LEFT JOIN region_countries rc ON r.id = rc.region_id
      WHERE r.deleted_at IS NULL
      GROUP BY r.id, r.name, r.currency_code, r.tax_rate, r.metadata, r.created_at, r.updated_at
      ORDER BY r.created_at ASC
    `;

    const regions = await c.env.DB.prepare(query).all();

    const processedRegions = (regions.results || []).map((region: any) => {
      region.metadata = safeParseJson(region.metadata) || {};
      region.countries = region.countries ? region.countries.split(',') : [];
      return region;
    });

    return c.json({
      success: true,
      data: processedRegions
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch regions');
  }
});

export const productRoutes = app;