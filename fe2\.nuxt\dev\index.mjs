import process from 'node:process';globalThis._importMeta_={url:import.meta.url,env:process.env};import { mkdirSync } from 'node:fs';
import { Server } from 'node:http';
import { tmpdir } from 'node:os';
import { join } from 'node:path';
import { parentPort, threadId } from 'node:worker_threads';
import { getRequestHeader, splitCookiesString, setResponseStatus, setResponseHeader, send, getRequestHeaders, defineEventHandler, getCookie, setCookie, handleCacheHeaders, createEvent, fetchWithEvent, isEvent, eventHandler, getResponseStatus, setResponseHeaders, setHeaders, sendRedirect, proxyRequest, createError, parseCookies, getQuery as getQuery$1, getRequestURL, lazyEventHandler, useBase, createApp, createRouter as createRouter$1, toNodeListener, getRouterParam, readBody, appendHeader, getResponseStatusText } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/h3/dist/index.mjs';
import * as crypto from 'crypto';
import { createHmac } from 'crypto';
import { CognitoIdentityProviderClient, ConfirmSignUpCommand, ResendConfirmationCodeCommand, GlobalSignOutCommand, InitiateAuthCommand, GetUserCommand, SignUpCommand, ListUsersCommand, ConfirmForgotPasswordCommand, ForgotPasswordCommand } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/@aws-sdk/client-cognito-identity-provider/dist-cjs/index.js';
import Medusa from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/@medusajs/medusa-js/dist/index.mjs';
import { getCurrentUser, signOut } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/aws-amplify/dist/esm/auth/index.mjs';
import jwt from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/jsonwebtoken/index.js';
import { getRequestDependencies, getPreloadLinks, getPrefetchLinks, createRenderer } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import { stringify, uneval } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/devalue/index.js';
import destr from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/destr/dist/index.mjs';
import { renderToString } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/vue/server-renderer/index.mjs';
import { propsToString, renderSSRHead } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/@unhead/ssr/dist/index.mjs';
import { createServerHead as createServerHead$1, CapoPlugin } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unhead/dist/index.mjs';
import { klona } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/klona/dist/index.mjs';
import defu, { defuFn } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/defu/dist/defu.mjs';
import { snakeCase } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/scule/dist/index.mjs';
import { createHooks } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/hookable/dist/index.mjs';
import { createFetch as createFetch$1, Headers as Headers$1 } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/ofetch/dist/node.mjs';
import { createCall, createFetch } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unenv/runtime/fetch/index.mjs';
import { AsyncLocalStorage } from 'node:async_hooks';
import { consola } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/consola/dist/index.mjs';
import { getContext } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unctx/dist/index.mjs';
import { captureRawStackTrace, parseRawStackTrace } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/errx/dist/index.js';
import { isVNode, unref, version } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/vue/index.mjs';
import { Amplify } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/aws-amplify/dist/esm/index.mjs';
import { cognitoUserPoolsTokenProvider } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/aws-amplify/dist/esm/auth/cognito/index.mjs';
import { basename, isAbsolute } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/pathe/dist/index.mjs';
import { getIcons } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/@iconify/utils/lib/index.mjs';
import { hash } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/ohash/dist/index.mjs';
import { createStorage, prefixStorage } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unstorage/dist/index.mjs';
import unstorage_47drivers_47fs from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unstorage/drivers/fs.mjs';
import { collections } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt/nuxt-icon-server-bundle.mjs';
import { fileURLToPath } from 'node:url';
import { ipxFSStorage, ipxHttpStorage, createIPX, createIPXH3Handler } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/ipx/dist/index.mjs';
import { toRouteMatcher, createRouter } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/radix3/dist/index.mjs';
import { defineHeadPlugin } from 'file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/@unhead/shared/dist/index.mjs';

const HASH_RE = /#/g;
const AMPERSAND_RE = /&/g;
const SLASH_RE = /\//g;
const EQUAL_RE = /=/g;
const PLUS_RE = /\+/g;
const ENC_CARET_RE = /%5e/gi;
const ENC_BACKTICK_RE = /%60/gi;
const ENC_PIPE_RE = /%7c/gi;
const ENC_SPACE_RE = /%20/gi;
function encode(text) {
  return encodeURI("" + text).replace(ENC_PIPE_RE, "|");
}
function encodeQueryValue(input) {
  return encode(typeof input === "string" ? input : JSON.stringify(input)).replace(PLUS_RE, "%2B").replace(ENC_SPACE_RE, "+").replace(HASH_RE, "%23").replace(AMPERSAND_RE, "%26").replace(ENC_BACKTICK_RE, "`").replace(ENC_CARET_RE, "^").replace(SLASH_RE, "%2F");
}
function encodeQueryKey(text) {
  return encodeQueryValue(text).replace(EQUAL_RE, "%3D");
}
function decode(text = "") {
  try {
    return decodeURIComponent("" + text);
  } catch {
    return "" + text;
  }
}
function decodeQueryKey(text) {
  return decode(text.replace(PLUS_RE, " "));
}
function decodeQueryValue(text) {
  return decode(text.replace(PLUS_RE, " "));
}

function parseQuery(parametersString = "") {
  const object = {};
  if (parametersString[0] === "?") {
    parametersString = parametersString.slice(1);
  }
  for (const parameter of parametersString.split("&")) {
    const s = parameter.match(/([^=]+)=?(.*)/) || [];
    if (s.length < 2) {
      continue;
    }
    const key = decodeQueryKey(s[1]);
    if (key === "__proto__" || key === "constructor") {
      continue;
    }
    const value = decodeQueryValue(s[2] || "");
    if (object[key] === void 0) {
      object[key] = value;
    } else if (Array.isArray(object[key])) {
      object[key].push(value);
    } else {
      object[key] = [object[key], value];
    }
  }
  return object;
}
function encodeQueryItem(key, value) {
  if (typeof value === "number" || typeof value === "boolean") {
    value = String(value);
  }
  if (!value) {
    return encodeQueryKey(key);
  }
  if (Array.isArray(value)) {
    return value.map((_value) => `${encodeQueryKey(key)}=${encodeQueryValue(_value)}`).join("&");
  }
  return `${encodeQueryKey(key)}=${encodeQueryValue(value)}`;
}
function stringifyQuery(query) {
  return Object.keys(query).filter((k) => query[k] !== void 0).map((k) => encodeQueryItem(k, query[k])).filter(Boolean).join("&");
}

const PROTOCOL_STRICT_REGEX = /^[\s\w\0+.-]{2,}:([/\\]{1,2})/;
const PROTOCOL_REGEX = /^[\s\w\0+.-]{2,}:([/\\]{2})?/;
const PROTOCOL_RELATIVE_REGEX = /^([/\\]\s*){2,}[^/\\]/;
const JOIN_LEADING_SLASH_RE = /^\.?\//;
function hasProtocol(inputString, opts = {}) {
  if (typeof opts === "boolean") {
    opts = { acceptRelative: opts };
  }
  if (opts.strict) {
    return PROTOCOL_STRICT_REGEX.test(inputString);
  }
  return PROTOCOL_REGEX.test(inputString) || (opts.acceptRelative ? PROTOCOL_RELATIVE_REGEX.test(inputString) : false);
}
function hasTrailingSlash(input = "", respectQueryAndFragment) {
  {
    return input.endsWith("/");
  }
}
function withoutTrailingSlash(input = "", respectQueryAndFragment) {
  {
    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || "/";
  }
}
function withTrailingSlash(input = "", respectQueryAndFragment) {
  {
    return input.endsWith("/") ? input : input + "/";
  }
}
function withoutBase(input, base) {
  if (isEmptyURL(base)) {
    return input;
  }
  const _base = withoutTrailingSlash(base);
  if (!input.startsWith(_base)) {
    return input;
  }
  const trimmed = input.slice(_base.length);
  return trimmed[0] === "/" ? trimmed : "/" + trimmed;
}
function withQuery(input, query) {
  const parsed = parseURL(input);
  const mergedQuery = { ...parseQuery(parsed.search), ...query };
  parsed.search = stringifyQuery(mergedQuery);
  return stringifyParsedURL(parsed);
}
function getQuery(input) {
  return parseQuery(parseURL(input).search);
}
function isEmptyURL(url) {
  return !url || url === "/";
}
function isNonEmptyURL(url) {
  return url && url !== "/";
}
function joinURL(base, ...input) {
  let url = base || "";
  for (const segment of input.filter((url2) => isNonEmptyURL(url2))) {
    if (url) {
      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, "");
      url = withTrailingSlash(url) + _segment;
    } else {
      url = segment;
    }
  }
  return url;
}
function joinRelativeURL(..._input) {
  const JOIN_SEGMENT_SPLIT_RE = /\/(?!\/)/;
  const input = _input.filter(Boolean);
  const segments = [];
  let segmentsDepth = 0;
  for (const i of input) {
    if (!i || i === "/") {
      continue;
    }
    for (const [sindex, s] of i.split(JOIN_SEGMENT_SPLIT_RE).entries()) {
      if (!s || s === ".") {
        continue;
      }
      if (s === "..") {
        if (segments.length === 1 && hasProtocol(segments[0])) {
          continue;
        }
        segments.pop();
        segmentsDepth--;
        continue;
      }
      if (sindex === 1 && segments[segments.length - 1]?.endsWith(":/")) {
        segments[segments.length - 1] += "/" + s;
        continue;
      }
      segments.push(s);
      segmentsDepth++;
    }
  }
  let url = segments.join("/");
  if (segmentsDepth >= 0) {
    if (input[0]?.startsWith("/") && !url.startsWith("/")) {
      url = "/" + url;
    } else if (input[0]?.startsWith("./") && !url.startsWith("./")) {
      url = "./" + url;
    }
  } else {
    url = "../".repeat(-1 * segmentsDepth) + url;
  }
  if (input[input.length - 1]?.endsWith("/") && !url.endsWith("/")) {
    url += "/";
  }
  return url;
}

const protocolRelative = Symbol.for("ufo:protocolRelative");
function parseURL(input = "", defaultProto) {
  const _specialProtoMatch = input.match(
    /^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i
  );
  if (_specialProtoMatch) {
    const [, _proto, _pathname = ""] = _specialProtoMatch;
    return {
      protocol: _proto.toLowerCase(),
      pathname: _pathname,
      href: _proto + _pathname,
      auth: "",
      host: "",
      search: "",
      hash: ""
    };
  }
  if (!hasProtocol(input, { acceptRelative: true })) {
    return parsePath(input);
  }
  const [, protocol = "", auth, hostAndPath = ""] = input.replace(/\\/g, "/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/) || [];
  let [, host = "", path = ""] = hostAndPath.match(/([^#/?]*)(.*)?/) || [];
  if (protocol === "file:") {
    path = path.replace(/\/(?=[A-Za-z]:)/, "");
  }
  const { pathname, search, hash } = parsePath(path);
  return {
    protocol: protocol.toLowerCase(),
    auth: auth ? auth.slice(0, Math.max(0, auth.length - 1)) : "",
    host,
    pathname,
    search,
    hash,
    [protocolRelative]: !protocol
  };
}
function parsePath(input = "") {
  const [pathname = "", search = "", hash = ""] = (input.match(/([^#?]*)(\?[^#]*)?(#.*)?/) || []).splice(1);
  return {
    pathname,
    search,
    hash
  };
}
function stringifyParsedURL(parsed) {
  const pathname = parsed.pathname || "";
  const search = parsed.search ? (parsed.search.startsWith("?") ? "" : "?") + parsed.search : "";
  const hash = parsed.hash || "";
  const auth = parsed.auth ? parsed.auth + "@" : "";
  const host = parsed.host || "";
  const proto = parsed.protocol || parsed[protocolRelative] ? (parsed.protocol || "") + "//" : "";
  return proto + auth + host + pathname + search + hash;
}

function hasReqHeader(event, name, includes) {
  const value = getRequestHeader(event, name);
  return value && typeof value === "string" && value.toLowerCase().includes(includes);
}
function isJsonRequest(event) {
  if (hasReqHeader(event, "accept", "text/html")) {
    return false;
  }
  return hasReqHeader(event, "accept", "application/json") || hasReqHeader(event, "user-agent", "curl/") || hasReqHeader(event, "user-agent", "httpie/") || hasReqHeader(event, "sec-fetch-mode", "cors") || event.path.startsWith("/api/") || event.path.endsWith(".json");
}
function normalizeError(error, isDev) {
  const cwd = typeof process.cwd === "function" ? process.cwd() : "/";
  const stack = (error.unhandled || error.fatal) ? [] : (error.stack || "").split("\n").splice(1).filter((line) => line.includes("at ")).map((line) => {
    const text = line.replace(cwd + "/", "./").replace("webpack:/", "").replace("file://", "").trim();
    return {
      text,
      internal: line.includes("node_modules") && !line.includes(".cache") || line.includes("internal") || line.includes("new Promise")
    };
  });
  const statusCode = error.statusCode || 500;
  const statusMessage = error.statusMessage ?? (statusCode === 404 ? "Not Found" : "");
  const message = error.unhandled ? "internal server error" : error.message || error.toString();
  return {
    stack,
    statusCode,
    statusMessage,
    message
  };
}
function _captureError(error, type) {
  console.error(`[nitro] [${type}]`, error);
  useNitroApp().captureError(error, { tags: [type] });
}
function trapUnhandledNodeErrors() {
  process.on(
    "unhandledRejection",
    (error) => _captureError(error, "unhandledRejection")
  );
  process.on(
    "uncaughtException",
    (error) => _captureError(error, "uncaughtException")
  );
}
function joinHeaders(value) {
  return Array.isArray(value) ? value.join(", ") : String(value);
}
function normalizeFetchResponse(response) {
  if (!response.headers.has("set-cookie")) {
    return response;
  }
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: normalizeCookieHeaders(response.headers)
  });
}
function normalizeCookieHeader(header = "") {
  return splitCookiesString(joinHeaders(header));
}
function normalizeCookieHeaders(headers) {
  const outgoingHeaders = new Headers();
  for (const [name, header] of headers) {
    if (name === "set-cookie") {
      for (const cookie of normalizeCookieHeader(header)) {
        outgoingHeaders.append("set-cookie", cookie);
      }
    } else {
      outgoingHeaders.set(name, joinHeaders(header));
    }
  }
  return outgoingHeaders;
}

const errorHandler = (async function errorhandler(error, event) {
  const { stack, statusCode, statusMessage, message } = normalizeError(error);
  const errorObject = {
    url: event.path,
    statusCode,
    statusMessage,
    message,
    stack: statusCode !== 404 ? `<pre>${stack.map((i) => `<span class="stack${i.internal ? " internal" : ""}">${i.text}</span>`).join("\n")}</pre>` : "",
    // TODO: check and validate error.data for serialisation into query
    data: error.data
  };
  if (error.unhandled || error.fatal) {
    const tags = [
      "[nuxt]",
      "[request error]",
      error.unhandled && "[unhandled]",
      error.fatal && "[fatal]",
      Number(errorObject.statusCode) !== 200 && `[${errorObject.statusCode}]`
    ].filter(Boolean).join(" ");
    console.error(tags, (error.message || error.toString() || "internal server error") + "\n" + stack.map((l) => "  " + l.text).join("  \n"));
  }
  if (event.handled) {
    return;
  }
  setResponseStatus(event, errorObject.statusCode !== 200 && errorObject.statusCode || 500, errorObject.statusMessage);
  if (isJsonRequest(event)) {
    setResponseHeader(event, "Content-Type", "application/json");
    return send(event, JSON.stringify(errorObject));
  }
  const reqHeaders = getRequestHeaders(event);
  const isRenderingError = event.path.startsWith("/__nuxt_error") || !!reqHeaders["x-nuxt-error"];
  const res = isRenderingError ? null : await useNitroApp().localFetch(
    withQuery(joinURL(useRuntimeConfig(event).app.baseURL, "/__nuxt_error"), errorObject),
    {
      headers: { ...reqHeaders, "x-nuxt-error": "true" },
      redirect: "manual"
    }
  ).catch(() => null);
  if (!res) {
    const { template } = await Promise.resolve().then(function () { return errorDev; }) ;
    {
      errorObject.description = errorObject.message;
    }
    if (event.handled) {
      return;
    }
    setResponseHeader(event, "Content-Type", "text/html;charset=UTF-8");
    return send(event, template(errorObject));
  }
  const html = await res.text();
  if (event.handled) {
    return;
  }
  for (const [header, value] of res.headers.entries()) {
    setResponseHeader(event, header, value);
  }
  setResponseStatus(event, res.status && res.status !== 200 ? res.status : void 0, res.statusText);
  return send(event, html);
});

const script$1 = `
if (!window.__NUXT_DEVTOOLS_TIME_METRIC__) {
  Object.defineProperty(window, '__NUXT_DEVTOOLS_TIME_METRIC__', {
    value: {},
    enumerable: false,
    configurable: true,
  })
}
window.__NUXT_DEVTOOLS_TIME_METRIC__.appInit = Date.now()
`;

const _hE9BP2YFou = (function(nitro) {
  nitro.hooks.hook("render:html", (htmlContext) => {
    htmlContext.head.push(`<script>${script$1}<\/script>`);
  });
});

const rootDir = "C:/Users/<USER>/PhpstormProjects/ro/fe2";

const appHead = {"meta":[{"name":"viewport","content":"width=device-width, initial-scale=1"},{"charset":"utf-8"}],"link":[],"style":[],"script":[],"noscript":[]};

const appRootTag = "div";

const appRootAttrs = {"id":"__nuxt"};

const appTeleportTag = "div";

const appTeleportAttrs = {"id":"teleports"};

const appId = "nuxt-app";

const devReducers = {
  VNode: (data) => isVNode(data) ? { type: data.type, props: data.props } : void 0,
  URL: (data) => data instanceof URL ? data.toString() : void 0
};
const asyncContext = getContext("nuxt-dev", { asyncContext: true, AsyncLocalStorage });
const _dyW8azYzYP = (nitroApp) => {
  const handler = nitroApp.h3App.handler;
  nitroApp.h3App.handler = (event) => {
    return asyncContext.callAsync({ logs: [], event }, () => handler(event));
  };
  onConsoleLog((_log) => {
    const ctx = asyncContext.tryUse();
    if (!ctx) {
      return;
    }
    const rawStack = captureRawStackTrace();
    if (!rawStack || rawStack.includes("runtime/vite-node.mjs")) {
      return;
    }
    const trace = [];
    let filename = "";
    for (const entry of parseRawStackTrace(rawStack)) {
      if (entry.source === globalThis._importMeta_.url) {
        continue;
      }
      if (EXCLUDE_TRACE_RE.test(entry.source)) {
        continue;
      }
      filename ||= entry.source.replace(withTrailingSlash(rootDir), "");
      trace.push({
        ...entry,
        source: entry.source.startsWith("file://") ? entry.source.replace("file://", "") : entry.source
      });
    }
    const log = {
      ..._log,
      // Pass along filename to allow the client to display more info about where log comes from
      filename,
      // Clean up file names in stack trace
      stack: trace
    };
    ctx.logs.push(log);
  });
  nitroApp.hooks.hook("afterResponse", () => {
    const ctx = asyncContext.tryUse();
    if (!ctx) {
      return;
    }
    return nitroApp.hooks.callHook("dev:ssr-logs", { logs: ctx.logs, path: ctx.event.path });
  });
  nitroApp.hooks.hook("render:html", (htmlContext) => {
    const ctx = asyncContext.tryUse();
    if (!ctx) {
      return;
    }
    try {
      const reducers = Object.assign(/* @__PURE__ */ Object.create(null), devReducers, ctx.event.context._payloadReducers);
      htmlContext.bodyAppend.unshift(`<script type="application/json" data-nuxt-logs="${appId}">${stringify(ctx.logs, reducers)}<\/script>`);
    } catch (e) {
      const shortError = e instanceof Error && "toString" in e ? ` Received \`${e.toString()}\`.` : "";
      console.warn(`[nuxt] Failed to stringify dev server logs.${shortError} You can define your own reducer/reviver for rich types following the instructions in https://nuxt.com/docs/api/composables/use-nuxt-app#payload.`);
    }
  });
};
const EXCLUDE_TRACE_RE = /\/node_modules\/(?:.*\/)?(?:nuxt|nuxt-nightly|nuxt-edge|nuxt3|consola|@vue)\/|core\/runtime\/nitro/;
function onConsoleLog(callback) {
  consola.addReporter({
    log(logObj) {
      callback(logObj);
    }
  });
  consola.wrapConsole();
}

const script = "\"use strict\";(()=>{const t=window,e=document.documentElement,c=[\"dark\",\"light\"],n=getStorageValue(\"localStorage\",\"nuxt-color-mode\")||\"system\";let i=n===\"system\"?u():n;const r=e.getAttribute(\"data-color-mode-forced\");r&&(i=r),l(i),t[\"__NUXT_COLOR_MODE__\"]={preference:n,value:i,getColorScheme:u,addColorScheme:l,removeColorScheme:d};function l(o){const s=\"\"+o+\"\",a=\"\";e.classList?e.classList.add(s):e.className+=\" \"+s,a&&e.setAttribute(\"data-\"+a,o)}function d(o){const s=\"\"+o+\"\",a=\"\";e.classList?e.classList.remove(s):e.className=e.className.replace(new RegExp(s,\"g\"),\"\"),a&&e.removeAttribute(\"data-\"+a)}function f(o){return t.matchMedia(\"(prefers-color-scheme\"+o+\")\")}function u(){if(t.matchMedia&&f(\"\").media!==\"not all\"){for(const o of c)if(f(\":\"+o).matches)return o}return\"light\"}})();function getStorageValue(t,e){switch(t){case\"localStorage\":return window.localStorage.getItem(e);case\"sessionStorage\":return window.sessionStorage.getItem(e);case\"cookie\":return getCookie(e);default:return null}}function getCookie(t){const c=(\"; \"+window.document.cookie).split(\"; \"+t+\"=\");if(c.length===2)return c.pop()?.split(\";\").shift()}";

const _orPMcOGZj8 = (function(nitro) {
  nitro.hooks.hook("render:html", (htmlContext) => {
    htmlContext.head.push(`<script>${script}<\/script>`);
  });
});

const plugins = [
  _hE9BP2YFou,
_dyW8azYzYP,
_orPMcOGZj8
];

const inlineAppConfig = {
  "nuxt": {},
  "icon": {
    "provider": "server",
    "class": "",
    "aliases": {},
    "iconifyApiEndpoint": "https://api.iconify.design",
    "localApiEndpoint": "/api/_nuxt_icon",
    "fallbackToApi": true,
    "cssSelectorPrefix": "i-",
    "cssWherePseudo": true,
    "mode": "css",
    "attrs": {
      "aria-hidden": true
    },
    "collections": [
      "academicons",
      "akar-icons",
      "ant-design",
      "arcticons",
      "basil",
      "bi",
      "bitcoin-icons",
      "bpmn",
      "brandico",
      "bx",
      "bxl",
      "bxs",
      "bytesize",
      "carbon",
      "catppuccin",
      "cbi",
      "charm",
      "ci",
      "cib",
      "cif",
      "cil",
      "circle-flags",
      "circum",
      "clarity",
      "codicon",
      "covid",
      "cryptocurrency",
      "cryptocurrency-color",
      "dashicons",
      "devicon",
      "devicon-plain",
      "ei",
      "el",
      "emojione",
      "emojione-monotone",
      "emojione-v1",
      "entypo",
      "entypo-social",
      "eos-icons",
      "ep",
      "et",
      "eva",
      "f7",
      "fa",
      "fa-brands",
      "fa-regular",
      "fa-solid",
      "fa6-brands",
      "fa6-regular",
      "fa6-solid",
      "fad",
      "fe",
      "feather",
      "file-icons",
      "flag",
      "flagpack",
      "flat-color-icons",
      "flat-ui",
      "flowbite",
      "fluent",
      "fluent-emoji",
      "fluent-emoji-flat",
      "fluent-emoji-high-contrast",
      "fluent-mdl2",
      "fontelico",
      "fontisto",
      "formkit",
      "foundation",
      "fxemoji",
      "gala",
      "game-icons",
      "geo",
      "gg",
      "gis",
      "gravity-ui",
      "gridicons",
      "grommet-icons",
      "guidance",
      "healthicons",
      "heroicons",
      "heroicons-outline",
      "heroicons-solid",
      "hugeicons",
      "humbleicons",
      "ic",
      "icomoon-free",
      "icon-park",
      "icon-park-outline",
      "icon-park-solid",
      "icon-park-twotone",
      "iconamoon",
      "iconoir",
      "icons8",
      "il",
      "ion",
      "iwwa",
      "jam",
      "la",
      "lets-icons",
      "line-md",
      "logos",
      "ls",
      "lucide",
      "lucide-lab",
      "mage",
      "majesticons",
      "maki",
      "map",
      "marketeq",
      "material-symbols",
      "material-symbols-light",
      "mdi",
      "mdi-light",
      "medical-icon",
      "memory",
      "meteocons",
      "mi",
      "mingcute",
      "mono-icons",
      "mynaui",
      "nimbus",
      "nonicons",
      "noto",
      "noto-v1",
      "octicon",
      "oi",
      "ooui",
      "openmoji",
      "oui",
      "pajamas",
      "pepicons",
      "pepicons-pencil",
      "pepicons-pop",
      "pepicons-print",
      "ph",
      "pixelarticons",
      "prime",
      "ps",
      "quill",
      "radix-icons",
      "raphael",
      "ri",
      "rivet-icons",
      "si-glyph",
      "simple-icons",
      "simple-line-icons",
      "skill-icons",
      "solar",
      "streamline",
      "streamline-emojis",
      "subway",
      "svg-spinners",
      "system-uicons",
      "tabler",
      "tdesign",
      "teenyicons",
      "token",
      "token-branded",
      "topcoat",
      "twemoji",
      "typcn",
      "uil",
      "uim",
      "uis",
      "uit",
      "uiw",
      "unjs",
      "vaadin",
      "vs",
      "vscode-icons",
      "websymbol",
      "weui",
      "whh",
      "wi",
      "wpf",
      "zmdi",
      "zondicons"
    ],
    "fetchTimeout": 1500
  },
  "ui": {
    "primary": "green",
    "gray": "cool",
    "colors": [
      "red",
      "orange",
      "amber",
      "yellow",
      "lime",
      "green",
      "emerald",
      "teal",
      "cyan",
      "sky",
      "blue",
      "indigo",
      "violet",
      "purple",
      "fuchsia",
      "pink",
      "rose",
      "terracotta",
      "forest",
      "wheat",
      "romana-red",
      "primary"
    ],
    "strategy": "merge"
  }
};



const appConfig = defuFn(inlineAppConfig);

function getEnv(key, opts) {
  const envKey = snakeCase(key).toUpperCase();
  return destr(
    process.env[opts.prefix + envKey] ?? process.env[opts.altPrefix + envKey]
  );
}
function _isObject(input) {
  return typeof input === "object" && !Array.isArray(input);
}
function applyEnv(obj, opts, parentKey = "") {
  for (const key in obj) {
    const subKey = parentKey ? `${parentKey}_${key}` : key;
    const envValue = getEnv(subKey, opts);
    if (_isObject(obj[key])) {
      if (_isObject(envValue)) {
        obj[key] = { ...obj[key], ...envValue };
        applyEnv(obj[key], opts, subKey);
      } else if (envValue === void 0) {
        applyEnv(obj[key], opts, subKey);
      } else {
        obj[key] = envValue ?? obj[key];
      }
    } else {
      obj[key] = envValue ?? obj[key];
    }
    if (opts.envExpansion && typeof obj[key] === "string") {
      obj[key] = _expandFromEnv(obj[key]);
    }
  }
  return obj;
}
const envExpandRx = /{{(.*?)}}/g;
function _expandFromEnv(value) {
  return value.replace(envExpandRx, (match, key) => {
    return process.env[key] || match;
  });
}

const _inlineRuntimeConfig = {
  "app": {
    "baseURL": "/",
    "buildId": "dev",
    "buildAssetsDir": "/_nuxt/",
    "cdnURL": ""
  },
  "nitro": {
    "envPrefix": "NUXT_",
    "routeRules": {
      "/__nuxt_error": {
        "cache": false
      },
      "/_nuxt/builds/meta/**": {
        "headers": {
          "cache-control": "public, max-age=31536000, immutable"
        }
      },
      "/_nuxt/builds/**": {
        "headers": {
          "cache-control": "public, max-age=1, immutable"
        }
      }
    }
  },
  "public": {
    "serverUrl": "http://127.0.0.1:3001",
    "apiUrl": "http://127.0.0.1:3001",
    "cloudflareApiUrl": "http://localhost:8787",
    "stripePublishableKey": "pk_live_51Hg43XCyrV94JCEvu61jDws8MbIPydagZpl48WssYsJNR8ZkHqeNtK1yaYr4gtjJxZC4kB7kZuRP2IIFRaS4PQ8X00tok1acII",
    "reviewsApiEndpoint": "https://reviews.handmadein.ro",
    "defaultCountry": "RO",
    "defaultLanguage": "ro",
    "i18n": {
      "baseUrl": "https://handmadein.ro",
      "defaultLocale": "ro",
      "defaultDirection": "ltr",
      "strategy": "no_prefix",
      "lazy": true,
      "rootRedirect": "",
      "routesNameSeparator": "___",
      "defaultLocaleRouteNameSuffix": "default",
      "skipSettingLocaleOnNavigate": false,
      "differentDomains": false,
      "trailingSlash": false,
      "configLocales": [
        {
          "code": "ro",
          "name": "Română",
          "language": "ro-RO",
          "files": [
            "C:/Users/<USER>/PhpstormProjects/ro/fe2/locales/ro.json"
          ]
        }
      ],
      "locales": {
        "ro": {
          "domain": ""
        }
      },
      "detectBrowserLanguage": {
        "alwaysRedirect": false,
        "cookieCrossOrigin": true,
        "cookieDomain": "",
        "cookieKey": "i18n_redirected",
        "cookieSecure": false,
        "fallbackLocale": "",
        "redirectOn": "root",
        "useCookie": true
      },
      "experimental": {
        "localeDetector": "",
        "switchLocalePathLinkSSR": false,
        "autoImportTranslationFunctions": false
      },
      "multiDomainLocales": false
    },
    "gtag": {
      "enabled": true,
      "initMode": "auto",
      "id": "AW-16546086564",
      "initCommands": [],
      "config": {
        "cookie_prefix": "_ga",
        "cookie_domain": "handmadein.ro",
        "cookie_expires": 63072000
      },
      "tags": [],
      "loadingStrategy": "defer",
      "url": "https://www.googletagmanager.com/gtag/js"
    }
  },
  "icon": {
    "serverKnownCssClasses": []
  },
  "ipx": {
    "baseURL": "/_ipx",
    "alias": {},
    "fs": {
      "dir": [
        "C:/Users/<USER>/PhpstormProjects/ro/fe2/public"
      ]
    },
    "http": {
      "domains": []
    }
  }
};
const envOptions = {
  prefix: "NITRO_",
  altPrefix: _inlineRuntimeConfig.nitro.envPrefix ?? process.env.NITRO_ENV_PREFIX ?? "_",
  envExpansion: _inlineRuntimeConfig.nitro.envExpansion ?? process.env.NITRO_ENV_EXPANSION ?? false
};
const _sharedRuntimeConfig = _deepFreeze(
  applyEnv(klona(_inlineRuntimeConfig), envOptions)
);
function useRuntimeConfig(event) {
  if (!event) {
    return _sharedRuntimeConfig;
  }
  if (event.context.nitro.runtimeConfig) {
    return event.context.nitro.runtimeConfig;
  }
  const runtimeConfig = klona(_inlineRuntimeConfig);
  applyEnv(runtimeConfig, envOptions);
  event.context.nitro.runtimeConfig = runtimeConfig;
  return runtimeConfig;
}
const _sharedAppConfig = _deepFreeze(klona(appConfig));
function useAppConfig(event) {
  {
    return _sharedAppConfig;
  }
}
function _deepFreeze(object) {
  const propNames = Object.getOwnPropertyNames(object);
  for (const name of propNames) {
    const value = object[name];
    if (value && typeof value === "object") {
      _deepFreeze(value);
    }
  }
  return Object.freeze(object);
}
new Proxy(/* @__PURE__ */ Object.create(null), {
  get: (_, prop) => {
    console.warn(
      "Please use `useRuntimeConfig()` instead of accessing config directly."
    );
    const runtimeConfig = useRuntimeConfig();
    if (prop in runtimeConfig) {
      return runtimeConfig[prop];
    }
    return void 0;
  }
});

const serverStorage = {
  getItem: async (key) => null,
  setItem: async (key, value) => {
  },
  removeItem: async (key) => {
  },
  clear: async () => {
  }
};
const _NCQKQE = defineEventHandler((event) => {
  const config = useRuntimeConfig();
  Amplify.configure({
    Auth: {
      Cognito: {
        userPoolId: config.cognitoUserPoolId,
        userPoolClientId: config.cognitoClientId,
        signUpVerificationMethod: "code"
      }
    }
  });
  cognitoUserPoolsTokenProvider.setKeyValueStorage(serverStorage);
  event.context.generateSecretHash = (username) => {
    const message = username + config.cognitoClientId;
    const hmac = createHmac("SHA256", config.cognitoClientSecret);
    return hmac.update(message).digest("base64");
  };
});

const sessions = {};
function generateSessionId() {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}
function getOrCreateSession(event) {
  let sessionId = getCookie(event, "sessionId");
  if (!sessionId || !sessions[sessionId]) {
    sessionId = generateSessionId();
    sessions[sessionId] = {};
    useRuntimeConfig();
    setCookie(event, "sessionId", sessionId, {
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24 * 7,
      // 1 week
      secure: false,
      sameSite: "lax"
    });
  }
  return sessions[sessionId];
}
const _lIvYoZ = defineEventHandler((event) => {
  event.context.session = getOrCreateSession(event);
});

const serverAssets = [{"baseName":"server","dir":"C:/Users/<USER>/PhpstormProjects/ro/fe2/server/assets"}];

const assets = createStorage();

for (const asset of serverAssets) {
  assets.mount(asset.baseName, unstorage_47drivers_47fs({ base: asset.dir, ignore: (asset?.ignore || []) }));
}

const storage = createStorage({});

storage.mount('/assets', assets);

storage.mount('root', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"C:/Users/<USER>/PhpstormProjects/ro/fe2"}));
storage.mount('src', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"C:/Users/<USER>/PhpstormProjects/ro/fe2/server"}));
storage.mount('build', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt"}));
storage.mount('cache', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt/cache"}));
storage.mount('data', unstorage_47drivers_47fs({"driver":"fs","base":"C:/Users/<USER>/PhpstormProjects/ro/fe2/.data/kv"}));

function useStorage(base = "") {
  return base ? prefixStorage(storage, base) : storage;
}

function defaultCacheOptions() {
  return {
    name: "_",
    base: "/cache",
    swr: true,
    maxAge: 1
  };
}
function defineCachedFunction(fn, opts = {}) {
  opts = { ...defaultCacheOptions(), ...opts };
  const pending = {};
  const group = opts.group || "nitro/functions";
  const name = opts.name || fn.name || "_";
  const integrity = opts.integrity || hash([fn, opts]);
  const validate = opts.validate || ((entry) => entry.value !== void 0);
  async function get(key, resolver, shouldInvalidateCache, event) {
    const cacheKey = [opts.base, group, name, key + ".json"].filter(Boolean).join(":").replace(/:\/$/, ":index");
    let entry = await useStorage().getItem(cacheKey).catch((error) => {
      console.error(`[nitro] [cache] Cache read error.`, error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }) || {};
    if (typeof entry !== "object") {
      entry = {};
      const error = new Error("Malformed data read from cache.");
      console.error("[nitro] [cache]", error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }
    const ttl = (opts.maxAge ?? 0) * 1e3;
    if (ttl) {
      entry.expires = Date.now() + ttl;
    }
    const expired = shouldInvalidateCache || entry.integrity !== integrity || ttl && Date.now() - (entry.mtime || 0) > ttl || validate(entry) === false;
    const _resolve = async () => {
      const isPending = pending[key];
      if (!isPending) {
        if (entry.value !== void 0 && (opts.staleMaxAge || 0) >= 0 && opts.swr === false) {
          entry.value = void 0;
          entry.integrity = void 0;
          entry.mtime = void 0;
          entry.expires = void 0;
        }
        pending[key] = Promise.resolve(resolver());
      }
      try {
        entry.value = await pending[key];
      } catch (error) {
        if (!isPending) {
          delete pending[key];
        }
        throw error;
      }
      if (!isPending) {
        entry.mtime = Date.now();
        entry.integrity = integrity;
        delete pending[key];
        if (validate(entry) !== false) {
          let setOpts;
          if (opts.maxAge && !opts.swr) {
            setOpts = { ttl: opts.maxAge };
          }
          const promise = useStorage().setItem(cacheKey, entry, setOpts).catch((error) => {
            console.error(`[nitro] [cache] Cache write error.`, error);
            useNitroApp().captureError(error, { event, tags: ["cache"] });
          });
          if (event?.waitUntil) {
            event.waitUntil(promise);
          }
        }
      }
    };
    const _resolvePromise = expired ? _resolve() : Promise.resolve();
    if (entry.value === void 0) {
      await _resolvePromise;
    } else if (expired && event && event.waitUntil) {
      event.waitUntil(_resolvePromise);
    }
    if (opts.swr && validate(entry) !== false) {
      _resolvePromise.catch((error) => {
        console.error(`[nitro] [cache] SWR handler error.`, error);
        useNitroApp().captureError(error, { event, tags: ["cache"] });
      });
      return entry;
    }
    return _resolvePromise.then(() => entry);
  }
  return async (...args) => {
    const shouldBypassCache = await opts.shouldBypassCache?.(...args);
    if (shouldBypassCache) {
      return fn(...args);
    }
    const key = await (opts.getKey || getKey)(...args);
    const shouldInvalidateCache = await opts.shouldInvalidateCache?.(...args);
    const entry = await get(
      key,
      () => fn(...args),
      shouldInvalidateCache,
      args[0] && isEvent(args[0]) ? args[0] : void 0
    );
    let value = entry.value;
    if (opts.transform) {
      value = await opts.transform(entry, ...args) || value;
    }
    return value;
  };
}
function cachedFunction(fn, opts = {}) {
  return defineCachedFunction(fn, opts);
}
function getKey(...args) {
  return args.length > 0 ? hash(args, {}) : "";
}
function escapeKey(key) {
  return String(key).replace(/\W/g, "");
}
function defineCachedEventHandler(handler, opts = defaultCacheOptions()) {
  const variableHeaderNames = (opts.varies || []).filter(Boolean).map((h) => h.toLowerCase()).sort();
  const _opts = {
    ...opts,
    getKey: async (event) => {
      const customKey = await opts.getKey?.(event);
      if (customKey) {
        return escapeKey(customKey);
      }
      const _path = event.node.req.originalUrl || event.node.req.url || event.path;
      let _pathname;
      try {
        _pathname = escapeKey(decodeURI(parseURL(_path).pathname)).slice(0, 16) || "index";
      } catch {
        _pathname = "-";
      }
      const _hashedPath = `${_pathname}.${hash(_path)}`;
      const _headers = variableHeaderNames.map((header) => [header, event.node.req.headers[header]]).map(([name, value]) => `${escapeKey(name)}.${hash(value)}`);
      return [_hashedPath, ..._headers].join(":");
    },
    validate: (entry) => {
      if (!entry.value) {
        return false;
      }
      if (entry.value.code >= 400) {
        return false;
      }
      if (entry.value.body === void 0) {
        return false;
      }
      if (entry.value.headers.etag === "undefined" || entry.value.headers["last-modified"] === "undefined") {
        return false;
      }
      return true;
    },
    group: opts.group || "nitro/handlers",
    integrity: opts.integrity || hash([handler, opts])
  };
  const _cachedHandler = cachedFunction(
    async (incomingEvent) => {
      const variableHeaders = {};
      for (const header of variableHeaderNames) {
        const value = incomingEvent.node.req.headers[header];
        if (value !== void 0) {
          variableHeaders[header] = value;
        }
      }
      const reqProxy = cloneWithProxy(incomingEvent.node.req, {
        headers: variableHeaders
      });
      const resHeaders = {};
      let _resSendBody;
      const resProxy = cloneWithProxy(incomingEvent.node.res, {
        statusCode: 200,
        writableEnded: false,
        writableFinished: false,
        headersSent: false,
        closed: false,
        getHeader(name) {
          return resHeaders[name];
        },
        setHeader(name, value) {
          resHeaders[name] = value;
          return this;
        },
        getHeaderNames() {
          return Object.keys(resHeaders);
        },
        hasHeader(name) {
          return name in resHeaders;
        },
        removeHeader(name) {
          delete resHeaders[name];
        },
        getHeaders() {
          return resHeaders;
        },
        end(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2();
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return this;
        },
        write(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2(void 0);
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return true;
        },
        writeHead(statusCode, headers2) {
          this.statusCode = statusCode;
          if (headers2) {
            if (Array.isArray(headers2) || typeof headers2 === "string") {
              throw new TypeError("Raw headers  is not supported.");
            }
            for (const header in headers2) {
              const value = headers2[header];
              if (value !== void 0) {
                this.setHeader(
                  header,
                  value
                );
              }
            }
          }
          return this;
        }
      });
      const event = createEvent(reqProxy, resProxy);
      event.fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: useNitroApp().localFetch
      });
      event.$fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: globalThis.$fetch
      });
      event.context = incomingEvent.context;
      event.context.cache = {
        options: _opts
      };
      const body = await handler(event) || _resSendBody;
      const headers = event.node.res.getHeaders();
      headers.etag = String(
        headers.Etag || headers.etag || `W/"${hash(body)}"`
      );
      headers["last-modified"] = String(
        headers["Last-Modified"] || headers["last-modified"] || (/* @__PURE__ */ new Date()).toUTCString()
      );
      const cacheControl = [];
      if (opts.swr) {
        if (opts.maxAge) {
          cacheControl.push(`s-maxage=${opts.maxAge}`);
        }
        if (opts.staleMaxAge) {
          cacheControl.push(`stale-while-revalidate=${opts.staleMaxAge}`);
        } else {
          cacheControl.push("stale-while-revalidate");
        }
      } else if (opts.maxAge) {
        cacheControl.push(`max-age=${opts.maxAge}`);
      }
      if (cacheControl.length > 0) {
        headers["cache-control"] = cacheControl.join(", ");
      }
      const cacheEntry = {
        code: event.node.res.statusCode,
        headers,
        body
      };
      return cacheEntry;
    },
    _opts
  );
  return defineEventHandler(async (event) => {
    if (opts.headersOnly) {
      if (handleCacheHeaders(event, { maxAge: opts.maxAge })) {
        return;
      }
      return handler(event);
    }
    const response = await _cachedHandler(
      event
    );
    if (event.node.res.headersSent || event.node.res.writableEnded) {
      return response.body;
    }
    if (handleCacheHeaders(event, {
      modifiedTime: new Date(response.headers["last-modified"]),
      etag: response.headers.etag,
      maxAge: opts.maxAge
    })) {
      return;
    }
    event.node.res.statusCode = response.code;
    for (const name in response.headers) {
      const value = response.headers[name];
      if (name === "set-cookie") {
        event.node.res.appendHeader(
          name,
          splitCookiesString(value)
        );
      } else {
        if (value !== void 0) {
          event.node.res.setHeader(name, value);
        }
      }
    }
    return response.body;
  });
}
function cloneWithProxy(obj, overrides) {
  return new Proxy(obj, {
    get(target, property, receiver) {
      if (property in overrides) {
        return overrides[property];
      }
      return Reflect.get(target, property, receiver);
    },
    set(target, property, value, receiver) {
      if (property in overrides) {
        overrides[property] = value;
        return true;
      }
      return Reflect.set(target, property, value, receiver);
    }
  });
}
const cachedEventHandler = defineCachedEventHandler;

function defineRenderHandler(render) {
  const runtimeConfig = useRuntimeConfig();
  return eventHandler(async (event) => {
    const nitroApp = useNitroApp();
    const ctx = { event, render, response: void 0 };
    await nitroApp.hooks.callHook("render:before", ctx);
    if (!ctx.response) {
      if (event.path === `${runtimeConfig.app.baseURL}favicon.ico`) {
        setResponseHeader(event, "Content-Type", "image/x-icon");
        return send(
          event,
          "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
        );
      }
      ctx.response = await ctx.render(event);
      if (!ctx.response) {
        const _currentStatus = getResponseStatus(event);
        setResponseStatus(event, _currentStatus === 200 ? 500 : _currentStatus);
        return send(
          event,
          "No response returned from render handler: " + event.path
        );
      }
    }
    await nitroApp.hooks.callHook("render:response", ctx.response, ctx);
    if (ctx.response.headers) {
      setResponseHeaders(event, ctx.response.headers);
    }
    if (ctx.response.statusCode || ctx.response.statusMessage) {
      setResponseStatus(
        event,
        ctx.response.statusCode,
        ctx.response.statusMessage
      );
    }
    return ctx.response.body;
  });
}

const config = useRuntimeConfig();
const _routeRulesMatcher = toRouteMatcher(
  createRouter({ routes: config.nitro.routeRules })
);
function createRouteRulesHandler(ctx) {
  return eventHandler((event) => {
    const routeRules = getRouteRules(event);
    if (routeRules.headers) {
      setHeaders(event, routeRules.headers);
    }
    if (routeRules.redirect) {
      let target = routeRules.redirect.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.redirect._redirectStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return sendRedirect(event, target, routeRules.redirect.statusCode);
    }
    if (routeRules.proxy) {
      let target = routeRules.proxy.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.proxy._proxyStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return proxyRequest(event, target, {
        fetch: ctx.localFetch,
        ...routeRules.proxy
      });
    }
  });
}
function getRouteRules(event) {
  event.context._nitro = event.context._nitro || {};
  if (!event.context._nitro.routeRules) {
    event.context._nitro.routeRules = getRouteRulesForPath(
      withoutBase(event.path.split("?")[0], useRuntimeConfig().app.baseURL)
    );
  }
  return event.context._nitro.routeRules;
}
function getRouteRulesForPath(path) {
  return defu({}, ..._routeRulesMatcher.matchAll(path).reverse());
}

const r=Object.create(null),i=e=>globalThis.process?.env||globalThis._importMeta_.env||globalThis.Deno?.env.toObject()||globalThis.__env__||(e?r:globalThis),o=new Proxy(r,{get(e,s){return i()[s]??r[s]},has(e,s){const E=i();return s in E||s in r},set(e,s,E){const B=i(true);return B[s]=E,true},deleteProperty(e,s){if(!s)return  false;const E=i(true);return delete E[s],true},ownKeys(){const e=i(true);return Object.keys(e)}}),t=typeof process<"u"&&process.env&&"development"||"",f=[["APPVEYOR"],["AWS_AMPLIFY","AWS_APP_ID",{ci:true}],["AZURE_PIPELINES","SYSTEM_TEAMFOUNDATIONCOLLECTIONURI"],["AZURE_STATIC","INPUT_AZURE_STATIC_WEB_APPS_API_TOKEN"],["APPCIRCLE","AC_APPCIRCLE"],["BAMBOO","bamboo_planKey"],["BITBUCKET","BITBUCKET_COMMIT"],["BITRISE","BITRISE_IO"],["BUDDY","BUDDY_WORKSPACE_ID"],["BUILDKITE"],["CIRCLE","CIRCLECI"],["CIRRUS","CIRRUS_CI"],["CLOUDFLARE_PAGES","CF_PAGES",{ci:true}],["CODEBUILD","CODEBUILD_BUILD_ARN"],["CODEFRESH","CF_BUILD_ID"],["DRONE"],["DRONE","DRONE_BUILD_EVENT"],["DSARI"],["GITHUB_ACTIONS"],["GITLAB","GITLAB_CI"],["GITLAB","CI_MERGE_REQUEST_ID"],["GOCD","GO_PIPELINE_LABEL"],["LAYERCI"],["HUDSON","HUDSON_URL"],["JENKINS","JENKINS_URL"],["MAGNUM"],["NETLIFY"],["NETLIFY","NETLIFY_LOCAL",{ci:false}],["NEVERCODE"],["RENDER"],["SAIL","SAILCI"],["SEMAPHORE"],["SCREWDRIVER"],["SHIPPABLE"],["SOLANO","TDDIUM"],["STRIDER"],["TEAMCITY","TEAMCITY_VERSION"],["TRAVIS"],["VERCEL","NOW_BUILDER"],["VERCEL","VERCEL",{ci:false}],["VERCEL","VERCEL_ENV",{ci:false}],["APPCENTER","APPCENTER_BUILD_ID"],["CODESANDBOX","CODESANDBOX_SSE",{ci:false}],["CODESANDBOX","CODESANDBOX_HOST",{ci:false}],["STACKBLITZ"],["STORMKIT"],["CLEAVR"],["ZEABUR"],["CODESPHERE","CODESPHERE_APP_ID",{ci:true}],["RAILWAY","RAILWAY_PROJECT_ID"],["RAILWAY","RAILWAY_SERVICE_ID"],["DENO-DEPLOY","DENO_DEPLOYMENT_ID"],["FIREBASE_APP_HOSTING","FIREBASE_APP_HOSTING",{ci:true}]];function b(){if(globalThis.process?.env)for(const e of f){const s=e[1]||e[0];if(globalThis.process?.env[s])return {name:e[0].toLowerCase(),...e[2]}}return globalThis.process?.env?.SHELL==="/bin/jsh"&&globalThis.process?.versions?.webcontainer?{name:"stackblitz",ci:false}:{name:"",ci:false}}const l=b(),p=l.name;function n(e){return e?e!=="false":false}const I=globalThis.process?.platform||"",T=n(o.CI)||l.ci!==false,a=n(globalThis.process?.stdout&&globalThis.process?.stdout.isTTY);n(o.DEBUG);const R=t==="test"||n(o.TEST);n(o.MINIMAL)||T||R||!a;const A=/^win/i.test(I);!n(o.NO_COLOR)&&(n(o.FORCE_COLOR)||(a||A)&&o.TERM!=="dumb"||T);const C=(globalThis.process?.versions?.node||"").replace(/^v/,"")||null;Number(C?.split(".")[0])||null;const y=globalThis.process||Object.create(null),_={versions:{}};new Proxy(y,{get(e,s){if(s==="env")return o;if(s in e)return e[s];if(s in _)return _[s]}});const c=globalThis.process?.release?.name==="node",O=!!globalThis.Bun||!!globalThis.process?.versions?.bun,D=!!globalThis.Deno,L=!!globalThis.fastly,S=!!globalThis.Netlify,u=!!globalThis.EdgeRuntime,N=globalThis.navigator?.userAgent==="Cloudflare-Workers",F=[[S,"netlify"],[u,"edge-light"],[N,"workerd"],[L,"fastly"],[D,"deno"],[O,"bun"],[c,"node"]];function G(){const e=F.find(s=>s[0]);if(e)return {name:e[1]}}const P=G();P?.name||"";

const scheduledTasks = false;

const tasks = {
  
};

const __runningTasks__ = {};
async function runTask(name, {
  payload = {},
  context = {}
} = {}) {
  if (__runningTasks__[name]) {
    return __runningTasks__[name];
  }
  if (!(name in tasks)) {
    throw createError({
      message: `Task \`${name}\` is not available!`,
      statusCode: 404
    });
  }
  if (!tasks[name].resolve) {
    throw createError({
      message: `Task \`${name}\` is not implemented!`,
      statusCode: 501
    });
  }
  const handler = await tasks[name].resolve();
  const taskEvent = { name, payload, context };
  __runningTasks__[name] = handler.run(taskEvent);
  try {
    const res = await __runningTasks__[name];
    return res;
  } finally {
    delete __runningTasks__[name];
  }
}

function buildAssetsDir() {
  return useRuntimeConfig().app.buildAssetsDir;
}
function buildAssetsURL(...path) {
  return joinRelativeURL(publicAssetsURL(), buildAssetsDir(), ...path);
}
function publicAssetsURL(...path) {
  const app = useRuntimeConfig().app;
  const publicBase = app.cdnURL || app.baseURL;
  return path.length ? joinRelativeURL(publicBase, ...path) : publicBase;
}

const AUTH_COOKIE_NAME = "handmadein_auth";
const COOKIE_OPTIONS = {
  maxAge: 30 * 24 * 60 * 60,
  // 30 days in seconds
  path: "/",
  // Secure should be true in production but can be false in development
  secure: false,
  // Must be false to be accessible by JavaScript
  sameSite: "lax"
  // 'lax' allows the cookie to be sent when navigating to the site from an external link
  // Don't set domain or expires - let the browser handle those
};

const getMedusaClient$1 = () => {
  const config = useRuntimeConfig();
  const baseUrl = config.public.medusaApiUrl;
  const publishableApiKey = config.public.medusaPublishableKey;
  return new Medusa({
    baseUrl,
    maxRetries: 3,
    publishableApiKey
  });
};

function generateSecureMedusaPassword(userId, serverSecret) {
  const secret = process.env.PASSWORD_SECRET || crypto.createHash("sha256").update("development").digest("hex");
  const hmac = crypto.createHmac("sha256", secret);
  hmac.update(userId);
  const hash = hmac.digest("hex");
  const uppercaseChars = "ABCDEFGHJKLMNPQRSTUVWXYZ";
  const lowercaseChars = "abcdefghijkmnopqrstuvwxyz";
  const numberChars = "23456789";
  const specialChars = "!@#$%^&*_+-=";
  const upperPart = parseInt(hash.substring(0, 8), 16);
  const lowerPart = parseInt(hash.substring(8, 16), 16);
  const numberPart = parseInt(hash.substring(16, 24), 16);
  const specialPart = parseInt(hash.substring(24, 32), 16);
  let password = "";
  for (let i = 0; i < 3 + upperPart % 3; i++) {
    password += uppercaseChars[upperPart % uppercaseChars.length];
  }
  for (let i = 0; i < 3 + lowerPart % 3; i++) {
    password += lowercaseChars[lowerPart % lowercaseChars.length];
  }
  for (let i = 0; i < 3 + numberPart % 3; i++) {
    password += numberChars[numberPart % numberChars.length];
  }
  for (let i = 0; i < 2 + specialPart % 2; i++) {
    password += specialChars[specialPart % specialChars.length];
  }
  const passwordArray = password.split("");
  for (let i = passwordArray.length - 1; i > 0; i--) {
    const j = parseInt(hash.charAt(i % hash.length) + hash.charAt((i + 1) % hash.length), 16) % (i + 1);
    [passwordArray[i], passwordArray[j]] = [passwordArray[j], passwordArray[i]];
  }
  return passwordArray.join("");
}
const passwordCache = /* @__PURE__ */ new Map();
function getOrCreateSecurePassword(userId, serverSecret) {
  if (!passwordCache.has(userId)) {
    passwordCache.set(userId, generateSecureMedusaPassword(userId));
  }
  return passwordCache.get(userId);
}

const getSession = async (event) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k;
  try {
    const cookies = parseCookies(event);
    const authCookie = cookies[AUTH_COOKIE_NAME];
    if (authCookie) {
      try {
        const authData = JSON.parse(authCookie);
        if (authData.isAuthenticated && authData.userId && (!((_a = authData.medusa) == null ? void 0 : _a.customer) || !((_b = authData.medusa) == null ? void 0 : _b.token))) {
          console.log("Found Cognito authenticated user with missing Medusa customer");
          try {
            const currentUser = await getCurrentUser();
            if (currentUser) {
              console.log("Got current Cognito user, attempting to sync with Medusa");
              const userToSync = {
                userId: authData.userId || currentUser.userId,
                username: authData.username || currentUser.username,
                email: authData.email,
                attributes: {
                  email: authData.email,
                  given_name: ((_c = authData.name) == null ? void 0 : _c.split(" ")[0]) || "",
                  family_name: ((_d = authData.name) == null ? void 0 : _d.split(" ").slice(1).join(" ")) || ""
                }
              };
              const medusaCustomer = await syncCognitoToMedusa(event, userToSync);
              if (medusaCustomer) {
                console.log("Successfully synchronized with Medusa from auth cookie data");
                return {
                  provider: "cognito",
                  userId: userToSync.userId,
                  username: userToSync.username,
                  medusaCustomerId: medusaCustomer.id
                };
              }
            }
          } catch (syncError) {
            console.error("Error syncing from auth cookie:", syncError);
          }
        }
      } catch (e) {
        console.error("Failed to parse auth cookie:", e);
      }
    }
    try {
      const currentUser = await getCurrentUser();
      if (currentUser && currentUser.username) {
        const medusaCustomer = await syncCognitoToMedusa(event, currentUser);
        if (!medusaCustomer) {
          console.error("Failed to sync Cognito user to Medusa customer");
          return null;
        }
        return {
          provider: "cognito",
          userId: currentUser.userId,
          username: currentUser.username,
          medusaCustomerId: medusaCustomer.id
        };
      }
    } catch (cognitoError) {
      console.log("No Cognito session found, trying Medusa");
    }
    const authCookieAgain = cookies[AUTH_COOKIE_NAME];
    if (authCookieAgain) {
      try {
        const authData = JSON.parse(authCookieAgain);
        if ((_e = authData.medusa) == null ? void 0 : _e.token) {
          const medusaToken2 = authData.medusa.token;
          try {
            const payload = jwt.decode(medusaToken2);
            if (payload && typeof payload === "object") {
              return {
                provider: "medusa",
                userId: payload.customer_id || ((_f = authData.medusa.customer) == null ? void 0 : _f.id),
                username: payload.email || ((_g = authData.medusa.customer) == null ? void 0 : _g.email) || "",
                medusaCustomerId: payload.customer_id || ((_h = authData.medusa.customer) == null ? void 0 : _h.id)
              };
            }
          } catch (jwtError) {
            console.log("JWT decode failed, but will try to use token anyway");
          }
          if (authData.medusa.customer) {
            return {
              provider: "medusa",
              userId: authData.medusa.customer.id,
              username: authData.medusa.customer.email || "",
              medusaCustomerId: authData.medusa.customer.id
            };
          }
        } else if ((_i = authData.medusa) == null ? void 0 : _i.customer) {
          return {
            provider: "medusa",
            userId: authData.medusa.customer.id,
            username: authData.medusa.customer.email || "",
            medusaCustomerId: authData.medusa.customer.id
          };
        } else if (authData.isAuthenticated && authData.userId && authData.email) {
          console.log("Found authenticated user but no Medusa info, attempting sync");
          const userToSync = {
            userId: authData.userId,
            username: authData.username,
            email: authData.email,
            attributes: {
              email: authData.email,
              given_name: ((_j = authData.name) == null ? void 0 : _j.split(" ")[0]) || "",
              family_name: ((_k = authData.name) == null ? void 0 : _k.split(" ").slice(1).join(" ")) || ""
            }
          };
          const medusaCustomer = await syncCognitoToMedusa(event, userToSync);
          if (medusaCustomer) {
            console.log("Successfully created Medusa customer from auth cookie data");
            return {
              provider: "cognito",
              userId: userToSync.userId,
              username: userToSync.username,
              medusaCustomerId: medusaCustomer.id
            };
          }
        }
      } catch (parseError) {
        console.error("Error parsing auth cookie:", parseError);
      }
    }
    const medusaToken = cookies["medusaToken"] || getCookie(event, "medusaToken");
    if (medusaToken) {
      try {
        const payload = jwt.decode(medusaToken);
        if (payload && typeof payload === "object" && payload.customer_id) {
          return {
            provider: "medusa",
            userId: payload.customer_id,
            username: payload.email || "",
            medusaCustomerId: payload.customer_id
          };
        }
      } catch (jwtError) {
      }
    }
    console.log("No valid session found");
    return null;
  } catch (error) {
    console.error("Error getting session:", error);
    return null;
  }
};
const syncCognitoToMedusa = async (event, cognitoUser, retryCount = 0) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i;
  try {
    const maxRetries = 3;
    console.log(`Syncing Cognito user to Medusa (attempt ${retryCount + 1}/${maxRetries + 1})`);
    const cookies = parseCookies(event);
    const authCookie = cookies[AUTH_COOKIE_NAME];
    let authData = {};
    if (authCookie) {
      try {
        authData = JSON.parse(authCookie);
        if (((_b = (_a = authData.medusa) == null ? void 0 : _a.customer) == null ? void 0 : _b.id) && ((_c = authData.medusa) == null ? void 0 : _c.token) && ((_d = authData.cognito) == null ? void 0 : _d.userId) === cognitoUser.userId) {
          console.log("Using existing Medusa customer from auth cookie");
          try {
            const medusaClient2 = getMedusaClient$1();
            const customHeaders = {
              Cookie: `connect.sid=${authData.medusa.token}`,
              Authorization: `Bearer ${authData.medusa.token}`
            };
            const response = await medusaClient2.customers.retrieve(customHeaders);
            if (response.customer) {
              console.log("Successfully verified existing Medusa customer");
              return response.customer;
            }
          } catch (verifyError) {
            console.log("Failed to verify existing customer, will create new one");
          }
        }
      } catch (e) {
        console.error("Failed to parse auth cookie:", e);
      }
    }
    const medusaClient = getMedusaClient$1();
    const email = getEmailFromCognitoUser(cognitoUser);
    if (!email) {
      console.error("No email found in Cognito user");
      return null;
    }
    const securePassword = getOrCreateSecurePassword(email);
    let firstName = ((_e = cognitoUser.attributes) == null ? void 0 : _e.given_name) || "";
    let lastName = ((_f = cognitoUser.attributes) == null ? void 0 : _f.family_name) || "";
    if ((_g = cognitoUser.attributes) == null ? void 0 : _g.name) {
      const nameParts = cognitoUser.attributes.name.split(" ");
      firstName = nameParts[0] || firstName;
      lastName = nameParts.slice(1).join(" ") || lastName;
    } else if (authData.name) {
      const nameParts = authData.name.split(" ");
      firstName = nameParts[0] || firstName;
      lastName = nameParts.slice(1).join(" ") || lastName;
    }
    const phone = ((_h = cognitoUser.attributes) == null ? void 0 : _h.phone_number) || "";
    console.log(`Will sync with Medusa using email: ${email}, name: ${firstName} ${lastName}`);
    console.log(`Attempting to log in to Medusa with email: ${email}`);
    try {
      const response = await medusaClient.auth.authenticate({
        email,
        password: securePassword
      });
      console.log("Successfully logged in to Medusa with existing account");
      if (response.customer && response.customer.has_account === false) {
        console.log("Customer exists but is a guest account (has_account: false), converting to registered account");
        try {
          const updateResponse = await medusaClient.customers.update({
            email,
            first_name: firstName,
            last_name: lastName,
            phone,
            password: securePassword
          }, {
            headers: {
              Cookie: `connect.sid=${getSessionTokenFromResponse(response)}`,
              Authorization: `Bearer ${getSessionTokenFromResponse(response)}`
            }
          });
          if (updateResponse.customer) {
            console.log("Successfully converted guest account to registered account");
            Object.assign(response.customer, updateResponse.customer);
          } else {
            console.error("Failed to convert guest account to registered account");
          }
        } catch (updateError) {
          console.error("Error converting guest account to registered account:", updateError);
        }
      }
      if (!authData.medusa) {
        authData.medusa = {};
      }
      authData.medusa = {
        customer: response.customer,
        token: getSessionTokenFromResponse(response),
        authenticated: true
      };
      authData.cognito = {
        userId: cognitoUser.userId,
        username: cognitoUser.username
      };
      if (!authData.isAuthenticated) {
        authData.isAuthenticated = true;
      }
      if (!authData.userId) {
        authData.userId = cognitoUser.userId;
      }
      if (!authData.username) {
        authData.username = cognitoUser.username;
      }
      if (!authData.email) {
        authData.email = email;
      }
      setCookie(event, AUTH_COOKIE_NAME, JSON.stringify(authData), {
        httpOnly: false,
        path: "/",
        maxAge: 30 * 24 * 60 * 60,
        // 30 days
        sameSite: "lax"
      });
      return response.customer;
    } catch (loginError) {
      console.log("Medusa login failed, will try to create a new customer");
    }
    try {
      console.log(`Creating new Medusa customer for: ${email}`);
      const createResponse = await medusaClient.customers.create({
        email,
        first_name: firstName,
        last_name: lastName,
        phone,
        password: securePassword
      });
      if (!createResponse.customer) {
        throw new Error("No customer returned from create operation");
      }
      console.log("Successfully created new Medusa customer");
      if (!authData.medusa) {
        authData.medusa = {};
      }
      authData.medusa = {
        customer: createResponse.customer,
        authenticated: true
        // The create endpoint doesn't return a token, so we'll need to authenticate now
      };
      authData.cognito = {
        userId: cognitoUser.userId,
        username: cognitoUser.username
      };
      if (!authData.isAuthenticated) {
        authData.isAuthenticated = true;
      }
      if (!authData.userId) {
        authData.userId = cognitoUser.userId;
      }
      if (!authData.username) {
        authData.username = cognitoUser.username;
      }
      if (!authData.email) {
        authData.email = email;
      }
      try {
        console.log("Authenticating with newly created customer");
        const authResponse = await medusaClient.auth.authenticate({
          email,
          password: securePassword
        });
        if (authResponse.customer) {
          authData.medusa.token = getSessionTokenFromResponse(authResponse);
          setCookie(event, AUTH_COOKIE_NAME, JSON.stringify(authData), {
            httpOnly: false,
            path: "/",
            maxAge: 30 * 24 * 60 * 60,
            // 30 days
            sameSite: "lax"
          });
          console.log("Successfully created and authenticated new Medusa customer");
          const updatedCookies = parseCookies(event);
          const updatedAuthCookie = updatedCookies[AUTH_COOKIE_NAME];
          if (updatedAuthCookie) {
            console.log("Updated auth cookie structure:", Object.keys(JSON.parse(updatedAuthCookie)));
          }
          return authResponse.customer;
        } else {
          throw new Error("No customer returned from authentication");
        }
      } catch (authError) {
        console.error("Error authenticating after customer creation:", authError);
        return createResponse.customer;
      }
    } catch (createError2) {
      console.error("Failed to create Medusa customer:", createError2.message || createError2);
      if (((_i = createError2.response) == null ? void 0 : _i.status) === 422 || typeof createError2.message === "string" && createError2.message.includes("already exists")) {
        console.log("Customer might already exist in Medusa, trying password reset flow");
        try {
          const possiblePasswords = [
            securePassword,
            `Cognito_${cognitoUser.username}`,
            `Cognito_${cognitoUser.email}`,
            email
            // Some systems set email as initial password
          ];
          for (const pwd of possiblePasswords) {
            try {
              console.log(`Trying login with alternative password`);
              const response = await medusaClient.auth.authenticate({
                email,
                password: pwd
              });
              if (response.customer) {
                console.log("Successfully authenticated with alternative password");
                if (!authData.medusa) {
                  authData.medusa = {};
                }
                authData.medusa = {
                  customer: response.customer,
                  token: getSessionTokenFromResponse(response),
                  authenticated: true
                };
                authData.cognito = {
                  userId: cognitoUser.userId,
                  username: cognitoUser.username
                };
                if (!authData.isAuthenticated) {
                  authData.isAuthenticated = true;
                }
                if (!authData.userId) {
                  authData.userId = cognitoUser.userId;
                }
                if (!authData.username) {
                  authData.username = cognitoUser.username;
                }
                if (!authData.email) {
                  authData.email = email;
                }
                setCookie(event, AUTH_COOKIE_NAME, JSON.stringify(authData), {
                  httpOnly: false,
                  path: "/",
                  maxAge: 30 * 24 * 60 * 60,
                  // 30 days
                  sameSite: "lax"
                });
                const updatedCookies = parseCookies(event);
                const updatedAuthCookie = updatedCookies[AUTH_COOKIE_NAME];
                if (updatedAuthCookie) {
                  console.log("Updated auth cookie structure:", Object.keys(JSON.parse(updatedAuthCookie)));
                }
                return response.customer;
              }
            } catch (e) {
            }
          }
          console.log("All alternative password attempts failed, trying one more create with different password");
          try {
            const altPassword = `Cognito_${email.split("@")[0]}`;
            const createResponse = await medusaClient.customers.create({
              email,
              first_name: firstName || email.split("@")[0],
              last_name: lastName || "",
              password: altPassword
            });
            if (createResponse.customer) {
              console.log("Successfully created customer with alternative password");
              if (!authData.medusa) {
                authData.medusa = {};
              }
              authData.medusa = {
                customer: createResponse.customer,
                authenticated: true
              };
              try {
                const authResponse = await medusaClient.auth.authenticate({
                  email,
                  password: altPassword
                });
                if (authResponse.customer) {
                  authData.medusa.token = getSessionTokenFromResponse(authResponse);
                  authData.cognito = {
                    userId: cognitoUser.userId,
                    username: cognitoUser.username
                  };
                  if (!authData.isAuthenticated) {
                    authData.isAuthenticated = true;
                  }
                  if (!authData.userId) {
                    authData.userId = cognitoUser.userId;
                  }
                  if (!authData.username) {
                    authData.username = cognitoUser.username;
                  }
                  if (!authData.email) {
                    authData.email = email;
                  }
                  setCookie(event, AUTH_COOKIE_NAME, JSON.stringify(authData), {
                    httpOnly: false,
                    path: "/",
                    maxAge: 30 * 24 * 60 * 60,
                    // 30 days
                    sameSite: "lax"
                  });
                  console.log("Successfully created and authenticated with alternative password");
                  return authResponse.customer;
                }
              } catch (e) {
                return createResponse.customer;
              }
            }
          } catch (e) {
            console.log("Alternative create attempt also failed");
          }
        } catch (resetError) {
          console.error("Error with password reset flow:", resetError);
        }
      }
      if (retryCount >= maxRetries) {
        console.error(`Failed to sync Cognito user to Medusa after ${maxRetries + 1} attempts`);
        return null;
      }
      console.log(`Retrying Cognito to Medusa sync (attempt ${retryCount + 2}/${maxRetries + 1})`);
      await new Promise((resolve) => setTimeout(resolve, 1e3));
      return syncCognitoToMedusa(event, cognitoUser, retryCount + 1);
    }
  } catch (error) {
    console.error("Unexpected error in syncCognitoToMedusa:", error);
    return null;
  }
};
function getEmailFromCognitoUser(cognitoUser) {
  var _a;
  if (cognitoUser.email) {
    return cognitoUser.email;
  }
  if ((_a = cognitoUser.attributes) == null ? void 0 : _a.email) {
    return cognitoUser.attributes.email;
  }
  if (cognitoUser.username && cognitoUser.username.includes("@")) {
    return cognitoUser.username;
  }
  return null;
}
function getSessionTokenFromResponse(response) {
  if (response.jwt) {
    return response.jwt;
  }
  if (response.session_id) {
    return response.session_id;
  }
  return null;
}
async function syncCognitoToMedusaDirectly(email, medusaUrl, fullName) {
  if (!email || false) {
    console.error("Email and Medusa URL are required for syncing");
    return null;
  }
  try {
    console.log(`Syncing Cognito user to Medusa: ${email}`);
    let firstName = "Customer";
    let lastName = "Account";
    if (fullName) {
      const nameParts = fullName.split(" ");
      firstName = nameParts[0] || firstName;
      lastName = nameParts.slice(1).join(" ") || lastName;
    }
    const securePassword = getOrCreateSecurePassword(email);
    console.log("Attempting to login to Medusa with secure password");
    const loginResponse = await fetch(`${medusaUrl}/auth/customer/emailpass`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        email,
        password: securePassword
      })
    });
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log("Successfully logged in to Medusa");
      const token = loginData.token || loginData.access_token;
      if (token) {
        console.log("Checking if this is a guest account");
        try {
          const customerProfileResponse = await fetch(`${medusaUrl}/store/customers/me`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          });
          if (customerProfileResponse.ok) {
            const customerData = await customerProfileResponse.json();
            if (customerData.customer && customerData.customer.has_account === false) {
              console.log("Found guest account, converting to registered account");
              const updateResponse = await fetch(`${medusaUrl}/store/customers`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "Authorization": `Bearer ${token}`
                },
                body: JSON.stringify({
                  email,
                  first_name: firstName,
                  last_name: lastName,
                  password: securePassword
                })
              });
              if (updateResponse.ok) {
                console.log("Successfully converted guest account to registered account");
                return token;
              } else {
                console.error("Failed to convert guest account to registered account:", await updateResponse.text());
              }
            } else {
              console.log("Customer already has a registered account (has_account: true)");
            }
          } else {
            console.error("Failed to fetch customer profile:", await customerProfileResponse.text());
          }
        } catch (profileError) {
          console.error("Error checking customer profile:", profileError);
        }
        return token;
      }
    }
    console.log("Login failed, attempting to register in Medusa");
    const registerResponse = await fetch(`${medusaUrl}/auth/customer/emailpass/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        email,
        password: securePassword
      })
    });
    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      console.log("Successfully registered in Medusa");
      const customerResponse = await fetch(`${medusaUrl}/store/customers`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${registerData.token}`
        },
        body: JSON.stringify({
          email,
          first_name: firstName,
          last_name: lastName
        })
      });
      if (customerResponse.ok) {
        console.log("Successfully created customer in Medusa");
        return registerData.token;
      } else {
        console.error("Failed to create customer in Medusa");
      }
      return registerData.token;
    }
    const registerError = await registerResponse.json().catch(() => ({}));
    if (registerError.type === "unauthorized" && registerError.message === "Identity with email already exists") {
      console.log("Identity exists but credentials mismatch, cannot sync");
    } else {
      console.error("Failed to register in Medusa:", registerError);
    }
    return null;
  } catch (error) {
    console.error("Error syncing Cognito to Medusa:", error);
    return null;
  }
}
async function getCustomerIdFromAuth(event) {
  var _a, _b;
  try {
    const session = await getSession(event);
    if (!session) {
      return null;
    }
    if (session.medusaCustomerId) {
      return session.medusaCustomerId;
    }
    if ((_b = (_a = session.medusa) == null ? void 0 : _a.customer) == null ? void 0 : _b.id) {
      return session.medusa.customer.id;
    }
    const emailToUse = session.username && typeof session.username === "string" && session.username.includes("@") ? session.username : null;
    if (emailToUse) {
      const config = useRuntimeConfig();
      const medusaUrl = config.medusaUrl || "https://api.handmadein.ro";
      const adminToken = config.medusaAdminToken;
      if (!adminToken) {
        console.error("Admin token not found in configuration");
        return null;
      }
      try {
        const searchResponse = await fetch(`${medusaUrl}/admin/customers?q=${encodeURIComponent(emailToUse)}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${adminToken}`
          }
        });
        if (!searchResponse.ok) {
          console.error("Failed to search for customer by email");
          return null;
        }
        const searchData = await searchResponse.json();
        if (searchData.customers && searchData.customers.length > 0) {
          const customerId = searchData.customers[0].id;
          return customerId;
        }
      } catch (error) {
        console.error("Error finding customer by email:", error);
      }
    }
    return null;
  } catch (error) {
    console.error("Error getting customer ID from auth:", error);
    return null;
  }
}

const warnOnceSet = /* @__PURE__ */ new Set();
const DEFAULT_ENDPOINT = "https://api.iconify.design";
const _eiaC0S = defineCachedEventHandler(async (event) => {
  const url = getRequestURL(event);
  if (!url)
    return createError({ status: 400, message: "Invalid icon request" });
  const options = useAppConfig().icon;
  const collectionName = event.context.params?.collection?.replace(/\.json$/, "");
  const collection = collectionName ? await collections[collectionName]?.() : null;
  const apiEndPoint = options.iconifyApiEndpoint || DEFAULT_ENDPOINT;
  const icons = url.searchParams.get("icons")?.split(",");
  if (collection) {
    if (icons?.length) {
      const data = getIcons(
        collection,
        icons
      );
      consola.debug(`[Icon] serving ${(icons || []).map((i) => "`" + collectionName + ":" + i + "`").join(",")} from bundled collection`);
      return data;
    }
  } else {
    if (collectionName && !warnOnceSet.has(collectionName) && apiEndPoint === DEFAULT_ENDPOINT) {
      consola.warn([
        `[Icon] Collection \`${collectionName}\` is not found locally`,
        `We suggest to install it via \`npm i -D @iconify-json/${collectionName}\` to provide the best end-user experience.`
      ].join("\n"));
      warnOnceSet.add(collectionName);
    }
  }
  if (options.fallbackToApi === true || options.fallbackToApi === "server-only") {
    const apiUrl = new URL("./" + basename(url.pathname) + url.search, apiEndPoint);
    consola.debug(`[Icon] fetching ${(icons || []).map((i) => "`" + collectionName + ":" + i + "`").join(",")} from iconify api`);
    if (apiUrl.host !== new URL(apiEndPoint).host) {
      return createError({ status: 400, message: "Invalid icon request" });
    }
    try {
      const data = await $fetch(apiUrl.href);
      return data;
    } catch (e) {
      consola.error(e);
      if (e.status === 404)
        return createError({ status: 404 });
      else
        return createError({ status: 500, message: "Failed to fetch fallback icon" });
    }
  }
  return createError({ status: 404 });
}, {
  group: "nuxt",
  name: "icon",
  getKey(event) {
    const collection = event.context.params?.collection?.replace(/\.json$/, "") || "unknown";
    const icons = String(getQuery$1(event).icons || "");
    return `${collection}_${icons.split(",")[0]}_${icons.length}_${hash(icons)}`;
  },
  swr: true,
  maxAge: 60 * 60 * 24 * 7
  // 1 week
});

const _ABy88F = lazyEventHandler(() => {
  const opts = useRuntimeConfig().ipx || {};
  const fsDir = opts?.fs?.dir ? (Array.isArray(opts.fs.dir) ? opts.fs.dir : [opts.fs.dir]).map((dir) => isAbsolute(dir) ? dir : fileURLToPath(new URL(dir, globalThis._importMeta_.url))) : void 0;
  const fsStorage = opts.fs?.dir ? ipxFSStorage({ ...opts.fs, dir: fsDir }) : void 0;
  const httpStorage = opts.http?.domains ? ipxHttpStorage({ ...opts.http }) : void 0;
  if (!fsStorage && !httpStorage) {
    throw new Error("IPX storage is not configured!");
  }
  const ipxOptions = {
    ...opts,
    storage: fsStorage || httpStorage,
    httpStorage
  };
  const ipx = createIPX(ipxOptions);
  const ipxHandler = createIPXH3Handler(ipx);
  return useBase(opts.baseURL, ipxHandler);
});

const _lazy_xQ1DI1 = () => Promise.resolve().then(function () { return _____$1; });
const _lazy_JiUMRb = () => Promise.resolve().then(function () { return cookieFix$1; });
const _lazy_5wesNI = () => Promise.resolve().then(function () { return currentUser$1; });
const _lazy_u8nyXl = () => Promise.resolve().then(function () { return debug$1; });
const _lazy_9k9C53 = () => Promise.resolve().then(function () { return login$1; });
const _lazy_45v9RT = () => Promise.resolve().then(function () { return signin$1; });
const _lazy_LP2YkR = () => Promise.resolve().then(function () { return signout$1; });
const _lazy_ssle0K = () => Promise.resolve().then(function () { return syncMedusa$1; });
const _lazy_f4NtYD = () => Promise.resolve().then(function () { return contact$1; });
const _lazy_3yePj5 = () => Promise.resolve().then(function () { return addresses$1; });
const _lazy_plL724 = () => Promise.resolve().then(function () { return _id_$5; });
const _lazy_KiZsTW = () => Promise.resolve().then(function () { return me$1; });
const _lazy_irZLtx = () => Promise.resolve().then(function () { return password$1; });
const _lazy_9EAKvD = () => Promise.resolve().then(function () { return customer$1; });
const _lazy_aR7UBD = () => Promise.resolve().then(function () { return _id_$3; });
const _lazy_l4NmIi = () => Promise.resolve().then(function () { return updateCustomer$1; });
const _lazy_YMB39u = () => Promise.resolve().then(function () { return index$1; });
const _lazy_6850vL = () => Promise.resolve().then(function () { return _id_$1; });
const _lazy_qBGyAz = () => Promise.resolve().then(function () { return prices$1; });
const _lazy_crxQn7 = () => Promise.resolve().then(function () { return renderer$1; });

const handlers = [
  { route: '', handler: _NCQKQE, lazy: false, middleware: true, method: undefined },
  { route: '', handler: _lIvYoZ, lazy: false, middleware: true, method: undefined },
  { route: '/api/auth/**', handler: _lazy_xQ1DI1, lazy: true, middleware: false, method: undefined },
  { route: '/api/auth/cookie-fix', handler: _lazy_JiUMRb, lazy: true, middleware: false, method: undefined },
  { route: '/api/auth/current-user', handler: _lazy_5wesNI, lazy: true, middleware: false, method: undefined },
  { route: '/api/auth/debug', handler: _lazy_u8nyXl, lazy: true, middleware: false, method: undefined },
  { route: '/api/auth/login', handler: _lazy_9k9C53, lazy: true, middleware: false, method: undefined },
  { route: '/api/auth/signin', handler: _lazy_45v9RT, lazy: true, middleware: false, method: undefined },
  { route: '/api/auth/signout', handler: _lazy_LP2YkR, lazy: true, middleware: false, method: undefined },
  { route: '/api/auth/sync-medusa', handler: _lazy_ssle0K, lazy: true, middleware: false, method: undefined },
  { route: '/api/contact', handler: _lazy_f4NtYD, lazy: true, middleware: false, method: undefined },
  { route: '/api/customers/addresses', handler: _lazy_3yePj5, lazy: true, middleware: false, method: undefined },
  { route: '/api/customers/addresses/:id', handler: _lazy_plL724, lazy: true, middleware: false, method: undefined },
  { route: '/api/customers/me', handler: _lazy_KiZsTW, lazy: true, middleware: false, method: undefined },
  { route: '/api/customers/password', handler: _lazy_irZLtx, lazy: true, middleware: false, method: undefined },
  { route: '/api/medusa/carts/:id/customer', handler: _lazy_9EAKvD, lazy: true, middleware: false, method: undefined },
  { route: '/api/orders/:id', handler: _lazy_aR7UBD, lazy: true, middleware: false, method: undefined },
  { route: '/api/orders/:id/update-customer', handler: _lazy_l4NmIi, lazy: true, middleware: false, method: undefined },
  { route: '/api/orders', handler: _lazy_YMB39u, lazy: true, middleware: false, method: undefined },
  { route: '/api/products/:id', handler: _lazy_6850vL, lazy: true, middleware: false, method: undefined },
  { route: '/api/products/prices', handler: _lazy_qBGyAz, lazy: true, middleware: false, method: undefined },
  { route: '/__nuxt_error', handler: _lazy_crxQn7, lazy: true, middleware: false, method: undefined },
  { route: '/api/_nuxt_icon/:collection', handler: _eiaC0S, lazy: false, middleware: false, method: undefined },
  { route: '/_ipx/**', handler: _ABy88F, lazy: false, middleware: false, method: undefined },
  { route: '/**', handler: _lazy_crxQn7, lazy: true, middleware: false, method: undefined }
];

function createNitroApp() {
  const config = useRuntimeConfig();
  const hooks = createHooks();
  const captureError = (error, context = {}) => {
    const promise = hooks.callHookParallel("error", error, context).catch((error_) => {
      console.error("Error while capturing another error", error_);
    });
    if (context.event && isEvent(context.event)) {
      const errors = context.event.context.nitro?.errors;
      if (errors) {
        errors.push({ error, context });
      }
      if (context.event.waitUntil) {
        context.event.waitUntil(promise);
      }
    }
  };
  const h3App = createApp({
    debug: destr(true),
    onError: (error, event) => {
      captureError(error, { event, tags: ["request"] });
      return errorHandler(error, event);
    },
    onRequest: async (event) => {
      await nitroApp$1.hooks.callHook("request", event).catch((error) => {
        captureError(error, { event, tags: ["request"] });
      });
    },
    onBeforeResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("beforeResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    },
    onAfterResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("afterResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    }
  });
  const router = createRouter$1({
    preemptive: true
  });
  const localCall = createCall(toNodeListener(h3App));
  const _localFetch = createFetch(localCall, globalThis.fetch);
  const localFetch = (input, init) => _localFetch(input, init).then(
    (response) => normalizeFetchResponse(response)
  );
  const $fetch = createFetch$1({
    fetch: localFetch,
    Headers: Headers$1,
    defaults: { baseURL: config.app.baseURL }
  });
  globalThis.$fetch = $fetch;
  h3App.use(createRouteRulesHandler({ localFetch }));
  h3App.use(
    eventHandler((event) => {
      event.context.nitro = event.context.nitro || { errors: [] };
      const envContext = event.node.req?.__unenv__;
      if (envContext) {
        Object.assign(event.context, envContext);
      }
      event.fetch = (req, init) => fetchWithEvent(event, req, init, { fetch: localFetch });
      event.$fetch = (req, init) => fetchWithEvent(event, req, init, {
        fetch: $fetch
      });
      event.waitUntil = (promise) => {
        if (!event.context.nitro._waitUntilPromises) {
          event.context.nitro._waitUntilPromises = [];
        }
        event.context.nitro._waitUntilPromises.push(promise);
        if (envContext?.waitUntil) {
          envContext.waitUntil(promise);
        }
      };
      event.captureError = (error, context) => {
        captureError(error, { event, ...context });
      };
    })
  );
  for (const h of handlers) {
    let handler = h.lazy ? lazyEventHandler(h.handler) : h.handler;
    if (h.middleware || !h.route) {
      const middlewareBase = (config.app.baseURL + (h.route || "/")).replace(
        /\/+/g,
        "/"
      );
      h3App.use(middlewareBase, handler);
    } else {
      const routeRules = getRouteRulesForPath(
        h.route.replace(/:\w+|\*\*/g, "_")
      );
      if (routeRules.cache) {
        handler = cachedEventHandler(handler, {
          group: "nitro/routes",
          ...routeRules.cache
        });
      }
      router.use(h.route, handler, h.method);
    }
  }
  h3App.use(config.app.baseURL, router.handler);
  const app = {
    hooks,
    h3App,
    router,
    localCall,
    localFetch,
    captureError
  };
  return app;
}
function runNitroPlugins(nitroApp2) {
  for (const plugin of plugins) {
    try {
      plugin(nitroApp2);
    } catch (error) {
      nitroApp2.captureError(error, { tags: ["plugin"] });
      throw error;
    }
  }
}
const nitroApp$1 = createNitroApp();
function useNitroApp() {
  return nitroApp$1;
}
runNitroPlugins(nitroApp$1);

const nitroApp = useNitroApp();
const server = new Server(toNodeListener(nitroApp.h3App));
function getAddress() {
  if (p === "stackblitz" || process.env.NITRO_NO_UNIX_SOCKET || process.versions.bun) {
    return 0;
  }
  const socketName = `worker-${process.pid}-${threadId}.sock`;
  if (A) {
    return join(String.raw`\\.\pipe\nitro`, socketName);
  }
  const socketDir = join(tmpdir(), "nitro");
  mkdirSync(socketDir, { recursive: true });
  return join(socketDir, socketName);
}
const listenAddress = getAddress();
server.listen(listenAddress, () => {
  const _address = server.address();
  parentPort?.postMessage({
    event: "listen",
    address: typeof _address === "string" ? { socketPath: _address } : { host: "localhost", port: _address?.port }
  });
});
nitroApp.router.get(
  "/_nitro/tasks",
  defineEventHandler(async (event) => {
    const _tasks = await Promise.all(
      Object.entries(tasks).map(async ([name, task]) => {
        const _task = await task.resolve?.();
        return [name, { description: _task?.meta?.description }];
      })
    );
    return {
      tasks: Object.fromEntries(_tasks),
      scheduledTasks
    };
  })
);
nitroApp.router.use(
  "/_nitro/tasks/:name",
  defineEventHandler(async (event) => {
    const name = getRouterParam(event, "name");
    const payload = {
      ...getQuery$1(event),
      ...await readBody(event).then((r) => r?.payload).catch(() => ({}))
    };
    return await runTask(name, { payload });
  })
);
trapUnhandledNodeErrors();
async function onShutdown(signal) {
  await nitroApp.hooks.callHook("close");
}
parentPort?.on("message", async (msg) => {
  if (msg && msg.event === "shutdown") {
    await onShutdown();
    parentPort?.postMessage({ event: "exit" });
  }
});

const _messages = { "appName": "Nuxt", "version": "", "statusCode": 500, "statusMessage": "Server error", "description": "An error occurred in the application and the page could not be served. If you are the application owner, check your server logs for details.", "stack": "" };
const template$1 = (messages) => {
  messages = { ..._messages, ...messages };
  return '<!DOCTYPE html><html lang="en"><head><title>' + messages.statusCode + " - " + messages.statusMessage + " | " + messages.appName + `</title><meta charset="utf-8"><meta content="width=device-width,initial-scale=1.0,minimum-scale=1.0" name="viewport"><style>.spotlight{background:linear-gradient(45deg,#00dc82,#36e4da 50%,#0047e1);bottom:-40vh;filter:blur(30vh);height:60vh;opacity:.8}*,:after,:before{border-color:var(--un-default-border-color,#e5e7eb);border-style:solid;border-width:0;box-sizing:border-box}:after,:before{--un-content:""}html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}body{line-height:inherit;margin:0}h1{font-size:inherit;font-weight:inherit}pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-size:1em;font-variation-settings:normal}h1,p,pre{margin:0}*,:after,:before{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 transparent;--un-ring-shadow:0 0 transparent;--un-shadow-inset: ;--un-shadow:0 0 transparent;--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgba(147,197,253,.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: }.fixed{position:fixed}.left-0{left:0}.right-0{right:0}.z-10{z-index:10}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.h-auto{height:auto}.min-h-screen{min-height:100vh}.flex{display:flex}.flex-1{flex:1 1 0%}.flex-col{flex-direction:column}.overflow-y-auto{overflow-y:auto}.rounded-t-md{border-top-left-radius:.375rem;border-top-right-radius:.375rem}.bg-black\\/5{background-color:#0000000d}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255/var(--un-bg-opacity))}.p-8{padding:2rem}.px-10{padding-left:2.5rem;padding-right:2.5rem}.pt-14{padding-top:3.5rem}.text-6xl{font-size:3.75rem;line-height:1}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-black{--un-text-opacity:1;color:rgb(0 0 0/var(--un-text-opacity))}.font-light{font-weight:300}.font-medium{font-weight:500}.leading-tight{line-height:1.25}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme:dark){.dark\\:bg-black{--un-bg-opacity:1;background-color:rgb(0 0 0/var(--un-bg-opacity))}.dark\\:bg-white\\/10{background-color:#ffffff1a}.dark\\:text-white{--un-text-opacity:1;color:rgb(255 255 255/var(--un-text-opacity))}}@media (min-width:640px){.sm\\:text-2xl{font-size:1.5rem;line-height:2rem}.sm\\:text-8xl{font-size:6rem;line-height:1}}</style><script>!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))r(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)})).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();<\/script></head><body class="antialiased bg-white dark:bg-black dark:text-white flex flex-col font-sans min-h-screen pt-14 px-10 text-black"><div class="fixed left-0 right-0 spotlight"></div><h1 class="font-medium mb-6 sm:text-8xl text-6xl">` + messages.statusCode + '</h1><p class="font-light leading-tight mb-8 sm:text-2xl text-xl">' + messages.description + '</p><div class="bg-black/5 bg-white dark:bg-white/10 flex-1 h-auto overflow-y-auto rounded-t-md"><pre class="font-light leading-tight p-8 text-xl z-10">' + messages.stack + "</pre></div></body></html>";
};

const errorDev = /*#__PURE__*/Object.freeze({
  __proto__: null,
  template: template$1
});

const getCognitoClient = () => {
  const config = useRuntimeConfig();
  const region = config.cognitoRegion || process.env.COGNITO_REGION || "eu-central-1";
  return new CognitoIdentityProviderClient({
    region
  });
};
const cognitoClient = getCognitoClient();
const logCognitoConfig = () => {
  var _a;
  const config = useRuntimeConfig();
  console.log("Cognito Config:", {
    regionFromConfig: config.cognitoRegion,
    regionFromEnv: process.env.COGNITO_REGION,
    clientIdFromConfig: ((_a = config.public) == null ? void 0 : _a.cognitoClientId) ? "[SET]" : "[NOT SET]",
    clientIdFromEnv: process.env.COGNITO_CLIENT_ID ? "[SET]" : "[NOT SET]",
    secretFromConfig: config.cognitoClientSecret ? "[SET]" : "[NOT SET]",
    secretFromEnv: process.env.COGNITO_CLIENT_SECRET ? "[SET]" : "[NOT SET]",
    userPoolFromConfig: config.cognitoUserPoolId ? "[SET]" : "[NOT SET]",
    userPoolFromEnv: process.env.COGNITO_USER_POOL_ID ? "[SET]" : "[NOT SET]"
  });
};
logCognitoConfig();
function calculateSecretHash(username, clientId, clientSecret) {
  try {
    const normalizedUsername = username.toLowerCase();
    const message = normalizedUsername + clientId;
    const hash = createHmac("sha256", clientSecret).update(message).digest("base64");
    return hash;
  } catch (error) {
    console.error("Error calculating SECRET_HASH:", error);
    throw new Error("Failed to calculate SECRET_HASH for authentication");
  }
}
const getMedusaClient = () => {
  try {
    const config = useRuntimeConfig();
    const baseUrl = config.public.medusaApiUrl || "https://api.handmadein.ro";
    if (!baseUrl) ;
    return new Medusa({
      baseUrl,
      maxRetries: 3,
      publishableApiKey: config.public.medusaPublishableKey
    });
  } catch (error) {
    console.error("Failed to initialize Medusa client:", error);
    return new Medusa({
      baseUrl: "https://api.handmadein.ro",
      maxRetries: 0
    });
  }
};
const generateSecretHash = (username) => {
  try {
    const config = useRuntimeConfig();
    const clientId = config.public.cognitoClientId || process.env.COGNITO_CLIENT_ID;
    const clientSecret = config.cognitoClientSecret || process.env.COGNITO_CLIENT_SECRET;
    if (!clientId || !clientSecret) {
      console.error("Missing Cognito client ID or secret");
      return "";
    }
    const message = username + clientId;
    return createHmac("sha256", clientSecret).update(message).digest("base64");
  } catch (error) {
    console.error("Failed to generate secret hash:", error);
    return "";
  }
};
const handleSignUp = async (event) => {
  const body = await readBody(event);
  const {
    username,
    password,
    attributes,
    firstName,
    lastName,
    phone,
    addressLine1,
    addressLine2,
    city,
    state,
    postalCode,
    country
  } = body;
  const config = useRuntimeConfig();
  if (!config.cognitoUserPoolId || !config.cognitoClientId || !config.cognitoClientSecret) {
    throw createError({
      statusCode: 400,
      message: "Auth UserPool not configured. Missing Cognito configuration."
    });
  }
  const uniqueUsername = `user_${Date.now()}_${Math.floor(Math.random() * 1e4)}`;
  const addressObj = {
    formatted: `${addressLine1}, ${city}, ${state}, ${postalCode}, ${country}`,
    street_address: addressLine1,
    locality: city,
    region: state,
    postal_code: postalCode,
    country
  };
  const updatedAttributes = {
    ...attributes || {},
    email: username,
    // The username is actually an email in our form
    address: JSON.stringify(addressObj),
    locale: (attributes == null ? void 0 : attributes.locale) || "en",
    // Default locale
    phone_number: phone || "",
    given_name: firstName || "",
    family_name: lastName || "",
    name: `${firstName || ""} ${lastName || ""}`.trim()
  };
  const secretHash = calculateSecretHash(
    uniqueUsername,
    config.cognitoClientId,
    config.cognitoClientSecret
  );
  try {
    const client = new CognitoIdentityProviderClient({
      region: config.cognitoRegion
    });
    const userAttributes = Object.entries(updatedAttributes).map(([key, value]) => ({
      Name: key,
      Value: value
    }));
    const signUpCommand = new SignUpCommand({
      ClientId: config.cognitoClientId,
      Username: uniqueUsername,
      Password: password,
      SecretHash: secretHash,
      UserAttributes: userAttributes
    });
    const cognitoResult = await client.send(signUpCommand);
    return {
      isSignUpComplete: cognitoResult.UserConfirmed,
      nextStep: {
        signUpStep: cognitoResult.UserConfirmed ? "DONE" : "CONFIRM_SIGN_UP"
      },
      userId: cognitoResult.UserSub,
      username: uniqueUsername,
      // Return the generated username for future operations
      email: username
      // Also return the email for reference
    };
  } catch (error) {
    console.error("Signup error:", error);
    throw createError({
      statusCode: 400,
      message: error.message
    });
  }
};
const getAccessToken = async (event, authResult) => {
  try {
    if (authResult && authResult.AccessToken) ;
    const tokenStorageKey = "amplify-signin-with-hostedUI-oauth2-access-token";
    if (event) {
      const cookies = parseCookies(event);
      const authCookie = cookies[AUTH_COOKIE_NAME];
      if (authCookie) {
        try {
          const authData = JSON.parse(authCookie);
          if (authData.accessToken) {
            return authData.accessToken;
          }
        } catch (e) {
          console.error("Error parsing auth cookie", e);
        }
      }
    }
    if (false) ;
    return null;
  } catch (error) {
    console.error("Error getting access token", error);
    return null;
  }
};
const findUserByEmail = async (usernameOrEmail) => {
  try {
    const config = useRuntimeConfig();
    const userPoolId = config.cognitoUserPoolId || process.env.COGNITO_USER_POOL_ID;
    if (!userPoolId) {
      console.error("Missing Cognito user pool ID");
      return null;
    }
    const command = new ListUsersCommand({
      UserPoolId: userPoolId,
      Filter: usernameOrEmail.includes("@") ? `email = "${usernameOrEmail}"` : `username = "${usernameOrEmail}"`,
      Limit: 1
    });
    const response = await cognitoClient.send(command);
    if (response.Users && response.Users.length > 0) {
      return response.Users[0];
    }
    return null;
  } catch (error) {
    console.error("Error finding user by email", error);
    return null;
  }
};
const handleSignIn = async (event) => {
  var _a, _b, _c, _d;
  try {
    const body = await readBody(event);
    const username = body.username;
    const password = body.password;
    if (!username || !password) {
      throw createError({
        statusCode: 400,
        statusMessage: "Username and password are required"
      });
    }
    const config = useRuntimeConfig();
    const clientId = config.public.cognitoClientId || process.env.COGNITO_CLIENT_ID;
    if (!clientId) {
      throw createError({
        statusCode: 500,
        statusMessage: "Missing Cognito client configuration"
      });
    }
    const secretHash = generateSecretHash(username);
    const signInCommand = new InitiateAuthCommand({
      AuthFlow: "USER_PASSWORD_AUTH",
      ClientId: clientId,
      AuthParameters: {
        USERNAME: username,
        PASSWORD: password,
        SECRET_HASH: secretHash
      }
    });
    console.log("Attempting to sign in user:", username);
    const result = await cognitoClient.send(signInCommand);
    console.log("Sign in result received, challenge name:", result.ChallengeName || "none");
    if (result.ChallengeName) {
      return {
        success: false,
        nextChallenge: {
          challengeName: result.ChallengeName,
          challengeParameters: result.ChallengeParameters,
          session: result.Session
        }
      };
    }
    const idToken = (_a = result.AuthenticationResult) == null ? void 0 : _a.IdToken;
    const accessToken = (_b = result.AuthenticationResult) == null ? void 0 : _b.AccessToken;
    const refreshToken = (_c = result.AuthenticationResult) == null ? void 0 : _c.RefreshToken;
    const expiresIn = (_d = result.AuthenticationResult) == null ? void 0 : _d.ExpiresIn;
    console.log("Authentication successful, access token received:", !!accessToken);
    let username_from_token = null;
    let userId = null;
    let userAttributes = {};
    if (accessToken) {
      try {
        const getUserCommand = new GetUserCommand({
          AccessToken: accessToken
        });
        const userResult = await cognitoClient.send(getUserCommand);
        username_from_token = userResult.Username;
        if (userResult.UserAttributes) {
          userAttributes = userResult.UserAttributes.reduce((attrs, attr) => {
            if (attr.Name && attr.Value) {
              attrs[attr.Name] = attr.Value;
              if (attr.Name === "sub") {
                userId = attr.Value;
              }
            }
            return attrs;
          }, {});
        }
        console.log("User info retrieved, userId:", userId);
      } catch (userInfoError) {
        console.error("Error getting user info:", userInfoError);
      }
    }
    let medusaResult = { authenticated: false };
    let medusaToken = null;
    try {
      const medusa = getMedusaClient();
      const response = await medusa.auth.authenticate({
        email: username,
        password
      });
      if (response.customer) {
        const authResponse = response;
        medusaToken = authResponse.access_token;
        medusaResult = {
          customer: response.customer,
          authenticated: true,
          token: medusaToken
        };
        console.log("Medusa authentication successful, token received:", !!medusaToken);
      }
    } catch (medusaError) {
      console.log("Medusa authentication failed, but Cognito succeeded", medusaError);
      medusaResult = {
        authenticated: false,
        error: medusaError.message
      };
    }
    const cookieData = {
      isAuthenticated: true,
      userId: userId || "",
      username: username_from_token || username,
      email: username.includes("@") ? username : (userAttributes == null ? void 0 : userAttributes.email) || "",
      name: (userAttributes == null ? void 0 : userAttributes.name) || username_from_token || username,
      accessToken,
      expiration: expiresIn ? Date.now() + expiresIn * 1e3 : void 0,
      medusa: {
        authenticated: medusaResult.authenticated,
        customer: medusaResult.customer,
        token: medusaToken
      }
    };
    setAuthCookie(event, AUTH_COOKIE_NAME, JSON.stringify(cookieData));
    console.log("Auth cookie set successfully in handleSignIn");
    return {
      success: true,
      user: {
        userId: userId || "",
        username: username_from_token || username,
        email: username.includes("@") ? username : (userAttributes == null ? void 0 : userAttributes.email) || "",
        name: (userAttributes == null ? void 0 : userAttributes.name) || username_from_token || username,
        medusa: {
          authenticated: medusaResult.authenticated,
          customer: medusaResult.customer,
          token: medusaToken
        }
      },
      message: "Authentication successful"
    };
  } catch (error) {
    console.error("Sign in error:", error);
    const body = await readBody(event);
    const username = body.username || "";
    if (error.name === "UserNotConfirmedException") {
      return {
        success: false,
        error: "User is not confirmed",
        nextChallenge: {
          challengeName: "USER_NOT_CONFIRMED",
          username
        }
      };
    }
    if (error.name === "NotAuthorizedException") {
      throw createError({
        statusCode: 401,
        statusMessage: "Incorrect username or password"
      });
    }
    if (error.name === "UserNotFoundException") {
      throw createError({
        statusCode: 401,
        statusMessage: "Incorrect username or password"
      });
    }
    throw createError({
      statusCode: 500,
      statusMessage: error.message
    });
  }
};
const handleConfirmSignUp = async (event) => {
  const body = await readBody(event);
  const { username, confirmationCode } = body;
  const config = useRuntimeConfig();
  if (!config.cognitoUserPoolId || !config.cognitoClientId || !config.cognitoClientSecret) {
    throw createError({
      statusCode: 400,
      message: "Auth UserPool not configured. Missing Cognito configuration."
    });
  }
  if (username.includes("@")) {
    throw createError({
      statusCode: 400,
      message: "Please use the generated username returned from signup, not the email address."
    });
  }
  const secretHash = calculateSecretHash(
    username,
    config.cognitoClientId,
    config.cognitoClientSecret
  );
  try {
    const client = new CognitoIdentityProviderClient({
      region: config.cognitoRegion
    });
    const confirmCommand = new ConfirmSignUpCommand({
      ClientId: config.cognitoClientId,
      Username: username,
      ConfirmationCode: confirmationCode,
      SecretHash: secretHash
    });
    await client.send(confirmCommand);
    try {
    } catch (syncError) {
      console.error("Failed to sync with Medusa after confirmation:", syncError);
    }
    return {
      isSignUpComplete: true,
      nextStep: {
        signUpStep: "DONE"
      }
    };
  } catch (error) {
    console.error("Confirm signup error:", error);
    throw createError({
      statusCode: 400,
      message: error.message
    });
  }
};
const handleConfirmResetPassword = async (event) => {
  const body = await readBody(event);
  const { username, code, newPassword, name } = body;
  generateSecretHash(username);
  try {
    const cognitoResult = await confirmResetPasswordCognito(username, code, newPassword);
    const medusaClient = getMedusaClient();
    try {
      await medusaClient.auth.authenticate({
        email: username,
        password: newPassword
      });
    } catch (authError) {
      let firstName = "";
      let lastName = "";
      if (name) {
        const nameParts = name.split(" ");
        firstName = nameParts[0] || "";
        lastName = nameParts.slice(1).join(" ") || "";
      }
      await medusaClient.customers.create({
        email: username,
        password: newPassword,
        first_name: firstName,
        last_name: lastName
      });
    }
    return cognitoResult;
  } catch (error) {
    throw createError({
      statusCode: 400,
      message: error.message
    });
  }
};
const handleResetPassword = async (event) => {
  const body = await readBody(event);
  const { username } = body;
  generateSecretHash(username);
  try {
    const result = await resetPasswordCognito(username);
    return result;
  } catch (error) {
    throw createError({
      statusCode: 400,
      message: error.message
    });
  }
};
const COOKIE_SETTINGS = {
  maxAge: COOKIE_OPTIONS.maxAge,
  path: COOKIE_OPTIONS.path,
  secure: COOKIE_OPTIONS.secure,
  httpOnly: false,
  // Must be false to be accessible by JavaScript
  sameSite: COOKIE_OPTIONS.sameSite
  // Don't use the expires option - it can cause issues with cookie expiration
  // Don't use domain - let the browser handle it
};
const logCookieOperation = (operation, name, value) => {
  console.log(`Cookie ${operation}: ${name}, size: ${typeof value === "string" ? value.length : "N/A"} bytes, options: ${JSON.stringify(COOKIE_SETTINGS)}`);
};
const setAuthCookie = (event, name, value) => {
  logCookieOperation("set", name, value);
  setCookie(event, name, value, COOKIE_SETTINGS);
};
const handleGetCurrentUser = async (event) => {
  var _a, _b, _c, _d, _e, _f;
  try {
    const accessToken = await getAccessToken(event);
    if (!accessToken) {
      console.log("No access token found in handleGetCurrentUser");
      return {
        isAuthenticated: false,
        message: "No access token found"
      };
    }
    console.log("Access token found in handleGetCurrentUser, length:", accessToken.length);
    const cognitoUser = await getCurrentUserFromCognito(accessToken);
    let medusaCustomer = null;
    let medusaAuthenticated = false;
    try {
      const medusa = getMedusaClient();
      const medusaResponse = await medusa.customers.retrieve();
      if (medusaResponse.customer) {
        medusaCustomer = medusaResponse.customer;
        medusaAuthenticated = true;
      }
    } catch (medusaError) {
      console.log("Medusa customer retrieval error:", medusaError);
    }
    const userData = {
      isAuthenticated: true,
      userId: cognitoUser.userId,
      username: cognitoUser.username,
      email: ((_a = cognitoUser.signInDetails) == null ? void 0 : _a.loginId) || ((_b = cognitoUser.attributes) == null ? void 0 : _b.email),
      name: ((_c = cognitoUser.attributes) == null ? void 0 : _c.name) || cognitoUser.username,
      attributes: cognitoUser.attributes || {},
      medusa: {
        customer: medusaCustomer,
        authenticated: medusaAuthenticated
      }
    };
    const cookieData = {
      isAuthenticated: true,
      userId: cognitoUser.userId,
      username: cognitoUser.username,
      email: ((_d = cognitoUser.signInDetails) == null ? void 0 : _d.loginId) || ((_e = cognitoUser.attributes) == null ? void 0 : _e.email),
      name: ((_f = cognitoUser.attributes) == null ? void 0 : _f.name) || cognitoUser.username,
      accessToken
    };
    setAuthCookie(event, AUTH_COOKIE_NAME, JSON.stringify(cookieData));
    console.log("Auth cookie set in handleGetCurrentUser");
    return userData;
  } catch (error) {
    console.error("Get current user error:", error);
    return {
      isAuthenticated: false,
      error: error.message
    };
  }
};
const clearAuthCookie = (event, name) => {
  logCookieOperation("clear", name, null);
  setCookie(event, name, "", {
    maxAge: -1,
    path: "/",
    httpOnly: false,
    secure: COOKIE_OPTIONS.secure,
    sameSite: COOKIE_OPTIONS.sameSite
  });
  appendHeader(event, "Set-Cookie", `${name}=; Max-Age=0; Path=/; SameSite=Lax; ${""}`);
  console.log("Auth cookie cleared");
};
const handleSignOut = async (event) => {
  try {
    const accessToken = await getAccessToken(event);
    if (accessToken) {
      try {
        const signOutCommand = new GlobalSignOutCommand({
          AccessToken: accessToken
        });
        await cognitoClient.send(signOutCommand);
        console.log("Global sign-out command sent to Cognito");
      } catch (signOutError) {
        console.error("Error during Cognito sign out:", signOutError);
      }
    } else {
      console.log("No access token found during sign out");
    }
    clearAuthCookie(event, AUTH_COOKIE_NAME);
    try {
      const medusa = getMedusaClient();
      await medusa.auth.deleteSession();
      console.log("Medusa session deleted");
    } catch (medusaError) {
      console.error("Error clearing Medusa session:", medusaError);
    }
    return {
      success: true,
      message: "Signed out successfully"
    };
  } catch (error) {
    console.error("Error during sign out:", error);
    clearAuthCookie(event, AUTH_COOKIE_NAME);
    return {
      success: false,
      message: "An error occurred during sign out, but cookies were cleared"
    };
  }
};
const handleResendCode = async (event) => {
  const body = await readBody(event);
  let { username } = body;
  const config = useRuntimeConfig();
  if (!config.cognitoUserPoolId || !config.cognitoClientId || !config.cognitoClientSecret) {
    throw createError({
      statusCode: 400,
      message: "Auth UserPool not configured. Missing Cognito configuration."
    });
  }
  if (username.includes("@")) {
    const actualUsername = await findUserByEmail(username);
    if (!actualUsername) {
      throw createError({
        statusCode: 400,
        message: "User not found with this email address."
      });
    }
    username = actualUsername;
  }
  const secretHash = calculateSecretHash(
    username,
    config.cognitoClientId,
    config.cognitoClientSecret
  );
  try {
    const client = new CognitoIdentityProviderClient({
      region: config.cognitoRegion
    });
    const resendCommand = new ResendConfirmationCodeCommand({
      ClientId: config.cognitoClientId,
      Username: username,
      SecretHash: secretHash
    });
    await client.send(resendCommand);
    return {
      success: true,
      message: "Confirmation code resent successfully."
    };
  } catch (error) {
    console.error("Resend code error:", error);
    throw createError({
      statusCode: 400,
      message: error.message
    });
  }
};
const handleUpdateUser = async (event) => {
  try {
    const { name, phoneNumber } = await readBody(event);
    if (!name && !phoneNumber) {
      throw createError({
        statusCode: 400,
        message: "At least one attribute to update is required"
      });
    }
    const accessToken = await getAccessToken(event);
    if (!accessToken) {
      throw createError({
        statusCode: 401,
        message: "No access token available, please sign in again"
      });
    }
    const attributes = {};
    if (name) attributes.name = name;
    if (phoneNumber) attributes.phone_number = phoneNumber;
    await updateUserAttributesCognito(accessToken, attributes);
    const updatedUser = await getCurrentUserFromCognito(accessToken);
    return {
      success: true,
      user: updatedUser
    };
  } catch (error) {
    console.error("Error updating user:", error);
    throw createError({
      statusCode: 500,
      message: error.message || "An error occurred while updating user attributes"
    });
  }
};
const handleChangePassword = async (event) => {
  var _a;
  const body = await readBody(event);
  const { oldPassword, newPassword } = body;
  try {
    const cognitoUser = await getCurrentUserFromCognito();
    console.warn("Cognito direct password change is not supported in this version of aws-amplify/auth");
    const cognitoUpdated = false;
    try {
      const medusa = getMedusaClient();
      const email = (_a = cognitoUser.signInDetails) == null ? void 0 : _a.loginId;
      if (email) {
        await medusa.auth.authenticate({
          email,
          password: oldPassword
        });
        const medusaResult = await medusa.customers.update({
          password: newPassword
        });
        return {
          success: true,
          cognitoUpdated,
          cognitoMessage: "Cognito password change is not supported directly in this version",
          medusa: {
            updated: true
          }
        };
      } else {
        return {
          success: false,
          cognitoUpdated,
          cognitoMessage: "Cognito password change is not supported directly in this version",
          medusa: {
            updated: false,
            error: "Email not found in Cognito user"
          }
        };
      }
    } catch (medusaError) {
      const typedError = medusaError;
      return {
        success: false,
        cognitoUpdated,
        cognitoMessage: "Cognito password change is not supported directly in this version",
        medusa: {
          updated: false,
          error: typedError.message
        }
      };
    }
  } catch (error) {
    throw createError({
      statusCode: 400,
      message: error.message
    });
  }
};
const getCurrentUserFromCognito = async (accessToken) => {
  try {
    if (!accessToken) {
      accessToken = await getAccessToken();
    }
    if (!accessToken) {
      throw new Error("No access token available");
    }
    const getUserCommand = new GetUserCommand({
      AccessToken: accessToken
    });
    const userResult = await cognitoClient.send(getUserCommand);
    if (userResult && userResult.Username) {
      const attributes = {};
      if (userResult.UserAttributes) {
        userResult.UserAttributes.forEach((attr) => {
          if (attr.Name && attr.Value) {
            attributes[attr.Name] = attr.Value;
          }
        });
      }
      return {
        userId: attributes["sub"] || "",
        username: userResult.Username,
        signInDetails: {
          loginId: attributes["email"] || userResult.Username
        },
        attributes
      };
    }
    throw new Error("User not found");
  } catch (error) {
    console.error("Error getting current user:", error);
    throw error;
  }
};
const updateUserAttributesCognito = async (accessToken, attributes) => {
  try {
    if (!accessToken) {
      throw new Error("No access token available");
    }
    const attributesList = Object.entries(attributes).map(([Name, Value]) => ({
      Name,
      Value
    }));
    const updateCommand = {
      AccessToken: accessToken,
      UserAttributes: attributesList
    };
    const { UpdateUserAttributesCommand: UpdateUserAttributesCommand2 } = await import('file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/@aws-sdk/client-cognito-identity-provider/dist-cjs/index.js');
    const command = new UpdateUserAttributesCommand2(updateCommand);
    const result = await cognitoClient.send(command);
    return result;
  } catch (error) {
    console.error("Error updating user attributes:", error);
    throw error;
  }
};
const resetPasswordCognito = async (username) => {
  try {
    const config = useRuntimeConfig();
    const clientId = config.public.cognitoClientId || process.env.COGNITO_CLIENT_ID;
    if (!clientId) {
      throw new Error("Missing Cognito client ID");
    }
    const secretHash = generateSecretHash(username);
    const forgotPasswordCommand = new ForgotPasswordCommand({
      ClientId: clientId,
      Username: username,
      SecretHash: secretHash
    });
    const result = await cognitoClient.send(forgotPasswordCommand);
    return result;
  } catch (error) {
    console.error("Error initiating password reset:", error);
    throw error;
  }
};
const confirmResetPasswordCognito = async (username, code, newPassword) => {
  try {
    const config = useRuntimeConfig();
    const clientId = config.public.cognitoClientId || process.env.COGNITO_CLIENT_ID;
    if (!clientId) {
      throw new Error("Missing Cognito client ID");
    }
    const secretHash = generateSecretHash(username);
    const confirmForgotPasswordCommand = new ConfirmForgotPasswordCommand({
      ClientId: clientId,
      Username: username,
      ConfirmationCode: code,
      Password: newPassword,
      SecretHash: secretHash
    });
    const result = await cognitoClient.send(confirmForgotPasswordCommand);
    return result;
  } catch (error) {
    console.error("Error confirming password reset:", error);
    throw error;
  }
};
const _____ = defineEventHandler(async (event) => {
  var _a;
  const action = getRouterParam(event, "_");
  switch (action) {
    case "signup": {
      const result = await handleSignUp(event);
      event.context.session = event.context.session || {};
      event.context.session.generatedUsername = result.username;
      event.context.session.userEmail = result.email;
      return result;
    }
    case "signin":
      return handleSignIn(event);
    case "confirm-signup": {
      const body = await readBody(event);
      if (((_a = event.context.session) == null ? void 0 : _a.generatedUsername) && !body.username) {
        body.username = event.context.session.generatedUsername;
      }
      return handleConfirmSignUp(event);
    }
    case "reset":
      return handleResetPassword(event);
    case "reset-password":
      return handleResetPassword(event);
    case "confirm-reset-password":
      return handleConfirmResetPassword(event);
    case "current-user":
    case "me":
      return handleGetCurrentUser(event);
    case "signout":
      return handleSignOut(event);
    case "resend-code":
      return handleResendCode(event);
    case "confirm":
      return handleConfirmSignUp(event);
    // Reuse confirm-signup handler
    case "update-user":
      return handleUpdateUser(event);
    case "change-password":
      return handleChangePassword(event);
    default:
      throw createError({
        statusCode: 404,
        message: "Not found"
      });
  }
});

const _____$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _____
});

const cookieFix = defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const headers = getRequestHeaders(event);
    const origin = headers.origin || headers.referer || "";
    if (body.clear === true) {
      console.log("AUTH DEBUG: Clearing auth cookie via API endpoint");
      setCookie(event, AUTH_COOKIE_NAME, "", {
        httpOnly: false,
        path: "/",
        maxAge: 0,
        sameSite: "lax"
      });
      return {
        success: true,
        message: "Auth cookie cleared successfully"
      };
    }
    console.log("AUTH DEBUG: Attempting to fix cookie via API endpoint");
    if (!body.userData) {
      console.error("AUTH DEBUG: No userData provided to cookie-fix endpoint");
      return { success: false, message: "No auth data provided" };
    }
    setCookie(event, AUTH_COOKIE_NAME, body.userData, {
      httpOnly: false,
      // Allow JavaScript access
      path: "/",
      sameSite: "lax",
      secure: false,
      maxAge: 60 * 60 * 24 * 30
      // 30 days
    });
    setCookie(event, "cookie_test", "works", {
      httpOnly: false,
      path: "/",
      sameSite: "lax",
      maxAge: 60 * 60 * 24
      // 1 day
    });
    console.log("AUTH DEBUG: Server set auth cookie via API endpoint");
    return {
      success: true,
      message: "Auth cookie set successfully"
    };
  } catch (error) {
    console.error("AUTH DEBUG: Error setting cookie via API:", error);
    return {
      success: false,
      message: "Failed to set auth cookie",
      error: error.message
    };
  }
});

const cookieFix$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: cookieFix
});

const currentUser = defineEventHandler(async (event) => {
  var _a;
  console.log("AUTH DEBUG: /api/auth/current-user endpoint called");
  try {
    const authCookie = getCookie(event, "handmadein_auth");
    console.log("AUTH DEBUG: handmadein_auth cookie exists:", !!authCookie);
    if (!authCookie) {
      console.log("AUTH DEBUG: No auth cookie found, returning unauthenticated");
      return {
        isAuthenticated: false,
        userId: null,
        username: null,
        email: null
      };
    }
    try {
      const cookieData = JSON.parse(authCookie);
      console.log("AUTH DEBUG: Cookie parsed successfully, cookie data:", cookieData);
      if (cookieData && cookieData.isAuthenticated) {
        console.log("AUTH DEBUG: Cookie indicates user is authenticated");
        return {
          isAuthenticated: true,
          userId: cookieData.userId,
          username: cookieData.username,
          email: cookieData.email,
          name: cookieData.name
        };
      }
    } catch (e) {
      console.error("AUTH DEBUG: Error parsing auth cookie:", e);
    }
    console.log("AUTH DEBUG: Attempting to get current user via Amplify");
    const user = await getCurrentUser();
    console.log("AUTH DEBUG: Amplify getCurrentUser succeeded:", !!user);
    return {
      isAuthenticated: true,
      userId: user.userId,
      username: user.username,
      email: (_a = user.signInDetails) == null ? void 0 : _a.loginId
    };
  } catch (error) {
    console.error("AUTH DEBUG: Error in current-user endpoint:", error);
    return {
      isAuthenticated: false,
      userId: null,
      username: null,
      email: null
    };
  }
});

const currentUser$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: currentUser
});

const debug = defineEventHandler((event) => {
  try {
    const config = useRuntimeConfig();
    return {
      cognitoRegion: config.cognitoRegion ? "configured" : "missing",
      cognitoUserPoolId: config.cognitoUserPoolId ? "configured" : "missing",
      cognitoClientId: config.cognitoClientId ? "configured" : "missing",
      cognitoClientSecret: config.cognitoClientSecret ? "configured" : "missing",
      medusaApiUrl: config.public.medusaApiUrl ? "configured" : "missing",
      apiUrl: config.public.apiUrl ? "configured" : "missing",
      // Add session information, but redact sensitive data
      session: event.context.session ? {
        hasSession: true,
        keys: Object.keys(event.context.session),
        generatedUsername: event.context.session.generatedUsername ? "present" : "missing",
        userEmail: event.context.session.userEmail ? "present" : "missing"
      } : { hasSession: false },
      // Add any other relevant configuration fields
      allConfigKeys: Object.keys(config),
      publicConfigKeys: Object.keys(config.public || {})
    };
  } catch (error) {
    console.error("Error in debug endpoint:", error);
    throw createError({
      statusCode: 500,
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

const debug$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: debug
});

const login = defineEventHandler(async (event) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
  try {
    const body = await readBody(event);
    const { email, password } = body;
    if (!email || !password) {
      throw createError({
        statusCode: 400,
        message: "Email and password are required"
      });
    }
    const config = useRuntimeConfig();
    const serverUrl = config.public.serverUrl || process.env.API_URL || "http://localhost:3001";
    const signinUrl = "https://handmadein.ro/api/auth/signin";
    console.log(`Using signin URL: ${signinUrl}`);
    const response = await fetch(signinUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        username: email,
        password
      })
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw createError({
        statusCode: response.status,
        message: errorData.message || "Authentication failed"
      });
    }
    const responseData = await response.json();
    return {
      authentication: {
        isAuthenticated: true,
        user: {
          userId: (_a = responseData.user) == null ? void 0 : _a.userId,
          username: (_b = responseData.user) == null ? void 0 : _b.username,
          email: ((_c = responseData.user) == null ? void 0 : _c.email) || email,
          given_name: (_d = responseData.user) == null ? void 0 : _d.given_name,
          family_name: (_e = responseData.user) == null ? void 0 : _e.family_name,
          phone_number: (_f = responseData.user) == null ? void 0 : _f.phone_number,
          name: ((_g = responseData.user) == null ? void 0 : _g.name) || `${((_h = responseData.user) == null ? void 0 : _h.given_name) || ""} ${((_i = responseData.user) == null ? void 0 : _i.family_name) || ""}`.trim(),
          medusa: (_j = responseData.user) == null ? void 0 : _j.medusa
        },
        token: responseData.accessToken
      }
    };
  } catch (error) {
    console.error("Login error:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "Authentication failed"
    });
  }
});

const login$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: login
});

const signin = defineEventHandler(async (event) => {
  try {
    const { default: authHandler } = await Promise.resolve().then(function () { return _____$1; });
    const body = await readBody(event);
    event.context.body = body;
    event.context.params = { "_": "signin" };
    const result = await authHandler(event);
    return result;
  } catch (error) {
    console.error("Signin error:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "Authentication failed"
    });
  }
});

const signin$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: signin
});

const signout = defineEventHandler(async () => {
  try {
    await signOut();
    return { success: true };
  } catch (error) {
    return { success: false };
  }
});

const signout$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: signout
});

const syncMedusa = defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { email, name } = body;
    if (!email) {
      throw createError({
        statusCode: 400,
        message: "Email is required"
      });
    }
    const config = useRuntimeConfig();
    const medusaUrl = config.medusaUrl || "https://api.handmadein.ro";
    const token = await syncCognitoToMedusaDirectly(email, medusaUrl, name);
    if (token) {
      return {
        success: true,
        token
      };
    } else {
      return {
        success: false,
        message: "Failed to sync with Medusa"
      };
    }
  } catch (error) {
    console.error("Error syncing with Medusa:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "An error occurred while syncing with Medusa"
    });
  }
});

const syncMedusa$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: syncMedusa
});

const contact = defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const response = await $fetch(process.env.CONTACT_FORM_LAMBDA_URL || "", {
      method: "POST",
      body: JSON.stringify(body),
      headers: {
        "Content-Type": "application/json",
        "x-api-key": process.env.CONTACT_FORM_LAMBDA_KEY || ""
      }
    });
    return response;
  } catch (error) {
    console.error("Error submitting contact form:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Failed to submit contact form"
    });
  }
});

const contact$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: contact
});

const addresses = defineEventHandler(async (event) => {
  try {
    const cookies = parseCookies(event);
    const authCookie = cookies[AUTH_COOKIE_NAME] || cookies["auth"];
    if (!authCookie) {
      throw createError({
        statusCode: 401,
        message: "Authentication required: No auth cookie found"
      });
    }
    let authData;
    let accessToken;
    try {
      authData = JSON.parse(authCookie);
      accessToken = authData.accessToken;
      if (!authData.isAuthenticated || !accessToken) {
        throw new Error("No valid access token");
      }
    } catch (parseError) {
      console.error("Error parsing auth cookie:", parseError);
      throw createError({
        statusCode: 401,
        message: "Authentication required: Invalid auth cookie"
      });
    }
    const config = useRuntimeConfig();
    const apiUrl = false ? config.apiUrl || "https://api.fromte.com" : "http://localhost:8787";
    console.log("Using Cloudflare API URL:", apiUrl);
    if (event.method === "GET") {
      console.log("Handling GET request for addresses");
      const response = await fetch(`${apiUrl}/store/customers/me/addresses`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json"
        }
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching addresses:", errorText);
        throw createError({
          statusCode: response.status,
          message: `Failed to fetch addresses: ${errorText}`
        });
      }
      const data = await response.json();
      console.log("Addresses fetched successfully");
      return {
        shipping_addresses: data.data || []
      };
    } else if (event.method === "POST") {
      console.log("Handling POST request for address creation");
      const addressData = await readBody(event);
      if (!addressData) {
        throw createError({
          statusCode: 400,
          message: "Address data is required"
        });
      }
      console.log("Creating new address with data:", addressData);
      const response = await fetch(`${apiUrl}/store/customers/me/addresses`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(addressData)
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error creating address:", errorText);
        throw createError({
          statusCode: response.status,
          message: `Failed to create address: ${errorText}`
        });
      }
      const result = await response.json();
      console.log("Address created successfully");
      return result;
    }
    throw createError({
      statusCode: 405,
      message: "Method not allowed"
    });
  } catch (error) {
    console.error("Error in addresses endpoint:", error.message);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "An unknown error occurred"
    });
  }
});

const addresses$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: addresses
});

const _id_$4 = defineEventHandler(async (event) => {
  try {
    const addressId = getRouterParam(event, "id");
    console.log("Address ID from URL:", addressId);
    if (!addressId) {
      throw createError({
        statusCode: 400,
        message: "Address ID is required"
      });
    }
    const cookies = parseCookies(event);
    const authCookie = cookies[AUTH_COOKIE_NAME] || cookies["auth"];
    if (!authCookie) {
      throw createError({
        statusCode: 401,
        message: "Authentication required: No auth cookie found"
      });
    }
    let authData;
    let accessToken;
    try {
      authData = JSON.parse(authCookie);
      accessToken = authData.accessToken;
      if (!authData.isAuthenticated || !accessToken) {
        throw new Error("No valid access token");
      }
    } catch (parseError) {
      console.error("Error parsing auth cookie:", parseError);
      throw createError({
        statusCode: 401,
        message: "Authentication required: Invalid auth cookie"
      });
    }
    const config = useRuntimeConfig();
    const apiUrl = false ? config.apiUrl || "https://api.fromte.com" : "http://localhost:8787";
    console.log("Using Cloudflare API URL:", apiUrl);
    if (event.method === "PUT" || event.method === "POST") {
      console.log("Handling PUT/POST request for address update");
      const addressData = await readBody(event);
      if (!addressData) {
        throw createError({
          statusCode: 400,
          message: "Address data is required"
        });
      }
      console.log(`Updating address ${addressId} with data:`, addressData);
      const response = await fetch(`${apiUrl}/store/customers/me/addresses/${addressId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${accessToken}`
        },
        body: JSON.stringify(addressData)
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error updating address:", errorText);
        throw createError({
          statusCode: response.status,
          message: `Failed to update address: ${errorText}`
        });
      }
      const result = await response.json();
      console.log("Address updated successfully");
      return result;
    } else if (event.method === "DELETE") {
      console.log(`Deleting address ${addressId}`);
      const response = await fetch(`${apiUrl}/store/customers/me/addresses/${addressId}`, {
        method: "DELETE",
        headers: {
          "Authorization": `Bearer ${accessToken}`
        }
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error deleting address:", errorText);
        throw createError({
          statusCode: response.status,
          message: `Failed to delete address: ${errorText}`
        });
      }
      console.log("Address deleted successfully");
      return { success: true };
    }
    throw createError({
      statusCode: 405,
      message: "Method not allowed"
    });
  } catch (error) {
    console.error("Error in address ID endpoint:", error.message);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "An unknown error occurred"
    });
  }
});

const _id_$5 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id_$4
});

const me = defineEventHandler(async (event) => {
  try {
    const cookies = parseCookies(event);
    const authCookie = cookies[AUTH_COOKIE_NAME] || cookies["auth"];
    if (!authCookie) {
      throw createError({
        statusCode: 401,
        message: "Authentication required: No auth cookie found"
      });
    }
    let authData;
    let accessToken;
    try {
      authData = JSON.parse(authCookie);
      accessToken = authData.accessToken;
      if (!authData.isAuthenticated || !accessToken) {
        throw new Error("No valid access token");
      }
    } catch (parseError) {
      console.error("Error parsing auth cookie:", parseError);
      throw createError({
        statusCode: 401,
        message: "Authentication required: Invalid auth cookie"
      });
    }
    const config = useRuntimeConfig();
    const apiUrl = false ? config.apiUrl || "https://api.fromte.com" : "http://localhost:8787";
    console.log("Using Cloudflare API URL:", apiUrl);
    if (event.method === "GET") {
      console.log("Handling GET request for customer details");
      const response = await fetch(`${apiUrl}/store/customers/me`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json"
        }
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching customer details:", errorText);
        throw createError({
          statusCode: response.status,
          message: `Failed to fetch customer details: ${errorText}`
        });
      }
      const data = await response.json();
      console.log("Customer details fetched successfully");
      return data;
    } else if (event.method === "POST") {
      console.log("Handling POST request for customer update");
      const customerData = await readBody(event);
      if (!customerData) {
        throw createError({
          statusCode: 400,
          message: "Customer data is required"
        });
      }
      console.log("Updating customer with data:", customerData);
      const response = await fetch(`${apiUrl}/store/customers/me`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${accessToken}`
        },
        body: JSON.stringify(customerData)
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error updating customer:", errorText);
        throw createError({
          statusCode: response.status,
          message: `Failed to update customer: ${errorText}`
        });
      }
      const data = await response.json();
      console.log("Customer updated successfully");
      return data;
    }
    throw createError({
      statusCode: 405,
      message: "Method not allowed"
    });
  } catch (error) {
    console.error("Error in customer/me endpoint:", error.message);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "An unknown error occurred"
    });
  }
});

const me$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: me
});

const password = defineEventHandler(async (event) => {
  const session = await getSession(event);
  if (!session) {
    throw createError({
      statusCode: 401,
      message: "Unauthorized"
    });
  }
  const medusa = getMedusaClient$1();
  const method = event.node.req.method;
  if (method !== "POST") {
    throw createError({
      statusCode: 405,
      message: "Method not allowed"
    });
  }
  try {
    const { current_password, new_password } = await readBody(event);
    if (!current_password || !new_password) {
      throw createError({
        statusCode: 400,
        message: "Both current and new password are required"
      });
    }
    await medusa.customers.update({
      password: new_password,
      old_password: current_password
    });
    return {
      success: true,
      message: "Password updated successfully"
    };
  } catch (error) {
    throw createError({
      statusCode: error.status || 500,
      message: error.message || "Failed to update password"
    });
  }
});

const password$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: password
});

const MEDUSA_JWT_COOKIE = "jwt";
async function authenticateCustomer(email, medusaUrl, publishableKey, firstName, lastName) {
  try {
    console.log(`Authenticating customer with email: ${email}`);
    const securePassword = getOrCreateSecurePassword(email);
    console.log("Attempting customer login with secure password using direct API call to /auth/customer/emailpass");
    try {
      const loginResponse = await fetch(`${medusaUrl}/auth/customer/emailpass`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-publishable-api-key": publishableKey
        },
        body: JSON.stringify({
          email,
          password: securePassword
        })
      });
      const status = loginResponse.status;
      let responseData;
      let responseText;
      try {
        responseData = await loginResponse.json();
      } catch (jsonError) {
        console.log("Failed to parse /auth/customer/emailpass response as JSON, reading as text...");
      }
      const receivedToken = (responseData == null ? void 0 : responseData.access_token) || (responseData == null ? void 0 : responseData.token);
      if (status === 200 && receivedToken) {
        console.log("Customer login successful via /auth/customer/emailpass, received JWT token");
        return receivedToken;
      }
      console.log("Login via /auth/customer/emailpass failed with status:", status);
      if (responseData) {
        console.log("Login response data (JSON):", JSON.stringify(responseData));
      } else {
        console.log("Login response could not be parsed as JSON.");
      }
      console.log("Customer login failed via /auth/customer/emailpass, trying register flow");
      const extractedFirstName = firstName || email.split("@")[0];
      const extractedLastName = lastName || "Customer";
      const registerResponse = await fetch(`${medusaUrl}/store/customers`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-publishable-api-key": publishableKey
        },
        body: JSON.stringify({
          email,
          password: securePassword,
          first_name: extractedFirstName,
          last_name: extractedLastName
        })
      });
      if (registerResponse.ok) {
        console.log("Customer registered successfully, now trying to login via /store/auth/token");
        const secondLoginResponse = await fetch(`${medusaUrl}/store/auth/token`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-publishable-api-key": publishableKey
          },
          body: JSON.stringify({
            email,
            password: securePassword
          })
        });
        if (secondLoginResponse.ok) {
          const data = await secondLoginResponse.json();
          if (data.access_token) {
            console.log("Login after registration successful via /store/auth/token");
            return data.access_token;
          }
        } else {
          const secondLoginStatus = secondLoginResponse.status;
          const secondLoginText = await secondLoginResponse.text();
          console.log("Login after registration failed with status:", secondLoginStatus, "Response:", secondLoginText);
        }
      } else {
        const registerStatus = registerResponse.status;
        const registerText = await registerResponse.text();
        console.log("Register failed with status:", registerStatus, "Response:", registerText);
        if (registerText.includes("already exists")) {
          console.log("Customer already exists but login failed - credentials may be incorrect or issue with /auth/customer/emailpass");
          console.log("Trying standard /store/auth/token endpoint as fallback");
          const fallbackLoginResponse = await fetch(`${medusaUrl}/store/auth/token`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "x-publishable-api-key": publishableKey
            },
            body: JSON.stringify({
              email,
              password: securePassword
            })
          });
          if (fallbackLoginResponse.ok) {
            try {
              const fallbackData = await fallbackLoginResponse.json();
              if (fallbackData.access_token) {
                console.log("Fallback login via /store/auth/token successful");
                return fallbackData.access_token;
              }
            } catch (e) {
              console.error("Error parsing fallback login response:", e);
            }
          } else {
            console.log("Fallback login failed:", await fallbackLoginResponse.text());
          }
        }
      }
      return null;
    } catch (error) {
      console.error("Error in customer authentication process:", error);
      return null;
    }
  } catch (error) {
    console.error("Error authenticating customer (outer catch):", error);
    return null;
  }
}
async function ensureCustomerIsRegistered(email, token, medusaUrl, publishableKey, event) {
  console.log(`Ensuring customer with email ${email} is registered`);
  try {
    const checkResponse = await fetch(`${medusaUrl}/store/customers/me`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
        "x-publishable-api-key": publishableKey
      }
    });
    if (checkResponse.ok) {
      const customerData = await checkResponse.json();
      console.log("Retrieved customer profile:", customerData.customer);
      if (customerData.customer.has_account === true) {
        console.log("Customer already has a registered account");
        return {
          isRegistered: true,
          customer: customerData.customer,
          token
        };
      }
      console.log("Customer exists as a guest user (has_account: false), converting to registered user");
      const { first_name, last_name, email: email2 } = customerData.customer;
      try {
        console.log("Getting token from /auth/customer/emailpass");
        const securePassword2 = getOrCreateSecurePassword(email2);
        const authResponse = await fetch(`${medusaUrl}/auth/customer/emailpass`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-publishable-api-key": publishableKey
          },
          body: JSON.stringify({
            email: email2,
            password: securePassword2
          })
        });
        if (!authResponse.ok) {
          console.error("Failed to get token from auth endpoint:", await authResponse.text());
          return {
            isRegistered: false,
            customer: customerData.customer,
            token
          };
        }
        const authResponseData = await authResponse.json();
        const registrationToken2 = authResponseData.access_token || authResponseData.token;
        if (!registrationToken2) {
          console.error("Did not receive token from auth endpoint:", authResponseData);
          return {
            isRegistered: false,
            customer: customerData.customer,
            token
          };
        }
        console.log("Successfully got new token, now registering the guest user");
        const registerResponse2 = await fetch(`${medusaUrl}/store/customers`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${registrationToken2}`,
            "x-publishable-api-key": publishableKey
          },
          body: JSON.stringify({
            email: email2,
            first_name: first_name || email2.split("@")[0],
            last_name: last_name || "Customer"
          })
        });
        if (!registerResponse2.ok) {
          console.error("Failed to register guest user:", await registerResponse2.text());
          return {
            isRegistered: false,
            customer: customerData.customer,
            token: registrationToken2 || token
          };
        }
        console.log("Successfully registered guest user");
        const updatedProfileResponse = await fetch(`${medusaUrl}/store/customers/me`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${registrationToken2}`,
            "x-publishable-api-key": publishableKey
          }
        });
        if (updatedProfileResponse.ok) {
          const updatedCustomerData = await updatedProfileResponse.json();
          console.log(
            "Updated customer profile after registration:",
            updatedCustomerData.customer.has_account === true ? "Successfully converted to registered user" : "Still showing as guest user"
          );
          return {
            isRegistered: true,
            customer: updatedCustomerData.customer,
            token: registrationToken2
          };
        }
        return {
          isRegistered: true,
          customer: customerData.customer,
          token: registrationToken2
        };
      } catch (error) {
        console.error("Error converting guest user to registered user:", error);
        return {
          isRegistered: false,
          customer: customerData.customer,
          token
        };
      }
    }
    console.log("Customer not found or not registered, status:", checkResponse.status);
    console.log("Registering customer with Medusa v2 flow");
    const securePassword = getOrCreateSecurePassword(email);
    let firstName = "User";
    let lastName = "Account";
    try {
      if (event) {
        if (event.context && event.context.requestBody) {
          if (event.context.requestBody.first_name) {
            firstName = event.context.requestBody.first_name;
          }
          if (event.context.requestBody.last_name) {
            lastName = event.context.requestBody.last_name;
          }
        } else {
          const body = await readBody(event);
          if (body && body.first_name) {
            firstName = body.first_name;
          }
          if (body && body.last_name) {
            lastName = body.last_name;
          }
        }
      }
    } catch (e) {
      console.warn("Could not read request body:", e);
    }
    if (firstName === "User" || lastName === "Account") {
      const emailParts = email.split("@")[0].split(".");
      firstName = emailParts[0] ? emailParts[0].charAt(0).toUpperCase() + emailParts[0].slice(1) : "User";
      lastName = emailParts[1] ? emailParts[1].charAt(0).toUpperCase() + emailParts[1].slice(1) : "Account";
    }
    console.log("Getting registration token from auth endpoint");
    const authRegisterResponse = await fetch(`${medusaUrl}/auth/customer/emailpass/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-publishable-api-key": publishableKey
      },
      body: JSON.stringify({
        email,
        password: securePassword,
        first_name: firstName,
        last_name: lastName
      })
    });
    if (!authRegisterResponse.ok) {
      const errorText = await authRegisterResponse.text();
      console.error("Failed to get registration token:", errorText);
      console.log("Registration failed, trying alternate flow for guest users");
      try {
        console.log("Getting token from /auth/customer/emailpass");
        const authResponse = await fetch(`${medusaUrl}/auth/customer/emailpass`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-publishable-api-key": publishableKey
          },
          body: JSON.stringify({
            email,
            password: securePassword
          })
        });
        if (!authResponse.ok) {
          console.error("Failed to get token from auth endpoint:", await authResponse.text());
          if (errorText.includes("already exists")) {
            console.log("Falling back to standard authentication");
            return await authenticateExistingCustomer(email, medusaUrl, publishableKey);
          }
          return { isRegistered: false, token };
        }
        const authResponseData = await authResponse.json();
        const guestToken = authResponseData.access_token || authResponseData.token;
        if (!guestToken) {
          console.error("Did not receive token from auth endpoint:", authResponseData);
          return { isRegistered: false, token };
        }
        console.log("Successfully got token for guest user, now completing registration");
        const completeRegisterResponse = await fetch(`${medusaUrl}/store/customers`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${guestToken}`,
            "x-publishable-api-key": publishableKey
          },
          body: JSON.stringify({
            email,
            first_name: firstName,
            last_name: lastName
            // No password needed since we're using a token
          })
        });
        if (!completeRegisterResponse.ok) {
          console.error("Failed to complete guest registration:", await completeRegisterResponse.text());
          return { isRegistered: false, token: guestToken };
        }
        console.log("Successfully completed guest user registration");
        const customerResponse = await fetch(`${medusaUrl}/store/customers/me`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${guestToken}`,
            "x-publishable-api-key": publishableKey
          }
        });
        if (customerResponse.ok) {
          const customerData = await customerResponse.json();
          console.log("Retrieved customer details for converted guest user");
          return {
            isRegistered: true,
            customer: customerData.customer,
            token: guestToken
          };
        }
        return {
          isRegistered: true,
          token: guestToken
        };
      } catch (error) {
        console.error("Error in guest user conversion flow:", error);
        console.log("Error in guest flow, falling back to standard authentication");
        return await authenticateExistingCustomer(email, medusaUrl, publishableKey);
      }
    }
    const authData = await authRegisterResponse.json();
    const registrationToken = authData.access_token || authData.token;
    if (!registrationToken) {
      console.error("Did not receive token from auth register endpoint:", authData);
      return { isRegistered: false, token };
    }
    console.log("Received registration token, now creating customer");
    const registerResponse = await fetch(`${medusaUrl}/store/customers`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${registrationToken}`,
        "x-publishable-api-key": publishableKey
      },
      body: JSON.stringify({
        email,
        first_name: firstName,
        last_name: lastName
      })
    });
    if (registerResponse.ok) {
      console.log("Customer registered successfully");
      const loginResponse = await fetch(`${medusaUrl}/store/auth/token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-publishable-api-key": publishableKey
        },
        body: JSON.stringify({
          email,
          password: securePassword
        })
      });
      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        if (loginData.access_token) {
          console.log("Obtained new token for registered customer");
          const customerResponse = await fetch(`${medusaUrl}/store/customers/me`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${loginData.access_token}`,
              "x-publishable-api-key": publishableKey
            }
          });
          if (customerResponse.ok) {
            const customerData = await customerResponse.json();
            return {
              isRegistered: true,
              customer: customerData.customer,
              token: loginData.access_token
            };
          }
        }
      } else {
        console.error("Failed to login after registration:", await loginResponse.text());
      }
    } else {
      const errorText = await registerResponse.text();
      console.error("Failed to register customer:", errorText);
      if (errorText.includes("already exists")) {
        console.log("Customer already exists, trying to login");
        return await authenticateExistingCustomer(email, medusaUrl, publishableKey);
      }
    }
    return { isRegistered: false, token };
  } catch (error) {
    console.error("Error ensuring customer is registered:", error);
    return { isRegistered: false, token };
  }
}
async function authenticateExistingCustomer(email, medusaUrl, publishableKey) {
  try {
    const securePassword = getOrCreateSecurePassword(email);
    const loginResponse = await fetch(`${medusaUrl}/store/auth/token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-publishable-api-key": publishableKey
      },
      body: JSON.stringify({
        email,
        password: securePassword
      })
    });
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      if (loginData.access_token) {
        console.log("Authenticated existing customer successfully");
        const customerResponse = await fetch(`${medusaUrl}/store/customers/me`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${loginData.access_token}`,
            "x-publishable-api-key": publishableKey
          }
        });
        if (customerResponse.ok) {
          const customerData = await customerResponse.json();
          if (customerData.customer && customerData.customer.has_account === false) {
            console.log("Found guest account, converting to registered account");
            const firstName = customerData.customer.first_name || email.split("@")[0];
            const lastName = customerData.customer.last_name || "Customer";
            const updateResponse = await fetch(`${medusaUrl}/store/customers`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${loginData.access_token}`,
                "x-publishable-api-key": publishableKey
              },
              body: JSON.stringify({
                email,
                first_name: firstName,
                last_name: lastName,
                password: securePassword
              })
            });
            if (updateResponse.ok) {
              console.log("Successfully converted guest account to registered account");
              const updatedProfileResponse = await fetch(`${medusaUrl}/store/customers/me`, {
                method: "GET",
                headers: {
                  "Content-Type": "application/json",
                  "Authorization": `Bearer ${loginData.access_token}`,
                  "x-publishable-api-key": publishableKey
                }
              });
              if (updatedProfileResponse.ok) {
                const updatedCustomerData = await updatedProfileResponse.json();
                return {
                  isRegistered: true,
                  customer: updatedCustomerData.customer,
                  token: loginData.access_token
                };
              }
            } else {
              console.error("Failed to convert guest account to registered account:", await updateResponse.text());
            }
          }
          return {
            isRegistered: true,
            customer: customerData.customer,
            token: loginData.access_token
          };
        }
      }
    }
    return { isRegistered: false, token: "" };
  } catch (error) {
    console.error("Error authenticating existing customer:", error);
    return { isRegistered: false, token: "" };
  }
}
function setJwtCookie(event, token) {
  try {
    console.log("Setting Medusa JWT token as cookie");
    setCookie(event, MEDUSA_JWT_COOKIE, token, {
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24 * 30,
      // 30 days expiry
      sameSite: "lax",
      secure: false
      // Secure in production only
    });
  } catch (error) {
    console.error("Error setting JWT cookie:", error);
  }
}
const customer = defineEventHandler(async (event) => {
  var _a, _b, _c, _d, _e, _f, _g;
  try {
    const config = useRuntimeConfig();
    const medusaUrl = config.medusaUrl || "https://api.handmadein.ro";
    const publishableKey = config.public.medusaPublishableKey;
    const id = (_a = event.context.params) == null ? void 0 : _a.id;
    if (!id) {
      throw createError({
        statusCode: 400,
        message: "Cart ID is required"
      });
    }
    console.log(`Processing cart association for cart ID: ${id}`);
    const cookies = parseCookies(event);
    const authCookie = cookies[AUTH_COOKIE_NAME];
    let authData = {};
    if (authCookie) {
      try {
        authData = JSON.parse(authCookie);
      } catch (e) {
        console.error("Failed to parse auth cookie:", e);
      }
    }
    if (!authData || !authData.isAuthenticated) {
      throw createError({
        statusCode: 401,
        message: "User must be authenticated to associate cart with customer"
      });
    }
    let customerId = null;
    let customerEmail = null;
    if (authData.email) {
      customerEmail = authData.email;
      console.log(`Found customer email in auth data: ${customerEmail}`);
    }
    if (!customerEmail) {
      throw createError({
        statusCode: 400,
        message: "Customer email could not be determined"
      });
    }
    if ((_c = (_b = authData.medusa) == null ? void 0 : _b.customer) == null ? void 0 : _c.id) {
      customerId = authData.medusa.customer.id;
      console.log(`Found customer ID in auth data: ${customerId}`);
    }
    if (!customerId) {
      const session = await getSession(event);
      if (session == null ? void 0 : session.medusaCustomerId) {
        customerId = session.medusaCustomerId;
        console.log(`Found customer ID in session data: ${customerId}`);
      }
    }
    if (!customerId) {
      customerId = await getCustomerIdFromAuth(event);
      if (customerId) {
        console.log(`Found customer ID using getCustomerIdFromAuth: ${customerId}`);
      }
    }
    if (!customerId) {
      console.log(`No customer ID found through normal channels for email: ${customerEmail}`);
      throw createError({
        statusCode: 400,
        message: "Customer ID could not be determined. Please ensure you are logged in with a valid account."
      });
    }
    console.log(`Attempting to associate cart ${id} with customer ${customerId}`);
    console.log("Re-authenticating customer to ensure fresh token before cart association...");
    let freshCustomerToken = await authenticateCustomer(
      customerEmail,
      medusaUrl,
      publishableKey,
      ((_e = (_d = authData.medusa) == null ? void 0 : _d.customer) == null ? void 0 : _e.first_name) || authData.first_name || "",
      ((_g = (_f = authData.medusa) == null ? void 0 : _f.customer) == null ? void 0 : _g.last_name) || authData.last_name || ""
    );
    if (!freshCustomerToken) {
      console.error("Failed to obtain fresh token before cart association");
      throw createError({
        statusCode: 401,
        message: "Authentication failed before cart association"
      });
    }
    console.log("Obtained fresh JWT token for cart association:", freshCustomerToken.substring(0, 10) + "...");
    setJwtCookie(event, freshCustomerToken);
    if (!authData.medusa) {
      authData.medusa = {};
    }
    if (authData.medusa) {
      authData.medusa.token = freshCustomerToken;
    }
    console.log("Ensuring customer is registered before cart association...");
    const registrationResult = await ensureCustomerIsRegistered(
      customerEmail,
      freshCustomerToken,
      medusaUrl,
      publishableKey,
      event
    );
    if (registrationResult && registrationResult.isRegistered) {
      console.log("Customer is now confirmed as registered");
      if (registrationResult.token && registrationResult.token !== freshCustomerToken) {
        console.log("Updating token after registration");
        freshCustomerToken = registrationResult.token;
        setJwtCookie(event, freshCustomerToken);
        if (authData.medusa) {
          authData.medusa.token = freshCustomerToken;
        }
      }
    } else {
      console.warn("Customer registration check returned unexpected result:", registrationResult);
    }
    console.log("Attempting cart association via direct API call to /store/carts/{id}/customer");
    try {
      const response = await fetch(`${medusaUrl}/store/carts/${id}/customer`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${freshCustomerToken}`,
          "x-publishable-api-key": publishableKey
        }
        // No body is typically required for this endpoint
      });
      if (response.ok) {
        const data = await response.json();
        if (data.cart && data.cart.customer_id) {
          if (data.cart.customer_id === customerId) {
            console.log(`Successfully associated cart ${id} with customer ${customerId} using direct API call`);
          } else {
            console.warn(`WARN: Direct API call succeeded (200 OK) but returned customer_id (${data.cart.customer_id}) does not match expected customerId (${customerId}). Proceeding anyway.`);
          }
          return {
            success: true,
            cart: data.cart
          };
        } else {
          console.warn("Direct API call succeeded (200 OK) but response did not contain expected cart data. Proceeding anyway.", data);
          return {
            success: true,
            // Return an empty cart object or null if appropriate
            cart: data.cart || null
          };
        }
      } else {
        const errorStatus = response.status;
        const errorText = await response.text();
        console.error(`Direct API call failed with status ${errorStatus}:`, errorText);
        throw createError({
          statusCode: errorStatus,
          message: `Failed to associate cart with customer via API: ${errorText || "Unknown API error"}`
        });
      }
    } catch (directError) {
      console.error("Error during direct API call for cart association:", directError);
      throw createError({
        statusCode: 500,
        message: `Failed during cart association process: ${directError.message || "Unknown error"}`
      });
    }
  } catch (error) {
    console.error("Error in cart-customer association endpoint:", error.message);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "An unknown error occurred"
    });
  }
});

const customer$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: customer
});

const ADMIN_EMAIL$1 = "<EMAIL>";
const ADMIN_PASSWORD$1 = "Business95!";
async function getAdminToken$1(medusaUrl) {
  try {
    console.log("Obtaining admin token");
    const loginResponse = await fetch(`${medusaUrl}/auth/user/emailpass`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        email: ADMIN_EMAIL$1,
        password: ADMIN_PASSWORD$1
      })
    });
    if (!loginResponse.ok) {
      const error = await loginResponse.json().catch(() => ({}));
      console.error("Admin login failed:", error);
      throw new Error(`Admin login failed: ${error.message || loginResponse.statusText}`);
    }
    const loginData = await loginResponse.json();
    if (loginData.token) {
      console.log("Admin login successful, received JWT token");
      return loginData.token;
    }
    throw new Error("Admin login response did not contain a token");
  } catch (error) {
    console.error("Error getting admin token:", error.message);
    throw error;
  }
}
function transformOrderData(order) {
  var _a, _b, _c, _d;
  try {
    if (!order) return null;
    const orderTotal = ((_a = order.summary) == null ? void 0 : _a.current_order_total) || ((_b = order.summary) == null ? void 0 : _b.original_order_total) || 0;
    let displayStatus = "pending";
    if (order.payment_status === "captured") {
      displayStatus = "paid";
    } else if (order.payment_status === "authorized") {
      displayStatus = "authorized";
    }
    if (order.fulfillment_status === "fulfilled") {
      displayStatus = "processing";
    } else if (order.fulfillment_status === "shipped") {
      displayStatus = "shipped";
    } else if (order.fulfillment_status === "delivered") {
      displayStatus = "completed";
    }
    const formattedItems = ((_c = order.items) == null ? void 0 : _c.map((item) => ({
      id: item.id,
      title: item.title || item.subtitle || "Product",
      unit_price: item.unit_price || 0,
      quantity: item.quantity || 1,
      thumbnail: item.thumbnail || null,
      variant: {
        id: item.variant_id || "",
        title: item.variant_title || "",
        sku: item.variant_sku || "",
        product: {
          title: item.product_title || "",
          thumbnail: item.thumbnail || ""
        }
      }
    }))) || [];
    return {
      id: order.id,
      display_id: ((_d = order.display_id) == null ? void 0 : _d.toString()) || "",
      status: displayStatus,
      total: orderTotal,
      subtotal: orderTotal,
      // If subtotal is missing, use total
      shipping_total: 0,
      // Default to 0 if missing
      tax_total: 0,
      // Default to 0 if missing
      items: formattedItems,
      shipping_address: {},
      // Default empty object if missing
      billing_address: {},
      // Default empty object if missing
      shipping_methods: [],
      // Default empty array if missing
      payments: [],
      // Default empty array if missing
      created_at: order.created_at,
      updated_at: order.updated_at,
      currency_code: "RON"
      // Default to RON
    };
  } catch (error) {
    console.error("Error transforming order data:", error);
    return null;
  }
}
const _id_$2 = defineEventHandler(async (event) => {
  var _a;
  try {
    const orderId = (_a = event.context.params) == null ? void 0 : _a.id;
    if (!orderId) {
      throw createError({
        statusCode: 400,
        message: "Order ID is required"
      });
    }
    console.log("Fetching order with ID:", orderId);
    const cookies = parseCookies(event);
    const authCookie = cookies[AUTH_COOKIE_NAME] || cookies["auth"];
    if (!authCookie) {
      throw createError({
        statusCode: 401,
        message: "Authentication required"
      });
    }
    const config = useRuntimeConfig();
    const medusaUrl = config.medusaUrl || "https://api.handmadein.ro";
    const adminToken = await getAdminToken$1(medusaUrl);
    const orderResponse = await fetch(`${medusaUrl}/admin/orders/${orderId}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${adminToken}`,
        "Content-Type": "application/json"
      }
    });
    if (!orderResponse.ok) {
      const errorData = await orderResponse.json().catch(() => ({}));
      console.error("Error fetching order details:", errorData);
      throw createError({
        statusCode: orderResponse.status,
        message: `Failed to fetch order details: ${errorData.message || orderResponse.statusText}`
      });
    }
    const orderData = await orderResponse.json();
    console.log("Order details retrieved successfully");
    const transformedOrder = transformOrderData(orderData.order);
    return {
      order: transformedOrder
    };
  } catch (error) {
    console.error("Error in order details endpoint:", error.message);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "An unknown error occurred"
    });
  }
});

const _id_$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id_$2
});

const updateCustomer = defineEventHandler(async (event) => {
  var _a;
  try {
    const config = useRuntimeConfig();
    const medusaUrl = config.medusaUrl || "https://api.handmadein.ro";
    const adminToken = config.medusaAdminToken;
    if (!adminToken) {
      console.error("Admin token not found in configuration");
      throw createError({
        statusCode: 500,
        message: "Server configuration error"
      });
    }
    const url = getRequestURL(event);
    const id = (_a = event.context.params) == null ? void 0 : _a.id;
    if (!id) {
      throw createError({
        statusCode: 400,
        message: "Order ID is required"
      });
    }
    console.log(`Processing order update for order ID: ${id}`);
    const body = await readBody(event);
    const customerId = body.customerId;
    if (!customerId) {
      throw createError({
        statusCode: 400,
        message: "Customer ID is required"
      });
    }
    console.log(`Updating order ${id} to associate with customer ${customerId}`);
    const session = await getSession(event);
    if (!session || !session.username) {
      throw createError({
        statusCode: 401,
        message: "Authentication required"
      });
    }
    const updateResponse = await fetch(`${medusaUrl}/admin/orders/${id}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${adminToken}`
      },
      body: JSON.stringify({
        customer_id: customerId
      })
    });
    if (!updateResponse.ok) {
      const errorData = await updateResponse.json().catch(() => ({}));
      console.error("Error updating order customer association:", errorData);
      throw createError({
        statusCode: updateResponse.status,
        message: `Failed to update order: ${errorData.message || updateResponse.statusText}`
      });
    }
    const updateData = await updateResponse.json();
    console.log(`Successfully updated order ${id} with customer ${customerId}`);
    return {
      success: true,
      order: updateData.order
    };
  } catch (error) {
    console.error("Error in update-customer endpoint:", error.message);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "An unknown error occurred"
    });
  }
});

const updateCustomer$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: updateCustomer
});

const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Business95!";
async function getAdminToken(medusaUrl) {
  try {
    console.log("Obtaining admin token");
    const loginResponse = await fetch(`${medusaUrl}/auth/user/emailpass`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD
      })
    });
    if (!loginResponse.ok) {
      const error = await loginResponse.json().catch(() => ({}));
      console.error("Admin login failed:", error);
      throw new Error(`Admin login failed: ${error.message || loginResponse.statusText}`);
    }
    const loginData = await loginResponse.json();
    if (loginData.token) {
      console.log("Admin login successful, received JWT token");
      return loginData.token;
    }
    throw new Error("Admin login response did not contain a token");
  } catch (error) {
    console.error("Error getting admin token:", error.message);
    throw error;
  }
}
async function findCustomerByEmail(email, medusaUrl, adminToken) {
  try {
    console.log(`Looking up customer with email: ${email}`);
    const searchResponse = await fetch(`${medusaUrl}/admin/customers?q=${encodeURIComponent(email)}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${adminToken}`
      }
    });
    if (!searchResponse.ok) {
      const error = await searchResponse.json().catch(() => ({}));
      console.error("Customer search failed:", error);
      throw new Error(`Customer search failed: ${error.message || searchResponse.statusText}`);
    }
    const searchData = await searchResponse.json();
    if (searchData.customers && searchData.customers.length > 0) {
      console.log(`Found ${searchData.customers.length} customer records with this email`);
      searchData.customers.forEach((customer, index) => {
        var _a;
        console.log(`Customer ${index + 1}: ID=${customer.id}, 
          Has orders: ${Boolean((_a = customer.orders) == null ? void 0 : _a.length)}, 
          Has metadata: ${Boolean(customer.metadata && Object.keys(customer.metadata).length)},
          Created at: ${customer.created_at}`);
      });
      if (searchData.customers.length === 1) {
        const customer = searchData.customers[0];
        console.log(`Single customer found with ID: ${customer.id}`);
        return customer;
      }
      const customerWithOrders = searchData.customers.find((c) => c.orders && c.orders.length > 0);
      if (customerWithOrders) {
        console.log(`Selected customer with orders, ID: ${customerWithOrders.id}`);
        return customerWithOrders;
      }
      const customerWithAccount = searchData.customers.find((c) => c.has_account === true || c.metadata && c.metadata.has_account === true);
      if (customerWithAccount) {
        console.log(`Selected customer with account flag, ID: ${customerWithAccount.id}`);
        return customerWithAccount;
      }
      const customersWithMetadata = searchData.customers.filter((c) => c.metadata && Object.keys(c.metadata).length > 0).sort((a, b) => Object.keys(b.metadata || {}).length - Object.keys(a.metadata || {}).length);
      if (customersWithMetadata.length) {
        console.log(`Selected customer with most metadata, ID: ${customersWithMetadata[0].id}`);
        return customersWithMetadata[0];
      }
      const sortedCustomers = [...searchData.customers].sort(
        (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
      console.log(`Selected oldest customer record, ID: ${sortedCustomers[0].id}`);
      return sortedCustomers[0];
    }
    return null;
  } catch (error) {
    console.error("Error in findCustomerByEmail:", error);
    throw error;
  }
}
const index = defineEventHandler(async (event) => {
  var _a, _b, _c, _d;
  try {
    const cookies = parseCookies(event);
    console.log("Cookies received:", Object.keys(cookies));
    const authCookie = cookies[AUTH_COOKIE_NAME] || cookies["auth"];
    console.log("Auth cookie found:", authCookie ? "Yes" : "No");
    if (!authCookie) {
      throw createError({
        statusCode: 401,
        message: "Authentication required"
      });
    }
    const config = useRuntimeConfig();
    const medusaUrl = config.medusaUrl || "https://api.handmadein.ro";
    console.log("Using Medusa URL:", medusaUrl);
    let authData = null;
    let customerEmail = null;
    try {
      console.log("Parsing auth cookie");
      authData = JSON.parse(authCookie);
      if (authData.isAuthenticated && authData.email) {
        customerEmail = authData.email;
        console.log("Customer email found:", customerEmail);
      }
    } catch (parseError) {
      console.error("Error parsing auth cookie:", parseError);
      try {
        authData = JSON.parse(decodeURIComponent(authCookie));
        if (authData.isAuthenticated && authData.email) {
          customerEmail = authData.email;
          console.log("Customer email found after decoding:", customerEmail);
        }
      } catch (decodeError) {
        console.error("Error decoding and parsing auth cookie:", decodeError);
      }
    }
    if (!customerEmail && ((_a = authData == null ? void 0 : authData.user) == null ? void 0 : _a.email)) {
      customerEmail = authData.user.email;
      console.log("Customer email found in user object:", customerEmail);
    }
    if (!customerEmail && ((_c = (_b = authData == null ? void 0 : authData.medusa) == null ? void 0 : _b.customer) == null ? void 0 : _c.email)) {
      customerEmail = authData.medusa.customer.email;
      console.log("Customer email found in medusa.customer object:", customerEmail);
    }
    if (!customerEmail) {
      console.error("No customer email found in auth cookie");
      throw createError({
        statusCode: 401,
        message: "Authentication required - customer email not found"
      });
    }
    console.log("Getting admin token...");
    const adminToken = await getAdminToken(medusaUrl);
    console.log("Admin token obtained successfully");
    console.log("Looking up customer...");
    const customer = await findCustomerByEmail(
      customerEmail,
      medusaUrl,
      adminToken
    );
    if (!customer) {
      throw createError({
        statusCode: 404,
        message: "Customer not found"
      });
    }
    console.log("Customer found:", customer.id);
    console.log(`Fetching orders for customer: ${customer.id}`);
    const ordersResponse = await fetch(`${medusaUrl}/admin/orders?customer_id=${customer.id}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${adminToken}`,
        "Content-Type": "application/json"
      }
    });
    if (!ordersResponse.ok) {
      const errorData = await ordersResponse.json().catch(() => ({}));
      console.error("Error fetching customer orders:", errorData);
      throw createError({
        statusCode: ordersResponse.status,
        message: `Failed to fetch customer orders: ${errorData.message || ordersResponse.statusText}`
      });
    }
    const ordersData = await ordersResponse.json();
    console.log(`Retrieved ${((_d = ordersData.orders) == null ? void 0 : _d.length) || 0} orders successfully`);
    return {
      orders: ordersData.orders || [],
      count: ordersData.count || 0,
      offset: ordersData.offset || 0,
      limit: ordersData.limit || 20
    };
  } catch (error) {
    console.error("Error in orders endpoint:", error.message);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || "An unknown error occurred"
    });
  }
});

const index$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index
});

const _id_ = defineEventHandler(async (event) => {
  var _a, _b, _c;
  const config = useRuntimeConfig();
  const baseUrl = ((_a = config.public) == null ? void 0 : _a.medusaApiUrl) || "https://api.handmadein.ro";
  const publishableKey = typeof ((_b = config.public) == null ? void 0 : _b.medusaPublishableKey) === "string" ? config.public.medusaPublishableKey : "pk_b67a593e3089daed370d80a30c3c66e559afba90e52c28f8d989a69bdadb27e9";
  const productId = (_c = event.context.params) == null ? void 0 : _c.id;
  if (!productId) {
    throw createError({
      statusCode: 400,
      message: "Product ID is required"
    });
  }
  try {
    console.log(`Fetching product with ID: ${productId} from ${baseUrl}/store/products/${productId}`);
    const response = await fetch(`${baseUrl}/store/products/${productId}`, {
      headers: {
        "x-publishable-api-key": publishableKey,
        "Accept": "application/json"
      }
    });
    if (!response.ok) {
      console.error(`Error response from Medusa: ${response.status} ${response.statusText}`);
      let errorInfo = "";
      try {
        const errorData = await response.json();
        errorInfo = JSON.stringify(errorData);
      } catch (e) {
        errorInfo = "Could not parse error response";
      }
      console.error(`Error details: ${errorInfo}`);
      throw createError({
        statusCode: response.status,
        statusMessage: response.statusText,
        message: `Failed to fetch product data: ${response.statusText}`
      });
    }
    const data = await response.json();
    if (!data || typeof data !== "object") {
      console.error(`Invalid response data format: ${JSON.stringify(data)}`);
      throw createError({
        statusCode: 500,
        message: "Invalid response data format from Medusa API"
      });
    }
    const product = data.product;
    if (!product) {
      console.error(`Product not found in response: ${JSON.stringify(data)}`);
      throw createError({
        statusCode: 404,
        message: "Product not found"
      });
    }
    let price = 0;
    let currency = "USD";
    if (product.variants && product.variants.length > 0) {
      const variant = product.variants[0];
      if (variant.calculated_price) {
        price = variant.calculated_price.calculated_amount;
        currency = variant.calculated_price.currency_code;
      } else if (variant.prices && variant.prices.length > 0) {
        price = variant.prices[0].amount;
        currency = variant.prices[0].currency_code;
      }
    }
    return {
      id: product.id,
      title: product.title,
      handle: product.handle,
      description: product.description,
      thumbnail: product.thumbnail,
      price,
      currency,
      // Include other fields that might be useful
      images: product.images || [],
      variants: product.variants || []
    };
  } catch (error) {
    console.error(`Failed to fetch product ${productId}:`, error);
    let statusCode = 500;
    let errorMessage = "Failed to fetch product data";
    let statusMessage = "";
    if (error instanceof Error) {
      errorMessage = error.message;
      if (typeof error === "object" && error !== null) {
        if ("statusCode" in error) {
          statusCode = error.statusCode;
        }
        if ("statusMessage" in error) {
          statusMessage = error.statusMessage;
        }
      }
    }
    throw createError({
      statusCode,
      statusMessage,
      message: errorMessage
    });
  }
});

const _id_$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id_
});

const prices = defineEventHandler(async (event) => {
  var _a, _b, _c, _d;
  const config = useRuntimeConfig();
  const baseUrl = ((_a = config.public) == null ? void 0 : _a.medusaApiUrl) || "https://api.handmadein.ro";
  const publishableKey = typeof ((_b = config.public) == null ? void 0 : _b.medusaPublishableKey) === "string" ? config.public.medusaPublishableKey : "pk_b67a593e3089daed370d80a30c3c66e559afba90e52c28f8d989a69bdadb27e9";
  try {
    const body = await readBody(event);
    const inputIds = body.ids;
    if (!inputIds || !Array.isArray(inputIds) || inputIds.length === 0) {
      throw createError({
        statusCode: 400,
        message: "Product IDs array is required"
      });
    }
    const productIds = inputIds.map((id) => {
      if (typeof id === "string") {
        if (id.includes(":")) {
          return id.split(":")[0];
        } else if (id.includes("_variant_")) {
          return id.split("_variant_")[0];
        }
      }
      return id;
    });
    const queryParams = new URLSearchParams();
    queryParams.append("ids", productIds.join(","));
    const regionId = (_c = event.context.query) == null ? void 0 : _c.region_id;
    if (regionId) {
      queryParams.append("region_id", regionId);
    }
    const currencyCode = ((_d = event.context.query) == null ? void 0 : _d.currency_code) || "RON";
    queryParams.append("currency_code", currencyCode);
    console.log(`Fetching products from: ${baseUrl}/store/products/batch?${queryParams.toString()}`);
    console.log(`Request details - Product IDs: ${productIds.join(", ")}, Currency: ${currencyCode}`);
    const response = await fetch(`${baseUrl}/store/products/batch?${queryParams.toString()}`, {
      headers: {
        "x-publishable-api-key": publishableKey
      }
    });
    console.log(`Batch products response status: ${response.status} ${response.statusText}`);
    if (!response.ok) {
      if (response.status === 404) {
        console.error("404 error when fetching products. Request details:", {
          url: `${baseUrl}/store/products/batch?${queryParams.toString()}`,
          productIds
        });
      }
      throw createError({
        statusCode: response.status,
        message: `Failed to fetch products: ${response.statusText || response.status}`
      });
    }
    const data = await response.json();
    const products = data.products;
    if (!products || !Array.isArray(products)) {
      throw createError({
        statusCode: 500,
        message: "Invalid response format from backend"
      });
    }
    const priceMap = {};
    const variantMap = {};
    products.forEach((product) => {
      if (product.variants && product.variants.length > 0) {
        const defaultVariant = product.variants[0];
        let defaultPrice = 0;
        let defaultCurrency = "USD";
        if (defaultVariant.calculated_price) {
          defaultPrice = defaultVariant.calculated_price.calculated_amount;
          defaultCurrency = defaultVariant.calculated_price.currency_code;
        } else if (defaultVariant.prices && defaultVariant.prices.length > 0) {
          defaultPrice = defaultVariant.prices[0].amount;
          defaultCurrency = defaultVariant.prices[0].currency_code;
        }
        priceMap[product.id] = {
          id: product.id,
          price: defaultPrice,
          currency: defaultCurrency
        };
        product.variants.forEach((variant) => {
          let variantPrice = 0;
          let variantCurrency = "USD";
          if (variant.calculated_price) {
            variantPrice = variant.calculated_price.calculated_amount;
            variantCurrency = variant.calculated_price.currency_code;
          } else if (variant.prices && variant.prices.length > 0) {
            variantPrice = variant.prices[0].amount;
            variantCurrency = variant.prices[0].currency_code;
          }
          variantMap[variant.id] = {
            price: variantPrice,
            currency: variantCurrency
          };
        });
        inputIds.forEach((inputId) => {
          if (typeof inputId === "string") {
            if (inputId.includes(":")) {
              const [prodId, variantId] = inputId.split(":");
              if (prodId === product.id) {
                if (variantId && variantMap[variantId]) {
                  priceMap[inputId] = {
                    id: inputId,
                    price: variantMap[variantId].price,
                    currency: variantMap[variantId].currency
                  };
                } else {
                  priceMap[inputId] = {
                    id: inputId,
                    price: defaultPrice,
                    currency: defaultCurrency
                  };
                }
              }
            } else if (inputId.includes("_variant_")) {
              const [prodId, variantPart] = inputId.split("_variant_");
              if (prodId === product.id) {
                const variantId = "variant_" + variantPart;
                if (variantMap[variantId]) {
                  priceMap[inputId] = {
                    id: inputId,
                    price: variantMap[variantId].price,
                    currency: variantMap[variantId].currency
                  };
                } else {
                  if (variantMap[variantPart]) {
                    priceMap[inputId] = {
                      id: inputId,
                      price: variantMap[variantPart].price,
                      currency: variantMap[variantPart].currency
                    };
                  } else {
                    priceMap[inputId] = {
                      id: inputId,
                      price: defaultPrice,
                      currency: defaultCurrency
                    };
                  }
                }
              }
            } else if (variantMap[inputId]) {
              priceMap[inputId] = {
                id: inputId,
                price: variantMap[inputId].price,
                currency: variantMap[inputId].currency
              };
            }
          }
        });
      }
    });
    return priceMap;
  } catch (error) {
    console.error("Failed to fetch product prices:", error);
    throw createError({
      statusCode: error instanceof Error && "statusCode" in error ? error.statusCode : 500,
      message: error instanceof Error ? error.message : "Failed to fetch product prices"
    });
  }
});

const prices$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: prices
});

const Vue3 = version[0] === "3";

function resolveUnref(r) {
  return typeof r === "function" ? r() : unref(r);
}
function resolveUnrefHeadInput(ref) {
  if (ref instanceof Promise || ref instanceof Date || ref instanceof RegExp)
    return ref;
  const root = resolveUnref(ref);
  if (!ref || !root)
    return root;
  if (Array.isArray(root))
    return root.map((r) => resolveUnrefHeadInput(r));
  if (typeof root === "object") {
    const resolved = {};
    for (const k in root) {
      if (!Object.prototype.hasOwnProperty.call(root, k)) {
        continue;
      }
      if (k === "titleTemplate" || k[0] === "o" && k[1] === "n") {
        resolved[k] = unref(root[k]);
        continue;
      }
      resolved[k] = resolveUnrefHeadInput(root[k]);
    }
    return resolved;
  }
  return root;
}

const VueReactivityPlugin = defineHeadPlugin({
  hooks: {
    "entries:resolve": (ctx) => {
      for (const entry of ctx.entries)
        entry.resolvedInput = resolveUnrefHeadInput(entry.input);
    }
  }
});

const headSymbol = "usehead";
function vueInstall(head) {
  const plugin = {
    install(app) {
      if (Vue3) {
        app.config.globalProperties.$unhead = head;
        app.config.globalProperties.$head = head;
        app.provide(headSymbol, head);
      }
    }
  };
  return plugin.install;
}
function createServerHead(options = {}) {
  const head = createServerHead$1(options);
  head.use(VueReactivityPlugin);
  head.install = vueInstall(head);
  return head;
}

const unheadPlugins = true ? [CapoPlugin({ track: true })] : [];

const renderSSRHeadOptions = {"omitLineBreaks":false};

globalThis.__buildAssetsURL = buildAssetsURL;
globalThis.__publicAssetsURL = publicAssetsURL;
const getClientManifest = () => import('file://C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt/dist/server/client.manifest.mjs').then((r) => r.default || r).then((r) => typeof r === "function" ? r() : r);
const getServerEntry = () => import('file://C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt/dist/server/server.mjs').then((r) => r.default || r);
const getSSRStyles = lazyCachedFunction(() => Promise.resolve().then(function () { return styles$1; }).then((r) => r.default || r));
const getSSRRenderer = lazyCachedFunction(async () => {
  const manifest = await getClientManifest();
  if (!manifest) {
    throw new Error("client.manifest is not available");
  }
  const createSSRApp = await getServerEntry();
  if (!createSSRApp) {
    throw new Error("Server bundle is not available");
  }
  const options = {
    manifest,
    renderToString: renderToString$1,
    buildAssetsURL
  };
  const renderer = createRenderer(createSSRApp, options);
  async function renderToString$1(input, context) {
    const html = await renderToString(input, context);
    if (process.env.NUXT_VITE_NODE_OPTIONS) {
      renderer.rendererContext.updateManifest(await getClientManifest());
    }
    return APP_ROOT_OPEN_TAG + html + APP_ROOT_CLOSE_TAG;
  }
  return renderer;
});
const getSPARenderer = lazyCachedFunction(async () => {
  const manifest = await getClientManifest();
  const spaTemplate = await Promise.resolve().then(function () { return _virtual__spaTemplate; }).then((r) => r.template).catch(() => "").then((r) => {
    {
      return APP_ROOT_OPEN_TAG + r + APP_ROOT_CLOSE_TAG;
    }
  });
  const options = {
    manifest,
    renderToString: () => spaTemplate,
    buildAssetsURL
  };
  const renderer = createRenderer(() => () => {
  }, options);
  const result = await renderer.renderToString({});
  const renderToString = (ssrContext) => {
    const config = useRuntimeConfig(ssrContext.event);
    ssrContext.modules = ssrContext.modules || /* @__PURE__ */ new Set();
    ssrContext.payload.serverRendered = false;
    ssrContext.config = {
      public: config.public,
      app: config.app
    };
    return Promise.resolve(result);
  };
  return {
    rendererContext: renderer.rendererContext,
    renderToString
  };
});
const ISLAND_SUFFIX_RE = /\.json(\?.*)?$/;
async function getIslandContext(event) {
  let url = event.path || "";
  const componentParts = url.substring("/__nuxt_island".length + 1).replace(ISLAND_SUFFIX_RE, "").split("_");
  const hashId = componentParts.length > 1 ? componentParts.pop() : void 0;
  const componentName = componentParts.join("_");
  const context = event.method === "GET" ? getQuery$1(event) : await readBody(event);
  const ctx = {
    url: "/",
    ...context,
    id: hashId,
    name: componentName,
    props: destr(context.props) || {},
    slots: {},
    components: {}
  };
  return ctx;
}
const HAS_APP_TELEPORTS = !!(appTeleportAttrs.id);
const APP_TELEPORT_OPEN_TAG = HAS_APP_TELEPORTS ? `<${appTeleportTag}${propsToString(appTeleportAttrs)}>` : "";
const APP_TELEPORT_CLOSE_TAG = HAS_APP_TELEPORTS ? `</${appTeleportTag}>` : "";
const APP_ROOT_OPEN_TAG = `<${appRootTag}${propsToString(appRootAttrs)}>`;
const APP_ROOT_CLOSE_TAG = `</${appRootTag}>`;
const PAYLOAD_URL_RE = /\/_payload.json(\?.*)?$/ ;
const ROOT_NODE_REGEX = new RegExp(`^<${appRootTag}[^>]*>([\\s\\S]*)<\\/${appRootTag}>$`);
const renderer = defineRenderHandler(async (event) => {
  const nitroApp = useNitroApp();
  const ssrError = event.path.startsWith("/__nuxt_error") ? getQuery$1(event) : null;
  if (ssrError && ssrError.statusCode) {
    ssrError.statusCode = Number.parseInt(ssrError.statusCode);
  }
  if (ssrError && !("__unenv__" in event.node.req)) {
    throw createError({
      statusCode: 404,
      statusMessage: "Page Not Found: /__nuxt_error"
    });
  }
  const isRenderingIsland = event.path.startsWith("/__nuxt_island");
  const islandContext = isRenderingIsland ? await getIslandContext(event) : void 0;
  let url = ssrError?.url || islandContext?.url || event.path;
  const isRenderingPayload = PAYLOAD_URL_RE.test(url) && !isRenderingIsland;
  if (isRenderingPayload) {
    url = url.substring(0, url.lastIndexOf("/")) || "/";
    event._path = url;
    event.node.req.url = url;
  }
  const routeOptions = getRouteRules(event);
  const head = createServerHead({
    plugins: unheadPlugins
  });
  const headEntryOptions = { mode: "server" };
  if (!isRenderingIsland) {
    head.push(appHead, headEntryOptions);
  }
  const ssrContext = {
    url,
    event,
    runtimeConfig: useRuntimeConfig(event),
    noSSR: event.context.nuxt?.noSSR || routeOptions.ssr === false && !isRenderingIsland || (false),
    head,
    error: !!ssrError,
    nuxt: void 0,
    /* NuxtApp */
    payload: ssrError ? { error: ssrError } : {},
    _payloadReducers: /* @__PURE__ */ Object.create(null),
    modules: /* @__PURE__ */ new Set(),
    islandContext
  };
  const renderer = ssrContext.noSSR ? await getSPARenderer() : await getSSRRenderer();
  const _rendered = await renderer.renderToString(ssrContext).catch(async (error) => {
    if (ssrContext._renderResponse && error.message === "skipping render") {
      return {};
    }
    const _err = !ssrError && ssrContext.payload?.error || error;
    await ssrContext.nuxt?.hooks.callHook("app:error", _err);
    throw _err;
  });
  await ssrContext.nuxt?.hooks.callHook("app:rendered", { ssrContext, renderResult: _rendered });
  if (ssrContext._renderResponse) {
    return ssrContext._renderResponse;
  }
  if (ssrContext.payload?.error && !ssrError) {
    throw ssrContext.payload.error;
  }
  if (isRenderingPayload) {
    const response2 = renderPayloadResponse(ssrContext);
    return response2;
  }
  const inlinedStyles = isRenderingIsland ? await renderInlineStyles(ssrContext.modules ?? []) : [];
  const NO_SCRIPTS = routeOptions.experimentalNoScripts;
  const { styles, scripts } = getRequestDependencies(ssrContext, renderer.rendererContext);
  if (ssrContext._preloadManifest) {
    head.push({
      link: [
        { rel: "preload", as: "fetch", fetchpriority: "low", crossorigin: "anonymous", href: buildAssetsURL(`builds/meta/${ssrContext.runtimeConfig.app.buildId}.json`) }
      ]
    }, { ...headEntryOptions, tagPriority: "low" });
  }
  if (inlinedStyles.length) {
    head.push({ style: inlinedStyles });
  }
  {
    const link = [];
    for (const resource of Object.values(styles)) {
      if ("inline" in getQuery(resource.file)) {
        continue;
      }
      if (!isRenderingIsland || resource.file.includes("scoped") && !resource.file.includes("pages/")) {
        link.push({ rel: "stylesheet", href: renderer.rendererContext.buildAssetsURL(resource.file), crossorigin: "" });
      }
    }
    if (link.length) {
      head.push({ link }, headEntryOptions);
    }
  }
  if (!NO_SCRIPTS && !isRenderingIsland) {
    head.push({
      link: getPreloadLinks(ssrContext, renderer.rendererContext)
    }, headEntryOptions);
    head.push({
      link: getPrefetchLinks(ssrContext, renderer.rendererContext)
    }, headEntryOptions);
    head.push({
      script: renderPayloadJsonScript({ ssrContext, data: ssrContext.payload }) 
    }, {
      ...headEntryOptions,
      // this should come before another end of body scripts
      tagPosition: "bodyClose",
      tagPriority: "high"
    });
  }
  if (!routeOptions.experimentalNoScripts && !isRenderingIsland) {
    head.push({
      script: Object.values(scripts).map((resource) => ({
        type: resource.module ? "module" : null,
        src: renderer.rendererContext.buildAssetsURL(resource.file),
        defer: resource.module ? null : true,
        // if we are rendering script tag payloads that import an async payload
        // we need to ensure this resolves before executing the Nuxt entry
        tagPosition: "head",
        crossorigin: ""
      }))
    }, headEntryOptions);
  }
  const { headTags, bodyTags, bodyTagsOpen, htmlAttrs, bodyAttrs } = await renderSSRHead(head, renderSSRHeadOptions);
  const htmlContext = {
    island: isRenderingIsland,
    htmlAttrs: htmlAttrs ? [htmlAttrs] : [],
    head: normalizeChunks([headTags]),
    bodyAttrs: bodyAttrs ? [bodyAttrs] : [],
    bodyPrepend: normalizeChunks([bodyTagsOpen, ssrContext.teleports?.body]),
    body: [
      replaceIslandTeleports(ssrContext, _rendered.html) ,
      APP_TELEPORT_OPEN_TAG + (HAS_APP_TELEPORTS ? joinTags([ssrContext.teleports?.[`#${appTeleportAttrs.id}`]]) : "") + APP_TELEPORT_CLOSE_TAG
    ],
    bodyAppend: [bodyTags]
  };
  await nitroApp.hooks.callHook("render:html", htmlContext, { event });
  if (isRenderingIsland && islandContext) {
    const islandHead = {};
    for (const entry of head.headEntries()) {
      for (const [key, value] of Object.entries(resolveUnrefHeadInput(entry.input))) {
        const currentValue = islandHead[key];
        if (Array.isArray(currentValue)) {
          currentValue.push(...value);
        }
        islandHead[key] = value;
      }
    }
    islandHead.link ||= [];
    islandHead.style ||= [];
    const islandResponse = {
      id: islandContext.id,
      head: islandHead,
      html: getServerComponentHTML(htmlContext.body),
      components: getClientIslandResponse(ssrContext),
      slots: getSlotIslandResponse(ssrContext)
    };
    await nitroApp.hooks.callHook("render:island", islandResponse, { event, islandContext });
    const response2 = {
      body: JSON.stringify(islandResponse, null, 2),
      statusCode: getResponseStatus(event),
      statusMessage: getResponseStatusText(event),
      headers: {
        "content-type": "application/json;charset=utf-8",
        "x-powered-by": "Nuxt"
      }
    };
    return response2;
  }
  const response = {
    body: renderHTMLDocument(htmlContext),
    statusCode: getResponseStatus(event),
    statusMessage: getResponseStatusText(event),
    headers: {
      "content-type": "text/html;charset=utf-8",
      "x-powered-by": "Nuxt"
    }
  };
  return response;
});
function lazyCachedFunction(fn) {
  let res = null;
  return () => {
    if (res === null) {
      res = fn().catch((err) => {
        res = null;
        throw err;
      });
    }
    return res;
  };
}
function normalizeChunks(chunks) {
  return chunks.filter(Boolean).map((i) => i.trim());
}
function joinTags(tags) {
  return tags.join("");
}
function joinAttrs(chunks) {
  if (chunks.length === 0) {
    return "";
  }
  return " " + chunks.join(" ");
}
function renderHTMLDocument(html) {
  return `<!DOCTYPE html><html${joinAttrs(html.htmlAttrs)}><head>${joinTags(html.head)}</head><body${joinAttrs(html.bodyAttrs)}>${joinTags(html.bodyPrepend)}${joinTags(html.body)}${joinTags(html.bodyAppend)}</body></html>`;
}
async function renderInlineStyles(usedModules) {
  const styleMap = await getSSRStyles();
  const inlinedStyles = /* @__PURE__ */ new Set();
  for (const mod of usedModules) {
    if (mod in styleMap && styleMap[mod]) {
      for (const style of await styleMap[mod]()) {
        inlinedStyles.add(style);
      }
    }
  }
  return Array.from(inlinedStyles).map((style) => ({ innerHTML: style }));
}
function renderPayloadResponse(ssrContext) {
  return {
    body: stringify(splitPayload(ssrContext).payload, ssrContext._payloadReducers) ,
    statusCode: getResponseStatus(ssrContext.event),
    statusMessage: getResponseStatusText(ssrContext.event),
    headers: {
      "content-type": "application/json;charset=utf-8" ,
      "x-powered-by": "Nuxt"
    }
  };
}
function renderPayloadJsonScript(opts) {
  const contents = opts.data ? stringify(opts.data, opts.ssrContext._payloadReducers) : "";
  const payload = {
    "type": "application/json",
    "innerHTML": contents,
    "data-nuxt-data": appId,
    "data-ssr": !(opts.ssrContext.noSSR)
  };
  {
    payload.id = "__NUXT_DATA__";
  }
  if (opts.src) {
    payload["data-src"] = opts.src;
  }
  const config = uneval(opts.ssrContext.config);
  return [
    payload,
    {
      innerHTML: `window.__NUXT__={};window.__NUXT__.config=${config}`
    }
  ];
}
function splitPayload(ssrContext) {
  const { data, prerenderedAt, ...initial } = ssrContext.payload;
  return {
    initial: { ...initial, prerenderedAt },
    payload: { data, prerenderedAt }
  };
}
function getServerComponentHTML(body) {
  const match = body[0].match(ROOT_NODE_REGEX);
  return match?.[1] || body[0];
}
const SSR_SLOT_TELEPORT_MARKER = /^uid=([^;]*);slot=(.*)$/;
const SSR_CLIENT_TELEPORT_MARKER = /^uid=([^;]*);client=(.*)$/;
const SSR_CLIENT_SLOT_MARKER = /^island-slot=[^;]*;(.*)$/;
function getSlotIslandResponse(ssrContext) {
  if (!ssrContext.islandContext || !Object.keys(ssrContext.islandContext.slots).length) {
    return void 0;
  }
  const response = {};
  for (const [name, slot] of Object.entries(ssrContext.islandContext.slots)) {
    response[name] = {
      ...slot,
      fallback: ssrContext.teleports?.[`island-fallback=${name}`]
    };
  }
  return response;
}
function getClientIslandResponse(ssrContext) {
  if (!ssrContext.islandContext || !Object.keys(ssrContext.islandContext.components).length) {
    return void 0;
  }
  const response = {};
  for (const [clientUid, component] of Object.entries(ssrContext.islandContext.components)) {
    const html = ssrContext.teleports?.[clientUid]?.replaceAll("<!--teleport start anchor-->", "") || "";
    response[clientUid] = {
      ...component,
      html,
      slots: getComponentSlotTeleport(ssrContext.teleports ?? {})
    };
  }
  return response;
}
function getComponentSlotTeleport(teleports) {
  const entries = Object.entries(teleports);
  const slots = {};
  for (const [key, value] of entries) {
    const match = key.match(SSR_CLIENT_SLOT_MARKER);
    if (match) {
      const [, slot] = match;
      if (!slot) {
        continue;
      }
      slots[slot] = value;
    }
  }
  return slots;
}
function replaceIslandTeleports(ssrContext, html) {
  const { teleports, islandContext } = ssrContext;
  if (islandContext || !teleports) {
    return html;
  }
  for (const key in teleports) {
    const matchClientComp = key.match(SSR_CLIENT_TELEPORT_MARKER);
    if (matchClientComp) {
      const [, uid, clientId] = matchClientComp;
      if (!uid || !clientId) {
        continue;
      }
      html = html.replace(new RegExp(` data-island-uid="${uid}" data-island-component="${clientId}"[^>]*>`), (full) => {
        return full + teleports[key];
      });
      continue;
    }
    const matchSlot = key.match(SSR_SLOT_TELEPORT_MARKER);
    if (matchSlot) {
      const [, uid, slot] = matchSlot;
      if (!uid || !slot) {
        continue;
      }
      html = html.replace(new RegExp(` data-island-uid="${uid}" data-island-slot="${slot}"[^>]*>`), (full) => {
        return full + teleports[key];
      });
    }
  }
  return html;
}

const renderer$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: renderer
});

const styles = {};

const styles$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: styles
});

const template = "";

const _virtual__spaTemplate = /*#__PURE__*/Object.freeze({
  __proto__: null,
  template: template
});
//# sourceMappingURL=index.mjs.map
