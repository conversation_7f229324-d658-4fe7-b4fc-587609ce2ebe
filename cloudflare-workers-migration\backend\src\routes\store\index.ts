import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { productRoutes } from './products';
import { cartRoutes } from './cart';
import { cartSimplifiedRoutes } from './cart-simplified';
import { orderRoutes } from './orders';
import { ordersSimplifiedRoutes } from './orders-simplified';
import { customerRoutes } from './customers';
import { customersSimplifiedRoutes } from './customers-simplified';
import { journalRoutes } from './journal';
import { collectionRoutes } from './collections';
import { searchRoutes } from './search';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
};

export const storeRoutes = new Hono<{ Bindings: Bindings }>();

// CORS middleware
storeRoutes.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Mount sub-routes - Using simplified routes for better fe2 compatibility
storeRoutes.route('/products', productRoutes);
storeRoutes.route('/carts', cartSimplifiedRoutes);
storeRoutes.route('/orders', ordersSimplifiedRoutes);
storeRoutes.route('/customers', customersSimplifiedRoutes);
storeRoutes.route('/journal', journalRoutes);
storeRoutes.route('/collections', collectionRoutes);
storeRoutes.route('/search', searchRoutes);

// Legacy routes for backward compatibility
storeRoutes.route('/carts-legacy', cartRoutes);
storeRoutes.route('/orders-legacy', orderRoutes);
storeRoutes.route('/customers-legacy', customerRoutes);

// Store info endpoint
storeRoutes.get('/', (c) => {
  return c.json({
    success: true,
    data: {
      name: 'HandmadeIn.ro',
      description: 'Authentic Romanian handmade crafts',
      version: '2.0.0',
      environment: 'development',
    },
  });
});

// Regions endpoint - Updated for simplified schema
storeRoutes.get('/regions', async (c) => {
  try {
    // Get regions from the simplified schema
    const regionsQuery = `
      SELECT
        r.id, r.name, r.currency_code, r.tax_rate, r.metadata, r.created_at, r.updated_at,
        GROUP_CONCAT(rc.country_code) as countries
      FROM regions r
      LEFT JOIN region_countries rc ON r.id = rc.region_id
      WHERE r.deleted_at IS NULL
      GROUP BY r.id, r.name, r.currency_code, r.tax_rate, r.metadata, r.created_at, r.updated_at
      ORDER BY r.created_at ASC
    `;

    const regions = await c.env.DB.prepare(regionsQuery).all();

    // Process regions data
    const processedRegions = (regions.results || []).map((region: any) => {
      let metadata = {};
      try {
        if (region.metadata && typeof region.metadata === 'string') {
          metadata = JSON.parse(region.metadata);
        }
      } catch (e) {
        metadata = {};
      }

      const countries = region.countries ? region.countries.split(',') : [];

      return {
        id: region.id,
        name: region.name,
        currency_code: region.currency_code,
        currency: {
          code: region.currency_code,
          name: region.currency_code === 'RON' ? 'Romanian Leu' : 'US Dollar',
          symbol: region.currency_code === 'RON' ? 'lei' : '$',
        },
        tax_rate: region.tax_rate || 0,
        countries: countries.map((countryCode: string) => ({
          id: countryCode.toLowerCase(),
          iso_2: countryCode,
          iso_3: countryCode, // Simplified
          name: countryCode === 'RO' ? 'Romania' : countryCode,
          display_name: countryCode === 'RO' ? 'Romania' : countryCode,
        })),
        metadata: metadata,
        created_at: region.created_at,
        updated_at: region.updated_at,
      };
    });

    return c.json({
      success: true,
      data: processedRegions,
    });
  } catch (error) {
    console.error('Error fetching regions:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch regions',
    }, 500);
  }
});

// Shipping options endpoint
storeRoutes.get('/shipping-options', async (c) => {
  try {
    const region_id = c.req.query('region_id');
    const currency_code = c.req.query('currency_code') || 'RON';

    // Get shipping options from database
    const shippingOptionsQuery = `
      SELECT
        so.id, so.name, so.description, so.is_return, so.admin_only,
        so.metadata, so.created_at, so.updated_at,
        sp.price, sp.currency_code
      FROM shipping_options so
      LEFT JOIN shipping_option_prices sp ON so.id = sp.shipping_option_id
        AND sp.currency_code = ?
      WHERE so.deleted_at IS NULL
        AND so.is_return = 0
        AND so.admin_only = 0
        AND (so.region_id = ? OR so.region_id IS NULL OR ? IS NULL)
      ORDER BY sp.price ASC, so.created_at ASC
    `;

    const shippingOptions = await c.env.DB.prepare(shippingOptionsQuery)
      .bind(currency_code, region_id, region_id)
      .all();

    const processedOptions = (shippingOptions.results || []).map((option: any) => {
      let metadata = {};
      try {
        if (option.metadata && typeof option.metadata === 'string') {
          metadata = JSON.parse(option.metadata);
        }
      } catch (e) {
        metadata = {};
      }

      return {
        id: option.id,
        name: option.name,
        description: option.description,
        price: option.price || 0,
        currency_code: option.currency_code || currency_code,
        estimated_delivery: metadata.estimated_delivery || '3-5 business days',
        metadata: metadata,
      };
    });

    // If no options found in database, return default options
    if (processedOptions.length === 0) {
      const defaultPrice = currency_code === 'RON' ? 1500 : 1500; // 15 RON or 15 USD in cents
      return c.json({
        success: true,
        data: [
          {
            id: 'standard',
            name: 'Standard Shipping',
            description: 'Regular delivery',
            price: defaultPrice,
            currency_code: currency_code,
            estimated_delivery: '3-5 business days',
          },
          {
            id: 'express',
            name: 'Express Shipping',
            description: 'Fast delivery',
            price: defaultPrice * 2,
            currency_code: currency_code,
            estimated_delivery: '1-2 business days',
          },
        ],
      });
    }

    return c.json({
      success: true,
      data: processedOptions,
    });
  } catch (error) {
    console.error('Error fetching shipping options:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch shipping options',
    }, 500);
  }
});

// Payment methods endpoint
storeRoutes.get('/payment-methods', async (c) => {
  return c.json({
    success: true,
    data: [
      {
        id: 'stripe',
        name: 'Credit/Debit Card',
        description: 'Pay securely with your credit or debit card',
        enabled: true,
      },
      {
        id: 'cash_on_delivery',
        name: 'Cash on Delivery',
        description: 'Pay when you receive your order',
        enabled: true,
      },
    ],
  });
}); 